package com.cosfo.ordercenter.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishTaskParamDTO;
import com.cosfo.ordercenter.service.biz.OrderInnerService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderFinishTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderInnerService orderInnerService;

    private final static OrderAutoFinishTaskParamDTO TASK_DEFAULT_PARAM = new OrderAutoFinishTaskParamDTO(30, 3, 10);

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        String jobParameters = context.getJobParameters();
        String instanceParameters = context.getInstanceParameters();

        OrderAutoFinishTaskParamDTO paramDTO = TASK_DEFAULT_PARAM;

        if (!StringUtils.isEmpty(jobParameters)) {
            paramDTO = JSONObject.parseObject(jobParameters, OrderAutoFinishTaskParamDTO.class);
        }
        if (!StringUtils.isEmpty(instanceParameters)) {
            paramDTO = JSONObject.parseObject(instanceParameters, OrderAutoFinishTaskParamDTO.class);

        }
        if (paramDTO == null) {
            throw new ProviderException("订单自动完成任务参数配置错误");
        }
        if (paramDTO.getLimit() == null
                || paramDTO.getDeliveryDayStartOffset() == null
                || paramDTO.getDeliveryDayEndOffset() == null
                || paramDTO.getDeliveryDayStartOffset() <= paramDTO.getDeliveryDayEndOffset()) {
            throw new ProviderException("订单自动完成任务参数配置错误, param=" + paramDTO);
        }
        LocalDateTime now = LocalDateTime.now();
        OrderAutoFinishDTO autoFinishDTO = new OrderAutoFinishDTO();
        autoFinishDTO.setLimit(paramDTO.getLimit());
        autoFinishDTO.setDeliveryTimeStart(now.minusDays(paramDTO.getDeliveryDayStartOffset()));
        autoFinishDTO.setDeliveryTimeEnd(now.minusDays(paramDTO.getDeliveryDayEndOffset()));
        int successCnt = orderInnerService.autoFinish(autoFinishDTO);
        return new ProcessResult(true, "成功更新订单数量=" + successCnt);
    }
}
