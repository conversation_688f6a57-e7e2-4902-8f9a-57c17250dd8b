package com.cosfo.ordercenter.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderFinishTimeFixTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        String instanceParameters = context.getInstanceParameters();
        if (StringUtils.isEmpty(instanceParameters)) {
            log.warn("修复参数错误");
            return new ProcessResult(false);
        }

        List<Long> orderIds = Arrays.stream(instanceParameters.split(","))
                .map(String::trim) // 去除可能的空格
                .map(Long::valueOf)
                .collect(Collectors.toList());
        log.info("修复的订单id={}", orderIds);


        if (CollectionUtils.isEmpty(orderIds)) {
            log.warn("无需修复订单");
            return new ProcessResult(false);
        }
        OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
        afterSaleQueryReq.setOrderIds(orderIds);
        afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()));
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryList(afterSaleQueryReq);
        // 根据 orderId 分组， 获取 handleTime 最晚的一条记录
        Map<Long, Optional<OrderAfterSale>> handleTimeMap = orderAfterSales.stream().collect(Collectors.groupingBy(OrderAfterSale::getOrderId, Collectors.maxBy(Comparator.comparing(OrderAfterSale::getFinishedTime))));

        for (Long orderId : orderIds) {
            try {
                // 获取最后一次售后成功时间
                Optional<OrderAfterSale> orderAfterSaleOptional = handleTimeMap.get(orderId);
                if (!orderAfterSaleOptional.isPresent()) {
                    log.warn("订单[{}]无售后成功记录", orderId);
                    continue;
                }
                OrderAfterSale orderAfterSale = orderAfterSaleOptional.get();
                Order updateFinishTime = new Order();
                updateFinishTime.setFinishedTime(orderAfterSale.getFinishedTime());
                updateFinishTime.setId(orderId);
                orderDao.updateById(updateFinishTime);
            } catch (Exception ex) {
                log.error("订单完成时间修复异常, orderId=[{}]", orderId, ex);
            }

        }
        return new ProcessResult(true);
    }
}
