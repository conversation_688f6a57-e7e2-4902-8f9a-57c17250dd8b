package com.cosfo.ordercenter.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.ordercenter.dao.mapper.OrderAfterSaleMapper;
import com.cosfo.ordercenter.dao.mapper.OrderMapper;
import com.google.common.base.Stopwatch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单、售后单状态监控告警
 *
 * @author: xiaowk
 * @date: 2023/11/10 上午11:52
 */
@Component
@Slf4j
public class OrderStatusMonitorTask extends XianMuJavaProcessorV2 {

    @Resource
    private OrderAfterSaleMapper orderAfterSaleMapper;
    @Resource
    private OrderMapper orderMapper;

    private static ConditionDTO defaultConditionDTO = new ConditionDTO(90, 10, LocalDate.now().minusDays(1).atStartOfDay());

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("订单、售后单状态监控告警 task start, 参数：{}", context);

        ConditionDTO conditionDTO = defaultConditionDTO;

        String jobParameters = context.getJobParameters();
        String instanceParameters = context.getInstanceParameters();

        if (!StringUtils.isEmpty(jobParameters)) {
            conditionDTO = JSONObject.parseObject(jobParameters, ConditionDTO.class);
        }
        if (!StringUtils.isEmpty(instanceParameters)) {
            conditionDTO = JSONObject.parseObject(instanceParameters, ConditionDTO.class);
        }

        if (conditionDTO == null) {
            throw new ProviderException("任务参数配置错误");
        }

        if (conditionDTO.getScanDays() == null) {
            conditionDTO.setScanDays(defaultConditionDTO.getScanDays());
        }
        if (conditionDTO.getTimeoutMinutes() == null) {
            conditionDTO.setTimeoutMinutes(defaultConditionDTO.getTimeoutMinutes());
        }
        if (conditionDTO.getOrderStartTime() == null) {
            conditionDTO.setOrderStartTime(defaultConditionDTO.getOrderStartTime());
        }


        // 售后单同步履约告警
        handleAfterSaleOrder(conditionDTO);

        // 订单同步履约告警
        handleOrder(conditionDTO);

        log.info("订单、售后单状态监控告警 task end, spent={}", stopwatch.stop());
        return new ProcessResult(true);
    }

    private void handleAfterSaleOrder(ConditionDTO conditionDTO){
        Integer timeoutMinutes = conditionDTO.getTimeoutMinutes();

        LocalDateTime startTime = LocalDate.now().minusDays(conditionDTO.getScanDays()).atStartOfDay();
        LocalDateTime endTime = LocalDateTime.now().minusMinutes(timeoutMinutes);

        List<String> aftersaleNos = orderAfterSaleMapper.getAllRefundDealNoByTime(startTime, endTime);
        if (!CollectionUtils.isEmpty(aftersaleNos)) {
            String errMsg = String.format("发现存在%s条售后单记录%s分钟后仍然在【2-处理中】状态，请及时关注处理以下售后单%s", aftersaleNos.size(), timeoutMinutes, aftersaleNos.subList(0, Math.min(aftersaleNos.size(), 10)));
            log.error(errMsg, new BizException(errMsg));
        }
    }

    private void handleOrder(ConditionDTO conditionDTO){
        Integer timeoutMinutes = conditionDTO.getTimeoutMinutes();

        LocalDateTime startTime = LocalDate.now().minusDays(1).atStartOfDay();
        if(conditionDTO.getOrderStartTime() != null && startTime.isBefore(conditionDTO.getOrderStartTime())){
            startTime = conditionDTO.getOrderStartTime();
        }
        LocalDateTime endTime = LocalDateTime.now().minusMinutes(timeoutMinutes);
        List<String> orderNos = orderMapper.getWaitFulfillmentOrderNoByTime(startTime, endTime);
        if (!CollectionUtils.isEmpty(orderNos)) {
            String errMsg = String.format("发现存在%s条订单记录%s分钟后仍未收到创建履约单成功回告消息，请及时关注处理以下订单%s", orderNos.size(), timeoutMinutes, orderNos);
            log.error(errMsg, new BizException(errMsg));
        }
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ConditionDTO implements Serializable {

        /**
         * 扫描售后单记录天数 默认90天
         */
        private Integer scanDays;

        /**
         * 告警超时时间(分钟) 默认10分钟
         */
        private Integer timeoutMinutes;

        /**
         * 订单扫描起始时间
         */
        private LocalDateTime orderStartTime;

    }
}
