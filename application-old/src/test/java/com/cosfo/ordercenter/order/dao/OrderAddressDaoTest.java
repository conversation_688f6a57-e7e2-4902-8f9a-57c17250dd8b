package com.cosfo.ordercenter.order.dao;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 *
 * @author: xiaowk
 * @date: 2023/8/20 下午12:04
 */
@SpringBootTest
public class OrderAddressDaoTest {
    @Resource
    private OrderAddressDao orderAddressDao;

    @Test
    void getByOrderId() {
        OrderAddress orderAddress = orderAddressDao.getByOrderId(87370L, 2L);
        System.err.println(JSON.toJSONString(orderAddress));
    }

    @Test
    void saveAddressTest() {
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setId(0L);
        orderAddress.setTenantId(0L);
        orderAddress.setOrderId(0L);
        orderAddress.setContactName("test");
        orderAddress.setContactPhone("test");
        orderAddress.setProvince("test");
        orderAddress.setCity("test");
        orderAddress.setArea("test");
        orderAddress.setAddress("test");
        orderAddress.setPoiNote("test");
        Long add = orderAddressDao.add(orderAddress);
        System.out.println(add);
    }
}
