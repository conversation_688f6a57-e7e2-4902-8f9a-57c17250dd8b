package com.cosfo.ordercenter.order.dao;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryUpdateParam;
import com.cosfo.ordercenter.dao.model.param.OrderQueryParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.service.constant.DeliveryConstant;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderDaoTest {

    @Resource
    private OrderDao orderDao;

    @Test
    void selectTest() {
        Order order = orderDao.getBaseMapper().selectById(6L);
        System.out.println(JSON.toJSONString(order));
    }

    @Test
    void queryListTest() {
        OrderQueryReq queryParam = new OrderQueryReq();
        queryParam.setTenantId(2L);
        queryParam.setStatus(1);
        List<Order> orders = orderDao.queryList(queryParam);
        System.out.println(orders.size());
    }

    @Test
    void queryListDeliveryTest() {
        List<Order> dailyOrderList = orderDao.queryList(OrderQueryReq.builder()
                .tenantId(2L)
                .storeIds(Lists.newArrayList(4307L))
                .deliveryStartTime(LocalDate.parse("2023-08-25").atStartOfDay())
                .deliveryEndTime(LocalDate.parse("2023-08-25").atStartOfDay().with(LocalTime.MAX))
                .statusList(DeliveryConstant.DELIVERY_EFFECTIVE_STATUS)
                .warehouseType(1)
                .build());
        System.out.println(dailyOrderList.size());
    }

    @Test
    void queryListWithOrderItem() {
        List<Order> orders = orderDao.queryList(OrderQueryReq.builder().orderItemIds(Lists.newArrayList(191230L)).build());
        System.out.println(JSON.toJSONString(orders));
    }

    @Test
    void paySuccessUpdateStatus() {
        OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
        updateReq.setOrderId(87373L);
        updateReq.setStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
        orderDao.updateStatus(updateReq);
    }

    @Test
    void updateStatusWithFinishTimeExist() {
        OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
        updateReq.setOrderId(100508L);
        updateReq.setStatus(OrderStatusEnum.FINISHED.getCode());
        orderDao.updateStatus(updateReq);
    }

    @Test
    void updateDeliveryTime() {
        OrderDeliveryUpdateParam updateParam = new OrderDeliveryUpdateParam();
        updateParam.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_AUDIT.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode()));
        updateParam.setOrderId(100022L);
        updateParam.setDeliveryTime(null);
        updateParam.setFulfillmentNo(99999999L);
        boolean result = orderDao.updateDeliveryTime(updateParam);
    }

    @Test
    void orderFinishTest() {
        int i = orderDao.orderFinish(Lists.newArrayList(89693L));
    }

    @Test
    void queryNeedFinishOrderTest() {
        OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
        orderAutoFinishDTO.setLimit(10);
        orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(10));
        orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
        List<Order> orders = orderDao.queryNeedAutoFinishedOrder(orderAutoFinishDTO);
        System.out.println(orders.stream().map(Order::getId).collect(Collectors.toList()));
    }
}