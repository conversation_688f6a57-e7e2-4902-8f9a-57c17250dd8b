package com.cosfo.ordercenter.order.dao;

import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.google.common.base.Stopwatch;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderItemDaoTest {
    @Resource
    private OrderItemDao orderItemDao;
    @Test
    void queryDetailTest() {

    }

    @Test
    void updateDeliveryTimeTest() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<Long> itemIds = Lists.newArrayList(71148L, 71147L, 71146L, 71145L, 71136L, 71132L, 71131L, 68282L, 68281L, 68056L, 68055L, 67677L, 67653L, 67630L, 67498L, 67497L, 67496L, 67436L, 67416L, 67153L);
        for (Long itemId : itemIds) {
            OrderItemUpdateReq req = new OrderItemUpdateReq();
            req.setOrderItemId(itemId);
            req.setAfterSaleExpiryTime(LocalDateTime.now());
            orderItemDao.updateAfterSaleExpiryTime(req);
        }
        System.out.println(stopwatch.stop());
    }

    @Test
    void batchUpdateTest() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        List<Long> itemIds = Lists.newArrayList(71148L, 71147L, 71146L, 71145L, 71136L, 71132L, 71131L, 68282L, 68281L, 68056L, 68055L, 67677L, 67653L, 67630L, 67498L, 67497L, 67496L, 67436L, 67416L, 67153L);
        List<OrderItem> updateList = new ArrayList<>();
        for (Long itemId : itemIds) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(itemId);
            orderItem.setAfterSaleExpiryTime(LocalDateTime.now());
            updateList.add(orderItem);
        }
        orderItemDao.updateBatchById(updateList);
        System.out.println(stopwatch.stop());
    }
}
