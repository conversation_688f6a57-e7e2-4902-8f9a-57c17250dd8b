package com.cosfo.ordercenter.order.dao;

import com.cosfo.ordercenter.dao.dao.OrderItemExtraDao;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class OrderItemExtraDaoImplTest {

    @Resource
    private OrderItemExtraDao orderItemExtraDao;

    @Test
    void batchSave() {
        List<OrderItemExtra> orderItemExtraList = Lists.newArrayList();
        OrderItemExtra orderItemExtra1 = new OrderItemExtra();
        orderItemExtra1.setTenantId(2L);
        orderItemExtra1.setOrderId(111L);
        orderItemExtra1.setOrderItemId(0L);
        orderItemExtra1.setCustomerOrderItemId("ddd");
        orderItemExtra1.setCustomerSkuCode("ddd");
        orderItemExtra1.setSkuCode("ddd");
        orderItemExtra1.setCustomerSkuTitle("dddaa");
        orderItemExtra1.setCustomerSkuSpecification("aaaff");
        orderItemExtraList.add(orderItemExtra1);
        OrderItemExtra orderItemExtra2 = new OrderItemExtra();
        orderItemExtra2.setTenantId(2L);
        orderItemExtra2.setOrderId(111L);
        orderItemExtra2.setOrderItemId(111L);
        orderItemExtra2.setCustomerOrderItemId("ddd");
        orderItemExtra2.setCustomerSkuCode("ddd");
        orderItemExtra2.setSkuCode("dddjjj");
        orderItemExtra2.setCustomerSkuTitle("dddaa");
        orderItemExtra2.setCustomerSkuSpecification("aaaff");
        orderItemExtraList.add(orderItemExtra2);

        orderItemExtraDao.batchSave(orderItemExtraList);
    }
}