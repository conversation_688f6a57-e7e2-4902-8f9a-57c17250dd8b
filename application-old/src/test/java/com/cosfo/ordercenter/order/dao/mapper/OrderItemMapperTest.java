package com.cosfo.ordercenter.order.dao.mapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.cosfo.ordercenter.dao.mapper.OrderItemMapper;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemMapperTest {

    @Resource
    private OrderItemMapper orderItemMapper;

    @Test
    void batchQueryOrderItemDetail() {
        List<OrderItemWithSnapshot> orderItemWithSnapshots = orderItemMapper.batchQueryOrderItemDetail(Lists.newArrayList(60984L), Lists.newArrayList());
        System.out.println(orderItemWithSnapshots);
    }

    @Test
    void querySkuQuantity() {
        Integer skuQuantity = orderItemMapper.querySkuQuantity(2L, Lists.newArrayList(6L));
        System.out.println(skuQuantity);
    }

    @Test
    void querySaleQuantity() {
        Integer skuQuantity = orderItemMapper.querySkuQuantity(2L, Lists.newArrayList(6L));
        System.out.println(skuQuantity);
    }

    @Test
    void updateDeliveryQuantity() {
    }

    @Test
    void batchSaveTest() {
        OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0"));
        orderItem.setTotalPrice(new BigDecimal("0"));
        orderItem.setStoreNo(0);
        orderItem.setStatus(0);
        orderItem.setCreateTime(LocalDateTime.now());
        orderItem.setUpdateTime(LocalDateTime.now());
        orderItem.setAfterSaleExpiryTime(LocalDateTime.now());
        orderItem.setOrderType(0);
        orderItem.setDeliveryQuantity(0);

        orderItemMapper.batchSave(Lists.newArrayList(orderItem));
    }



}