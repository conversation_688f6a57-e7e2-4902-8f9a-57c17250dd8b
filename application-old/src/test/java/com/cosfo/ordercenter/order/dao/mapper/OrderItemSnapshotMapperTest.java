package com.cosfo.ordercenter.order.dao.mapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.cosfo.ordercenter.dao.mapper.OrderItemSnapshotMapper;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderItemSnapshotMapperTest {

    @Resource
    private OrderItemSnapshotMapper orderItemSnapshotMapper;

    @Test
    void batchSaveTest() {
        OrderItemSnapshot snapshot = new OrderItemSnapshot();
        snapshot.setId(0L);
        snapshot.setTenantId(0L);
        snapshot.setOrderItemId(0L);
        snapshot.setSkuId(0L);
        snapshot.setSupplierTenantId(0L);
        snapshot.setSupplierSkuId(0L);
        snapshot.setAreaItemId(0L);
        snapshot.setTitle("");
        snapshot.setMainPicture("");
        snapshot.setSpecificationUnit("");
        snapshot.setSpecification("");
        snapshot.setCreateTime(LocalDateTime.now());
        snapshot.setUpdateTime(LocalDateTime.now());
        snapshot.setSupplyPrice(new BigDecimal("0"));
        snapshot.setWarehouseType(0);
        snapshot.setDeliveryType(0);
        snapshot.setSupplierName("");
        snapshot.setMaxAfterSaleAmount(0);
        snapshot.setAfterSaleUnit("");
        snapshot.setPricingType(0);
        snapshot.setPricingNumber(new BigDecimal("0"));
        snapshot.setAfterSaleRule("");
        snapshot.setGoodsType(0);
        snapshot.setOrderId(0L);


        orderItemSnapshotMapper.batchSave(Lists.newArrayList(snapshot));
    }

    @Test
    void queryDetailById() {
    }
}
