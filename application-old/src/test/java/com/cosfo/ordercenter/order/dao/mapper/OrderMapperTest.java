package com.cosfo.ordercenter.order.dao.mapper;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.dao.mapper.OrderMapper;
import com.cosfo.ordercenter.dao.model.po.OrderItemSaleQuantity;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderMapperTest {

    @Resource
    private OrderMapper orderMapper;

    @Test
    void batchUpdateStatusByOrderIds() {

        Integer i = orderMapper.batchUpdateStatusByOrderIds(Lists.newArrayList(89693L), Lists.newArrayList(4), 5);
        assertEquals(1, i);
    }

    @Test
    void queryOrderItemSaleQuantityTest() {

        List<OrderItemSaleQuantity> orderItemSaleQuantities = orderMapper.queryOrderItemSaleQuantity(LocalDateTimeUtil.beginOfDay(LocalDateTime.now().plusDays(-7)),
                LocalDateTime.now(),
                2L,
                142087L,
                Lists.newArrayList(22248L),
                Arrays.asList(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
                        OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                        OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
                        OrderAfterSaleStatusEnum.WAIT_REFUND.getValue()),
                Arrays.asList(OrderStatusEnum.NO_PAYMENT.getCode(),
                        OrderStatusEnum.WAIT_DELIVERY.getCode(),
                        OrderStatusEnum.DELIVERING.getCode(),
                        OrderStatusEnum.FINISHED.getCode(),
                        OrderStatusEnum.WAITING_DELIVERY.getCode(),
                        OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(),
                        OrderStatusEnum.OUT_OF_STORAGE.getCode())
                );
        System.out.println(JSON.toJSONString(orderItemSaleQuantities));
    }
}