package com.cosfo.ordercenter.service;

import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.service.biz.OrderInnerService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.concurrent.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderInnerServiceTest {

    @Resource
    private OrderInnerService orderInnerService;


    // select * from `order` where delivery_time < "2023-09-05 11:01:25" and delivery_time > "2023-09-04 11:01:25" and status = 5 and tenant_id = 2;

        // UPDATE`order` SET status=4 WHERE delivery_time<"2023-09-05 11:01:25" AND delivery_time>"2023-09-04 11:01:25" AND status=5 AND tenant_id=2;
    @Test
    void autoFinish() {
        OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
        orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(13));
        orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
        orderAutoFinishDTO.setLimit(10);
        orderInnerService.autoFinish(orderAutoFinishDTO);
    }

    @Test
    void parallelExecTest() throws InterruptedException {
        ExecutorService executorService = new ThreadPoolExecutor(3,3,1,TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), new ThreadFactoryBuilder().setNameFormat("task-%d").build());
        for (int i = 0; i < 3; i++) {
            executorService.submit(()->{
                OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
                orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(13));
                orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
                orderAutoFinishDTO.setLimit(10);
                orderInnerService.autoFinish(orderAutoFinishDTO);
            });
        }
        executorService.awaitTermination(30, TimeUnit.SECONDS);
    }

    @Test
    void parallelExecTest2() throws InterruptedException {
        ExecutorService executorService = new ThreadPoolExecutor(3,3,1,TimeUnit.SECONDS, new ArrayBlockingQueue<>(10), new ThreadFactoryBuilder().setNameFormat("task-%d").build());
        executorService.submit(()->{
            OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
            orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(13));
            orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
            orderAutoFinishDTO.setLimit(10);
            orderInnerService.autoFinish(orderAutoFinishDTO);
        });
        Thread.sleep(2000);
        executorService.submit(()->{
            OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
            orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(13));
            orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
            orderAutoFinishDTO.setLimit(10);
            orderInnerService.autoFinish(orderAutoFinishDTO);
        });
        Thread.sleep(5000);
        executorService.submit(()->{
            OrderAutoFinishDTO orderAutoFinishDTO = new OrderAutoFinishDTO();
            orderAutoFinishDTO.setDeliveryTimeStart(LocalDateTime.now().minusDays(13));
            orderAutoFinishDTO.setDeliveryTimeEnd(LocalDateTime.now().minusDays(1));
            orderAutoFinishDTO.setLimit(10);
            orderInnerService.autoFinish(orderAutoFinishDTO);
        });
        executorService.awaitTermination(30, TimeUnit.SECONDS);
    }
}