package com.cosfo.ordercenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderQueryProviderImplTest {

    @Resource
    private OrderQueryService orderQueryService;

    @Test
    void queryById() {
        DubboResponse<OrderDTO> response = orderQueryService.queryById(6L);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void queryByIds() {
    }

    @Test
    void queryOrderList() {
        OrderQueryReq queryDTO = OrderQueryReq.builder().maxId(10L).build();
        List<OrderDTO> data = new ArrayList<>();
        do {
            queryDTO.setMaxId(data.stream().map(OrderDTO::getId).max(Long::compareTo).orElse(null));
            DubboResponse<List<OrderDTO>> response = orderQueryService.queryOrderList(queryDTO);
            if (!response.isSuccess()) {
                throw new ProviderException(response.getMsg());
            }
            data = response.getData();
            System.out.println(JSON.toJSONString(response));
        } while (!data.isEmpty());
    }

    @Test
    void queryOrderPage() {
        OrderQueryReq queryDTO = OrderQueryReq.builder().build();
        queryDTO.setPageSize(10);
        queryDTO.setPageNum(1);
        queryDTO.setItemIds(Lists.newArrayList(21249L));
        DubboResponse<PageInfo<OrderDTO>> response = orderQueryService.queryOrderPage(queryDTO);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void countOrderQuantity() {
    }

    @Test
    void sumOrderAmount() {
    }

    @Test
    void querySameDayOrder() {
        OrderQueryReq req = JSONObject.parseObject("{\"deliveryTime\":\"2023-08-27\",\"neOrderId\":89178,\"pageNum\":1,\"pageSize\":10,\"statusList\":[3,4,5,10],\"storeIds\":[4347],\"supplierTenantId\":1,\"tenantId\":2}", OrderQueryReq.class);
        List<OrderDTO> orderList = RpcResultUtil.handle(orderQueryService.queryOrderList(req));
        System.out.println(JSON.toJSONString(orderList));
    }

    @Test
    void queryOrderPageBySupplierIds() {
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setTenantId(2L);
        orderQueryReq.setPageNum(1);
        orderQueryReq.setPageSize(10);
        orderQueryReq.setSupplierTenantIds(Lists.newArrayList(3294L));
        PageInfo<OrderDTO> handle = RpcResultUtil.handle(orderQueryService.queryOrderPage(orderQueryReq));
        System.out.println(JSON.toJSONString(handle));
    }
}