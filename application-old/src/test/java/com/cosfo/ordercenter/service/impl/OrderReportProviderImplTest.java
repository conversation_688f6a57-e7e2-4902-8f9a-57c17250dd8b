package com.cosfo.ordercenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.req.ItemSaleQuantityReq;
import com.cosfo.ordercenter.client.req.OrderDetailReq;
import com.cosfo.ordercenter.client.req.OrderSkuSaleReq;
import com.cosfo.ordercenter.client.req.OrderSummaryReq;
import com.cosfo.ordercenter.client.resp.OrderDetailDTO;
import com.cosfo.ordercenter.client.resp.OrderSkuQuantityDTO;
import com.cosfo.ordercenter.client.resp.OrderSummaryDTO;
import com.cosfo.ordercenter.client.service.OrderReportService;
import com.cosfo.ordercenter.service.provider.order.OrderReportProviderImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderReportProviderImplTest {

    @Resource
    private OrderReportProviderImpl orderReportProvider;
    @DubboReference
    private OrderReportService orderReportService;

    @Test
    void querySummary() {
        OrderSummaryReq req = new OrderSummaryReq();
        req.setTenantId(2L);
        req.setStartTime(LocalDateTime.parse("2023-06-13 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        req.setEndTime(LocalDateTime.parse("2023-06-13 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        req.setStoreIds(Lists.newArrayList(1L, 132L, 1542L, 133L, 1544L, 1550L, 1551L, 134L, 1558L, 1559L, 1560L, 1566L, 1568L, 138L, 1575L, 1576L, 1577L, 141L, 1584L, 1586L, 1590L, 1593L, 144L, 1597L, 1604L, 1606L, 1607L, 1613L, 1615L, 1619L, 1623L, 1626L, 1627L, 1628L, 1629L, 1633L, 18L, 1637L, 1639L, 1640L, 1641L, 1649L, 1650L, 1652L, 1654L, 1662L, 1664L, 1667L, 151L, 1676L, 1677L, 1678L, 1680L, 1687L, 153L, 1690L, 1696L, 1701L, 1702L, 1703L, 1704L, 1707L, 1716L, 1717L, 156L, 1720L, 1721L, 1722L, 1724L, 1727L, 1729L, 1730L, 1731L, 1735L, 1740L, 1743L, 1746L, 159L, 1748L, 1752L, 1753L, 1758L, 1761L, 1764L, 4509L, 1773L, 1777L, 162L, 1779L, 1782L, 1789L, 1793L, 1800L, 1801L, 1802L, 1805L, 1807L, 165L, 1815L, 1819L, 1824L, 1825L, 167L, 1830L, 1831L, 1832L, 1833L, 1834L, 1835L, 1837L, 1842L, 175L, 1851L, 1858L, 1859L, 1861L, 1862L, 1865L, 1867L, 178L, 1868L, 1869L, 1870L, 1872L, 1873L, 1876L, 1877L, 179L, 1878L, 1879L, 1883L, 1884L, 1887L, 1897L, 1898L, 1901L, 1903L, 1905L, 182L, 1908L, 1909L, 1913L, 1917L, 1918L, 1921L, 1922L, 1926L, 184L, 1932L, 1934L, 1936L, 22L, 1938L, 1940L, 1942L, 1945L, 186L, 1949L, 1950L, 1958L, 188L, 1973L, 1979L, 1981L, 190L, 1994L, 1995L, 1996L, 2007L, 2008L, 2012L, 2018L, 2021L, 2026L, 194L, 2028L, 2030L, 2033L, 2034L, 2037L, 2039L, 2040L, 2041L, 2043L, 2044L, 2045L, 2046L, 196L, 2050L, 2051L, 2056L, 2058L, 2059L, 2065L, 2067L, 2068L, 2069L, 2071L, 2072L, 2073L, 2074L, 2077L, 2079L, 2082L, 2084L, 2086L, 2088L, 2093L, 2097L, 2101L, 2103L, 2110L, 2111L, 2112L, 2113L, 2115L, 2117L, 2122L, 2125L, 2129L, 2132L, 2144L, 2622L, 2623L, 2624L, 206L, 2628L, 2629L, 2630L, 2631L, 2632L, 2635L, 2639L, 2640L, 2646L, 2648L, 2649L, 2650L, 2661L, 2663L, 2665L, 2666L, 2668L, 2671L, 2672L, 2679L, 2680L, 2683L, 2686L, 2687L, 2689L, 2690L, 2693L, 2698L, 2701L, 2703L, 2704L, 2706L, 2711L, 2712L, 2714L, 2723L, 2724L, 2725L, 2726L, 2729L, 2730L, 215L, 2736L, 2743L, 2746L, 2749L, 2751L, 2752L, 2757L, 2764L, 2765L, 2768L, 2769L, 2771L, 2772L, 219L, 2773L, 2775L, 2779L, 2781L, 2782L, 2784L, 2789L, 2791L, 2793L, 2794L, 2795L, 2796L, 2811L, 2820L, 2821L, 2825L, 2826L, 2828L, 2831L, 2836L, 2838L, 2839L, 2841L, 2846L, 2847L, 2850L, 2851L, 2852L, 2853L, 2854L, 2856L, 2858L, 2862L, 228L, 2864L, 229L, 2873L, 2874L, 2876L, 2892L, 2893L, 2896L, 2898L, 2907L, 2909L, 233L, 2916L, 2921L, 2922L, 2925L, 2926L, 2928L, 2929L, 2932L, 235L, 2934L, 2935L, 2937L, 2938L, 2940L, 2943L, 2946L, 2949L, 2950L, 2954L, 2960L, 238L, 2963L, 2964L, 2968L, 2970L, 2971L, 2973L, 2974L, 2975L, 2977L, 2989L, 2991L, 241L, 2995L, 3002L, 242L, 3004L, 3006L, 3009L, 3011L, 3013L, 3014L, 3020L, 3021L, 3028L, 3029L, 3033L, 3046L, 247L, 4084L, 4487L, 4499L, 4482L, 3062L, 3075L, 3077L, 3078L, 3081L, 3082L, 3089L, 3090L, 3092L, 251L, 3094L, 3096L, 3099L, 3104L, 3110L, 3111L, 3115L, 3117L, 3120L, 254L, 3128L, 3132L, 3133L, 3137L, 3138L, 3141L, 3142L, 3151L, 257L, 3155L, 3158L, 3159L, 3168L, 3171L, 3176L, 3177L, 3178L, 259L, 3194L, 3196L, 3197L, 3200L, 3201L, 3202L, 3205L, 3207L, 3208L, 3209L, 3210L, 3212L, 3213L, 3215L, 3217L, 3220L, 3222L, 3224L, 3225L, 3228L, 3232L, 3238L, 3239L, 3243L, 3244L, 3245L, 3246L, 3247L, 3248L, 3249L, 3250L, 3251L, 3258L, 3259L, 264L, 3260L, 3264L, 3290L, 266L, 3295L, 3296L, 3301L, 3302L, 3303L, 267L, 3304L, 3306L, 3308L, 3310L, 3341L, 3342L, 3346L, 3352L, 3375L, 3376L, 3381L, 3389L, 3412L, 3419L, 3475L, 3484L, 3977L, 3979L, 3980L, 3981L, 3988L, 4013L, 4015L, 4016L, 4018L, 271L, 4021L, 4023L, 4030L, 4049L, 4064L, 4065L, 3464L, 4079L, 4080L, 4081L, 4082L, 4086L, 4087L, 4090L, 4097L, 4115L, 4117L, 4119L, 4120L, 4121L, 4122L, 4125L, 4126L, 275L, 4140L, 4184L, 4185L, 4204L, 4205L, 4216L, 4253L, 4260L, 4264L, 4286L, 4293L, 4296L, 4298L, 4310L, 4313L, 4315L, 278L, 4324L, 4325L, 4333L, 4334L, 279L, 4339L, 4341L, 4139L, 4342L, 4343L, 4345L, 4346L, 280L, 4347L, 4467L, 4470L, 4473L, 4474L, 281L, 4488L, 4492L, 4503L, 284L, 288L, 290L, 294L, 297L, 298L, 300L, 304L, 309L, 310L, 317L, 319L, 320L, 321L, 322L, 329L, 330L, 331L, 334L, 336L, 344L, 346L, 349L, 353L, 355L, 4477L, 360L, 365L, 366L, 367L, 370L, 371L, 374L, 377L, 380L, 382L, 384L, 385L, 387L, 388L, 391L, 394L, 396L, 397L, 398L, 405L, 410L, 411L, 412L, 413L, 418L, 421L, 423L, 424L, 47L, 426L, 428L, 429L, 431L, 435L, 436L, 438L, 439L, 440L, 451L, 455L, 459L, 469L, 470L, 478L, 481L, 486L, 491L, 497L, 498L, 503L, 504L, 507L, 513L, 521L, 522L, 532L, 533L, 534L, 535L, 536L, 540L, 542L, 62L, 547L, 548L, 555L, 560L, 563L, 564L, 566L, 576L, 579L, 580L, 582L, 583L, 585L, 592L, 594L, 596L, 599L, 600L, 602L, 603L, 604L, 72L, 612L, 615L, 617L, 622L, 624L, 631L, 634L, 75L, 637L, 638L, 640L, 648L, 650L, 651L, 655L, 656L, 660L, 661L, 662L, 664L, 668L, 675L, 677L, 680L, 682L, 80L, 1181L, 1183L, 1186L, 1187L, 1188L, 1189L, 1191L, 1192L, 1195L, 1200L, 1204L, 1206L, 1208L, 1209L, 1212L, 1213L, 1214L, 1217L, 1219L, 1220L, 1222L, 86L, 1228L, 1229L, 1230L, 1236L, 1243L, 88L, 1245L, 1247L, 1249L, 89L, 1256L, 1261L, 1262L, 91L, 1265L, 1268L, 1269L, 1270L, 1277L, 1278L, 1289L, 94L, 1299L, 1301L, 96L, 1307L, 1310L, 1311L, 1314L, 1316L, 1317L, 1323L, 1325L, 1327L, 1331L, 1332L, 111L, 1336L, 1337L, 1338L, 1340L, 1341L, 1342L, 1343L, 1347L, 1350L, 1353L, 1361L, 1362L, 114L, 1367L, 1368L, 1369L, 1371L, 115L, 1376L, 1377L, 1383L, 116L, 4495L, 4496L, 1389L, 1391L, 117L, 1394L, 1398L, 118L, 1407L, 1411L, 1412L, 1415L, 1417L, 1420L, 1422L, 1426L, 1431L, 1432L, 1433L, 16L, 1441L, 1443L, 1447L, 1450L, 1452L, 124L, 1473L, 125L, 1474L, 1475L, 1479L, 1481L, 1482L, 1484L, 1485L, 1493L, 127L, 1494L, 1498L, 1501L, 128L, 1508L, 1510L, 1512L, 1514L, 1518L, 1522L, 131L, 1525L, 1526L, 1529L, 1530L, 4508L, 4308L, 4427L));
        DubboResponse<OrderSummaryDTO> orderConditionDTODubboResponse = orderReportProvider.queryOrderSummary(req);
        System.out.println(JSON.toJSONString(orderConditionDTODubboResponse));
    }

    @Test
    void querySkuSaleQuantity() {
        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setSkuIds(Lists.newArrayList(18722L));
        orderSkuSaleReq.setStartTime(LocalDateTime.parse("2023-06-26 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setEndTime(LocalDateTime.parse("2023-06-26 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setTenantId(2L);
        DubboResponse<List<OrderSkuQuantityDTO>> listDubboResponse = orderReportProvider.querySkuSaleQuantity(orderSkuSaleReq);
        System.out.println(listDubboResponse.getData());
    }

    @Test
    void querySkuSaleWithStoreNoQuantity() {
        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setSkuIds(Lists.newArrayList(18722L));
        orderSkuSaleReq.setStartTime(LocalDateTime.parse("2023-06-26 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setEndTime(LocalDateTime.parse("2023-06-26 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setTenantId(2L);
        DubboResponse<List<OrderSkuQuantityDTO>> listDubboResponse = orderReportProvider.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq);
        System.out.println(listDubboResponse.getData());
    }

    @Test
    void querySkuSaleWithCityQuantity() {
        OrderSkuSaleReq orderSkuSaleReq = new OrderSkuSaleReq();
        orderSkuSaleReq.setSkuIds(Lists.newArrayList(18722L));
        orderSkuSaleReq.setStartTime(LocalDateTime.parse("2023-06-26 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setEndTime(LocalDateTime.parse("2023-06-26 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderSkuSaleReq.setTenantId(2L);
        DubboResponse<List<OrderSkuQuantityDTO>> listDubboResponse = orderReportProvider.querySkuSaleWithCityQuantity(orderSkuSaleReq);
        System.out.println(listDubboResponse.getData());
    }

    @Test
    void getWaitDeliveryQuantity() {
        DubboResponse<Integer> waitDeliveryQuantity = orderReportProvider.getWaitDeliveryQuantity(2L);
        System.out.println(waitDeliveryQuantity.getData());
    }

    @Test
    void queryOrderDetail() {
        OrderDetailReq req  = new OrderDetailReq();
        req.setOrderIds(Lists.newArrayList(67715L));
        req.setTenantId(2L);
        DubboResponse<List<OrderDetailDTO>> listDubboResponse = orderReportProvider.queryOrderDetail(req);
        System.out.println(listDubboResponse.getData());
    }

    @Test
    void countItemSaleQuantityTest() {
        ItemSaleQuantityReq req = new ItemSaleQuantityReq();
        req.setTenantId(2L);
        req.setMerchantStoreId(142087L);
        req.setItemIds(Lists.newArrayList(22248L));
        req.setStartDay(LocalDateTime.now().minusDays(7));
        req.setEndDay(LocalDateTime.now());

        DubboResponse<Map<Long, Integer>> mapDubboResponse = orderReportProvider.countItemSaleQuantity(req);
        System.out.println(JSON.toJSONString(mapDubboResponse));
    }

    @Test
    void countItemSaleQuantityRpcParamCheckTest() {
        ItemSaleQuantityReq req = new ItemSaleQuantityReq();
//        req.setTenantId(2L);
        req.setMerchantStoreId(142087L);
        req.setItemIds(Lists.newArrayList(22248L));
        req.setStartDay(LocalDateTime.now().minusDays(7));
        req.setEndDay(LocalDateTime.now());
        DubboResponse<Map<Long, Integer>> mapDubboResponse = orderReportService.countItemSaleQuantity(req);
        System.out.println(JSON.toJSONString(mapDubboResponse));
    }
}