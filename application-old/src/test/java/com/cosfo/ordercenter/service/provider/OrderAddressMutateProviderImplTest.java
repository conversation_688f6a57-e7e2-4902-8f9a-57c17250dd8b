package com.cosfo.ordercenter.service.provider;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.service.OrderAddressMutateService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAddressMutateProviderImplTest {

    @Resource
    private OrderAddressMutateService orderAddressMutateService;

    @Test
    void add() {
        OrderAddressDTO dto = JSONObject.parseObject("{\"address\":\"新杭商务中心1234\",\"area\":\"西湖区\",\"city\":\"杭州市\",\"contactName\":\"不吃香菜\",\"contactPhone\":\"18334345453\",\"id\":4137,\"poiNote\":\"120.05842440953303,30.280235188569886\",\"province\":\"浙江省\",\"tenantId\":2}", OrderAddressDTO.class);

        orderAddressMutateService.add(dto);
    }
}