package com.cosfo.ordercenter.service.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.service.OrderAddressQueryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAddressQueryProviderImplTest {


    @Resource
    private OrderAddressQueryService orderAddressQueryService;
    @Test
    void queryByOrderId() {
        DubboResponse<OrderAddressDTO> orderAddressDTODubboResponse = orderAddressQueryService.queryByOrderId(2L,87377L);
        System.out.println(JSON.toJSONString(orderAddressDTODubboResponse));
        assertTrue(orderAddressDTODubboResponse.isSuccess());
//        assertTrue(orderAddressDTODubboResponse.getData().getAddress().equals("新杭商务中心"));

    }
}