package com.cosfo.ordercenter.service.provider;

import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateStoreNoReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.service.provider.aftersale.OrderAfterSaleMutateProviderImpl;
import com.google.common.base.Stopwatch;
import net.xianmu.common.result.DubboResponse;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAfterSaleMutateProviderImplTest {

    @Resource
    private OrderAfterSaleMutateProviderImpl orderAfterSaleMutateService;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderDao orderDao;

    @Test
    void createAfterDeliveryAfterSale() {
    }

    @Test
    void createPreDeliveryAfterSale() {
        OrderAfterSaleDTO orderAfterSaleDTO = new OrderAfterSaleDTO();
        orderAfterSaleDTO.setOrderId(98726L);
        orderAfterSaleDTO.setOrderItemId(254873L);
        orderAfterSaleDTO.setAmount(3);
        orderAfterSaleDTO.setAfterSaleType(1);
        orderAfterSaleDTO.setServiceType(1);
        orderAfterSaleDTO.setApplyPrice(new BigDecimal("60"));
//        orderAfterSaleDTO.setTotalPrice(new BigDecimal("0.1"));
        orderAfterSaleDTO.setReason("拍多/拍错/不想要");
        orderAfterSaleDTO.setWarehouseType(2);
        orderAfterSaleMutateService.createPreDeliveryAfterSale(Lists.newArrayList(orderAfterSaleDTO));
    }

    @Test
    void cancel() {
    }

    @Test
    void updateStatus() {
        OrderAfterSaleStatusUpdateReq updateReq = new OrderAfterSaleStatusUpdateReq();
        updateReq.setAfterSaleId(1L);
        updateReq.setTargetStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
        orderAfterSaleMutateService.updateStatus(updateReq);
    }

    @Test
    void updateByIdWithNullObj() {
        OrderAfterSaleDTO afterSaleDTO = new OrderAfterSaleDTO();
        afterSaleDTO.setId(1L);
        Boolean result = RpcResultUtil.handle(orderAfterSaleMutateService.updateById(afterSaleDTO));
        System.out.println(result);
    }

    @Test
    void updateByIdSuccess() {
        OrderAfterSaleDTO afterSaleDTO = new OrderAfterSaleDTO();
        afterSaleDTO.setId(1L);
        afterSaleDTO.setStoreId(1L);
        Boolean result = RpcResultUtil.handle(orderAfterSaleMutateService.updateById(afterSaleDTO));
        System.out.println(result);
    }

    @Test
    void processFinish() {
        OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishReq = new OrderAfterSaleProcessFinishReq();
        orderAfterSaleProcessFinishReq.setOrderAfterSaleNo("AS165329066940259");
//        orderAfterSaleProcessFinishReq.setShouldCount(1);
//        orderAfterSaleProcessFinishReq.setDeliveryType();
//        orderAfterSaleProcessFinishReq.setShortCount(0);
//        orderAfterSaleProcessFinishReq.setState();
//        orderAfterSaleProcessFinishReq.setRemark();

//        orderAfterSaleMutateService.processFinish()
    }

    @Test
    void reviewSubmissions() {
        OrderAfterSaleAuditReq req = new OrderAfterSaleAuditReq();
        req.setAfterSaleOrderNo("AS1743176632164487168");
        req.setHandleRemark("退运费，fdmssk");
        req.setAuditStatus(1);
        req.setAmount(3);
        req.setTotalPrice(new BigDecimal("3"));
        req.setOperatorName("xwk");
        req.setResponsibilityType("0");
        req.setSupplierTotalRefundPrice(new BigDecimal("3"));
        req.setSystemSource(1);

        DubboResponse<Boolean> response = orderAfterSaleMutateService.reviewSubmissions(req);
    }

 /*   @Test
    void autoFinished() {

    }*/


    @Test
    void updateAfterSaleStoreNo() {
        OrderAfterSaleUpdateStoreNoReq req = new OrderAfterSaleUpdateStoreNoReq();
        req.setOrderAfterSaleNo("AS1686677147895537664");
        req.setSourceStoreNo(1);
        req.setStoreNo(172);
        DubboResponse<Boolean> response = orderAfterSaleMutateService.updateAfterSaleStoreNo(req);
        System.out.println(response.getData());
    }

    @Test
    void createPreDeliveryAfterSaleWith99OrderItem() {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Order order = orderDao.getById(93265L);
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(order.getId());
        List<OrderAfterSaleDTO> orderAfterSaleDTOS = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            orderAfterSaleDTOS.add(buildOrderAfterSaleDTO(order, orderItem, 1, 1, "拍多/拍错/不想要"));
        }
        //创建订单99
        orderAfterSaleMutateService.createPreDeliveryAfterSale(orderAfterSaleDTOS);
        System.out.println(stopwatch.stop());
    }

    private OrderAfterSaleDTO buildOrderAfterSaleDTO(Order combineOrder, OrderItem orderItem, Integer afterSaleType, Integer serviceType, String reason) {
        OrderAfterSaleDTO orderAfterSale = new OrderAfterSaleDTO();
        orderAfterSale.setTenantId(combineOrder.getTenantId());
        orderAfterSale.setOrderId(combineOrder.getId());
        orderAfterSale.setOrderItemId(orderItem.getId());
        orderAfterSale.setAmount(orderItem.getAmount());
        orderAfterSale.setAfterSaleType(afterSaleType);
        orderAfterSale.setServiceType(serviceType);
        orderAfterSale.setApplyPrice(orderItem.getTotalPrice());
        orderAfterSale.setWarehouseType(combineOrder.getWarehouseType());
        orderAfterSale.setReason(reason);
        orderAfterSale.setAutoFinishedTime(LocalDateTime.now().plusHours(combineOrder.getAutoFinishedTime() * TimeUnit.DAYS.toHours(1)));
//        LocalDateTime autoFinishedTme = orderAfterSaleBizService.orderAfterSaleDataCheck(orderAfterSale, combineOrder);

        return orderAfterSale;
    }


    @Test
    void dumpAfterSaleTest() {
        OrderAfterSaleDTO orderAfterSaleDTO = new OrderAfterSaleDTO();
        orderAfterSaleDTO.setOrderId(93206L);
        orderAfterSaleDTO.setOrderItemId(245284L);
        orderAfterSaleDTO.setAmount(1);
        orderAfterSaleDTO.setAfterSaleType(1);
        orderAfterSaleDTO.setServiceType(1);
        orderAfterSaleMutateService.createPreDeliveryAfterSale(Lists.newArrayList(orderAfterSaleDTO, orderAfterSaleDTO));
    }
}