package com.cosfo.ordercenter.service.provider;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Author: fansongsong
 * @Date: 2023-09-20
 * @Description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class OrderAfterSaleOpenProviderImplTest {

//    @Resource
//    private OrderAfterSaleOpenService OrderAfterSaleOpenService;
//
//    @Test
//    public void batchCreateAfterSale() {
//        Long tenantId = 2L;
//        IsvInfo isvInfo = new IsvInfo();
//        isvInfo.setAccountId(tenantId);
//        IsvInfoHolder.setAccount(isvInfo);
//
//        OrderAfterSaleBatchReq orderAfterSaleBatchReq = new OrderAfterSaleBatchReq();
//        OrderAfterSaleReq orderAfterSaleReq = new OrderAfterSaleReq();
//        orderAfterSaleReq.setCustomerOrderId("NJ158426693");
//        orderAfterSaleReq.setCustomerOrderItemId("NJ1006");
//        orderAfterSaleReq.setCustomerAfterSaleOrderNo("NJAS10066");
//        orderAfterSaleReq.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
//        orderAfterSaleReq.setAmount(1);
//        orderAfterSaleReq.setServiceType(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue());
//        orderAfterSaleReq.setReason("不想要了");
//
//        OrderAfterSaleReq orderAfterSaleReq2 = new OrderAfterSaleReq();
//        orderAfterSaleReq2.setCustomerOrderId("NJ158426693");
//        orderAfterSaleReq2.setCustomerOrderItemId("NJ1007");
//        orderAfterSaleReq2.setCustomerAfterSaleOrderNo("NJAS10077");
//        orderAfterSaleReq2.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
//        orderAfterSaleReq2.setAmount(1);
//        orderAfterSaleReq2.setServiceType(OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue());
//        orderAfterSaleReq2.setReason("不想要了");
//        orderAfterSaleBatchReq.setApplyAfterSaleList(Lists.newArrayList(orderAfterSaleReq, orderAfterSaleReq2));
//        String param = JSON.toJSONString(orderAfterSaleReq);
//        System.err.println(param);
//        DubboResponse<Boolean> response = OrderAfterSaleOpenService.batchCreateAfterSale(orderAfterSaleBatchReq);
//
//    }
}
