package com.cosfo.ordercenter.service.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleEnableApplyReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleOutQueryDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleOutDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.client.resp.QueryResentOrderAfterSaleDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@SpringBootTest
class OrderAfterSaleQueryProviderImplTest {
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;

    @Test
    void queryOrderAfterSaleInfoByPage() {
        OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO = new OrderAfterSaleOutQueryDTO();
        List<String> afterSaleOrderNos = new ArrayList<>();
        afterSaleOrderNos.add("AS1678665902945390592");
        afterSaleOrderNos.add("AS1678668833744670720");
        orderAfterSaleOutQueryDTO.setAfterSaleOrderNos(afterSaleOrderNos);
        orderAfterSaleOutQueryDTO.setTenantId(2L);
        DubboResponse<List<OrderAfterSaleOutDTO>> pageInfoDubboResponse = orderAfterSaleQueryService.queryOrderAfterSaleInfo(orderAfterSaleOutQueryDTO);
        System.out.println(pageInfoDubboResponse);
    }

    @Test
    void queryPageTest() {
        OrderAfterSalePageQueryReq pageQueryReq = new OrderAfterSalePageQueryReq();
        pageQueryReq.setTenantId(2L);
        pageQueryReq.setStoreIds(Lists.newArrayList(4139L));
        pageQueryReq.setPageNum(1);
        pageQueryReq.setPageSize(10);
        PageInfo<OrderAfterSaleWithOrderDTO> afterSalePage = RpcResultUtil.handle(orderAfterSaleQueryService.queryPage(pageQueryReq));
        System.out.println(afterSalePage);
    }

    @Test
    void queryPage() {
        OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
//        req.setSupplierTenantId(1L);
        req.setPageNum(1);
        req.setPageSize(10);
        req.setTenantId(2L);
        req.setStatusList(Lists.newArrayList(2,9,10,11));
//        req.setTenantIds(Lists.newArrayList(24463L,24513L,24514L));
        DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> response = orderAfterSaleQueryService.queryPage(req);
        System.out.println(response);
    }

    @Test
    void queryEnableApply() {
        OrderAfterSaleEnableApplyReq afterSaleEnableApplyReq = new OrderAfterSaleEnableApplyReq();
        afterSaleEnableApplyReq.setOrderId(98102L);
        afterSaleEnableApplyReq.setTenantId(2L);
//        afterSaleEnableApplyReq.setOrderItemId(237563L);
        Map<Long, OrderAfterSaleEnableDTO> handle = RpcResultUtil.handle(orderAfterSaleQueryService.queryEnableApply(afterSaleEnableApplyReq));
        System.out.println(JSON.toJSONString(handle));
    }

    @Test
    void queryListTest() {
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setTenantId(2L);
        queryReq.setOrderItemIds(Lists.newArrayList(191230L));
        List<OrderAfterSaleDTO> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryList(queryReq));
        System.out.println(JSON.toJSONString(afterSaleDTOList));
    }

    @Test
    void queryByOrderId() {
        List<OrderAfterSaleDTO> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryByOrderId(86877L, 2L));
        System.out.println(JSON.toJSONString(afterSaleDTOList));
    }

    @Test
    void queryResentOrderAfterSaleForTms() {
        QueryResentOrderAfterSaleReq req = new QueryResentOrderAfterSaleReq();
        req.setStoreId(3312L);
        req.setSku("50506777883");
        req.setCreateDate(LocalDate.parse("2023-09-04"));
        req.setDeliveryDate(LocalDate.parse("2023-09-06"));

        List<QueryResentOrderAfterSaleDTO> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryResentOrderAfterSaleForTms(req));
        System.out.println(JSON.toJSONString(afterSaleDTOList));
    }

    @Test
    void insertList() {
        OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setTenantId(0L);
        orderAfterSale.setOrderId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setStoreId(0L);
        orderAfterSale.setAccountId(0L);
        orderAfterSale.setAfterSaleOrderNo("");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setApplyPrice(new BigDecimal("0"));
        orderAfterSale.setTotalPrice(new BigDecimal("0"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0"));
        orderAfterSale.setReason("");
        orderAfterSale.setUserRemark("");
        orderAfterSale.setProofPicture("");
        orderAfterSale.setStatus(0);
        orderAfterSale.setHandleRemark("");
        orderAfterSale.setOperatorName("");
        orderAfterSale.setCreateTime(LocalDateTime.now());
        orderAfterSale.setUpdateTime(LocalDateTime.now());
        orderAfterSale.setFinishedTime(LocalDateTime.now());
        orderAfterSale.setHandleTime(LocalDateTime.now());
        orderAfterSale.setRecycleTime(LocalDateTime.now());
        orderAfterSale.setRecycleDetails("");
        orderAfterSale.setResponsibilityType(0);
        orderAfterSale.setStoreNo(0);
        orderAfterSale.setWarehouseType(0);
        orderAfterSale.setAutoFinishedTime(LocalDateTime.now());
        orderAfterSale.setApplyQuantity(0);
        orderAfterSale.setAdminRemark("");
        orderAfterSale.setAdminRemarkTime(LocalDateTime.now());
        orderAfterSale.setSecondHandleRemark("");
        orderAfterSale.setReturnAddressId(0L);
        orderAfterSale.setReturnWarehouseNo("");



        System.out.println(JSON.toJSONString(orderAfterSaleDao.batchAdd(Lists.newArrayList(orderAfterSale))));
    }
}