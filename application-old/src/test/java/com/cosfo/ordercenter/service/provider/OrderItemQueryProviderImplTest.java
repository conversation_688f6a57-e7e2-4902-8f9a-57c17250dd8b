package com.cosfo.ordercenter.service.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import net.xianmu.common.result.DubboResponse;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderItemQueryProviderImplTest {

    @Resource
    private OrderItemQueryService orderItemQueryService;


    @Test
    void queryById() {
        DubboResponse<OrderItemDTO> response = orderItemQueryService.queryById(5L);
        System.out.println(response);
    }

    @Test
    void queryList() {
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(Lists.newArrayList(71156L));
        DubboResponse<List<OrderItemAndSnapshotDTO>> response = orderItemQueryService.queryOrderItemList(orderItemQueryReq);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void batchQueryList() {
        DubboResponse<List<OrderItemDTO>> listDubboResponse = orderItemQueryService.queryOrderItemList(5L);
        System.out.println(listDubboResponse);
    }
}