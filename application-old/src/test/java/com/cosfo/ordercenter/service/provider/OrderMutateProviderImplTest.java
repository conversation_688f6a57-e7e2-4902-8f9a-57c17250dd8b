package com.cosfo.ordercenter.service.provider;
import com.cosfo.ordercenter.client.req.*;
import com.google.common.collect.Lists;
import java.time.LocalDate;

import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.service.OrderMutateService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderMutateProviderImplTest {

    @Resource
    private OrderMutateService orderMutateService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Test
    void updateById() {
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setId(67713L);
        orderDTO.setStatus(1);
        DubboResponse<Boolean> response = orderMutateService.updateById(orderDTO);
        System.out.println(response);
    }

    @Test
    void updateStatus() {
        OrderStatusUpdateReq req = new OrderStatusUpdateReq();
        req.setStatus(2);
        req.setOriginStatus(1);
        req.setOrderId(67713L);
        req.setTenantId(2L);
        DubboResponse<Boolean> response = orderMutateService.updateStatus(req);
        System.out.println(response.getData());
    }

    @Test
    void batchUpdateStatus() {
        OrderStatusBatchUpdateReq req = new OrderStatusBatchUpdateReq();
        req.setOrderNos(Lists.newArrayList("OR168777215874439"));
        req.setOriginStatus(5);
        req.setUpdateStatus(2);
//        req.setTenantId(2L);
        DubboResponse<Integer> integerDubboResponse = orderMutateService.batchUpdateStatus(req);
        System.out.println(integerDubboResponse.getData());
    }

    @Test
    void selfLifting() {
        OrderSelfLiftReq req = new OrderSelfLiftReq();
        req.setOrderId(67713L);
        DubboResponse<Boolean> response = orderMutateService.selfLifting(req);
        System.out.println(response.getData());
    }

    @Test
    void selfLiftingFinish() {
        OrderSelfLiftingFinishReq req = new OrderSelfLiftingFinishReq();
        req.setOrderId(87603L);
        DubboResponse<Boolean> response = orderMutateService.selfLiftingFinish(req);
        System.out.println(response.getData());
    }

    @Test
    void updateDeliveryTime() {
        OrderUpdateDelivertDateReq req = new OrderUpdateDelivertDateReq();
        req.setOrderNoList(Lists.newArrayList("01165294146686424", "01165294208211522"));
        req.setDeliveryDate(LocalDate.now().plusDays(2));
        DubboResponse<Integer> response = orderMutateService.updateOrderDeliveryTime(req);
        System.out.println(response.getData());
    }

    @Test
    void close() {
        OrderCloseReq req = new OrderCloseReq();
        req.setOrderId(95344L);
        req.setTenantId(2L);
        DubboResponse<Boolean> response = orderMutateService.close(req);
        System.out.println(response.getData());
    }

    @Test
    void finishTest() {
        DubboResponse<Boolean> finish = orderMutateService.finish();
        System.out.println(finish);
    }



    @Test
    void updateOrderStoreNo() {
        OrderUpdateStoreNoReq req = new OrderUpdateStoreNoReq();
        req.setOrderNo("OR169293030091051");
        req.setSourceStoreNo(2);
        req.setStoreNo(1);
        DubboResponse<Boolean> response = orderMutateService.updateOrderStoreNo(req);
        System.out.println(response.getData());
    }
}