package com.cosfo.ordercenter.service.provider;

import com.cosfo.ordercenter.client.req.OrderOutQueryReq;
import com.cosfo.ordercenter.client.resp.OrderOutDTO;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@SpringBootTest
class OrderQueryProviderImplTest {
    @Resource
    private OrderQueryService  orderQueryService;

    @Test
    void queryOrderInfoByPage() {
        OrderOutQueryReq orderOutQueryReq = new OrderOutQueryReq();
        List<String> orderNos = new ArrayList<>();
        orderNos.add("OR168922991815174");
        // orderNos.add("OR168965244955195");
        orderOutQueryReq.setOrderNos(orderNos);
        //orderOutQueryReq.setTenantId(2L);
        orderQueryService.queryOrderInfo(orderOutQueryReq);
    }
}