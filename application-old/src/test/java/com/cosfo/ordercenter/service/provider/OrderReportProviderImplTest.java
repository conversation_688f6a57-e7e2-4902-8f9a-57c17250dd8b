package com.cosfo.ordercenter.service.provider;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.SupplierOrderTotalReq;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.service.OrderReportService;
import net.xianmu.common.result.DubboResponse;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-19
 * @Description:
 */
@SpringBootTest
public class OrderReportProviderImplTest {

    @Resource
    private OrderReportService orderReportService;

    @Test
    void querySupplierOrderSummary() {
        SupplierOrderTotalReq supplierOrderTotalReq = new SupplierOrderTotalReq();
        supplierOrderTotalReq.setSupplierIds(Lists.newArrayList(2189L));
        supplierOrderTotalReq.setTenantId(2L);
        supplierOrderTotalReq.setWarehouseType(WarehouseTypeEnum.PROPRIETARY.getCode());
        supplierOrderTotalReq.setStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode(), OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode()));

        DubboResponse<List<SupplierOrderTotalResp>> response = orderReportService.querySupplierOrderSummary(supplierOrderTotalReq);
        System.err.println(JSON.toJSONString(response));
    }
}
