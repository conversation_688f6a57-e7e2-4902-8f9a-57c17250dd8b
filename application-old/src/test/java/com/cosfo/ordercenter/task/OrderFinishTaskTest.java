package com.cosfo.ordercenter.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderFinishTaskTest {

    @Resource
    private OrderFinishTask orderFinishTask;

    /**
     * 时间范围配置异常
     */
    @Test
    void processResultParamError1() {
        Assertions.assertThrows(ProviderException.class, () -> {
            XmJobInput xmJobInput = new XmJobInput();
            xmJobInput.setInstanceParameters("{\"deliveryDayStartOffset\":1,\"deliveryDayEndOffset\":1,\"limit\":1}");
            orderFinishTask.processResult(xmJobInput);
        });
    }

    /**
     * 配置存在空值
     */
    @Test
    void processResultParamError2() {
        Assertions.assertThrows(ProviderException.class, () -> {
            XmJobInput xmJobInput = new XmJobInput();
            xmJobInput.setInstanceParameters("{\"deliveryDayStartOffset\": null,\"deliveryDayEndOffset\":1,\"limit\":1}");
            orderFinishTask.processResult(xmJobInput);
        });
    }

    /**
     * 走默认配置
     */
    @Test
    void processResultParamDefault() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        ProcessResult processResult = orderFinishTask.processResult(xmJobInput);
        assertTrue(processResult.getStatus().isFinish());
    }

    @Test
    void processResultParamWithInstance() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"deliveryDayStartOffset\":2,\"deliveryDayEndOffset\":1,\"limit\":10}");
        ProcessResult processResult = orderFinishTask.processResult(xmJobInput);
        assertTrue(processResult.getStatus().isFinish());
    }

    @Test
    void processResultParamWithJob() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setJobParameters("{\"deliveryDayStartOffset\":2,\"deliveryDayEndOffset\":1,\"limit\":1}");
        ProcessResult processResult = orderFinishTask.processResult(xmJobInput);
        assertTrue(processResult.getStatus().isFinish());
    }
}