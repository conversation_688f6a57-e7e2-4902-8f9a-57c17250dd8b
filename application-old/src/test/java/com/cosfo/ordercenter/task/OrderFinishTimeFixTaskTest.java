package com.cosfo.ordercenter.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderFinishTimeFixTaskTest {

    @Resource
    private OrderFinishTimeFixTask orderFinishTimeFixTask;

    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("90057, 90056, 90027, 90026, 90025, 90024, 90023, 90022, 90021, 90018");
        ProcessResult processResult = orderFinishTimeFixTask.processResult(xmJobInput);
    }
}