package com.cosfo.ordercenter.task;

import net.xianmu.task.vo.input.XmJobInput;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class OrderStatusMonitorTaskTest {

    @Resource
    private OrderStatusMonitorTask orderStatusMonitorTask;

    @Test
    void processResult() throws Exception {
        XmJobInput xmJobInput = new XmJobInput();
        xmJobInput.setInstanceParameters("{\"orderStartTime\": \"2023-11-24 09:00:00\"}");
        orderStatusMonitorTask.processResult(xmJobInput);
    }
}