<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cosfo</groupId>
        <artifactId>order-center</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>order-center-application</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--   内部依赖     -->
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-facade</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-domain</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--    外部依赖    -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-infrastructure</artifactId>
            <version>${project.version}</version>

        </dependency>

    </dependencies>

</project>