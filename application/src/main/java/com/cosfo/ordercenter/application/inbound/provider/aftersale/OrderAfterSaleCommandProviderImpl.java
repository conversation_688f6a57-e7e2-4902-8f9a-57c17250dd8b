package com.cosfo.ordercenter.application.inbound.provider.aftersale;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleCommandService;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAfterSaleCommandProviderImpl implements OrderAfterSaleCommandProvider {

    @Resource
    private OrderAfterSaleCommandService orderAfterSaleCommandService;

    @Override
    public DubboResponse<Long> createAfterDeliveryAfterSale(OrderAfterSaleAddReq orderAfterSaleAddReq) {
        OrderAfterSaleEntity orderAfterSaleEntity = OrderAfterSaleConverter.convertToEntity(orderAfterSaleAddReq);
        return DubboResponse.getOK(orderAfterSaleCommandService.createAfterDeliveryAfterSale(orderAfterSaleEntity));
    }

    @Override
    public DubboResponse<List<Long>> createPreDeliveryAfterSale(List<OrderAfterSaleAddReq> orderAfterSaleAddReqs) {
        List<OrderAfterSaleEntity> orderAfterSaleEntities = OrderAfterSaleConverter.convertToEntityList(orderAfterSaleAddReqs);
        return DubboResponse.getOK(orderAfterSaleCommandService.createPreDeliveryAfterSale(orderAfterSaleEntities));
    }

    @Override
    public DubboResponse<Boolean> cancel(Long orderAfterSaleId) {
        return DubboResponse.getOK(orderAfterSaleCommandService.cancel(orderAfterSaleId));
    }

    @Override
    public DubboResponse<Boolean> updateStatus(OrderAfterSaleStatusUpdateReq req) {
        return DubboResponse.getOK(orderAfterSaleCommandService.updateStatus(req));
    }

    @Override
    public DubboResponse<Boolean> updateById(OrderAfterSaleUpdateReq orderAfterSaleUpdateReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.updateById(orderAfterSaleUpdateReq));
    }

    @Override
    public DubboResponse<Boolean> processFinish(List<OrderAfterSaleProcessFinishReq> reqs) {
        return DubboResponse.getOK(orderAfterSaleCommandService.processFinish(reqs));
    }

    @Override
    public DubboResponse<Boolean> reviewSubmissions(OrderAfterSaleAuditReq auditReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.reviewSubmissions(auditReq));
    }

    @Override
    public DubboResponse<Boolean> serviceProviderReviewSubmissions(OrderAfterSaleAuditReq auditReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.serviceProviderReviewSubmissions(auditReq));
    }

    @Override
    public DubboResponse<Boolean> autoFinished() {
        return DubboResponse.getOK(orderAfterSaleCommandService.autoFinished());
    }

    @Override
    public DubboResponse<Boolean> updateAfterSaleStoreNo(@Valid OrderAfterSaleUpdateStoreNoReq req) {
        return DubboResponse.getOK(orderAfterSaleCommandService.updateAfterSaleStoreNo(req));
    }

    @Override
    public DubboResponse<List<Long>> batchCreateAfterSaleForOpen(@Valid OrderAfterSaleBatchReq orderAfterSaleBatchReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.batchCreateAfterSaleForOpen(orderAfterSaleBatchReq));
    }

    @Override
    public DubboResponse<Boolean> modifyQuantity(@Valid OrderAfterSaleModifyQuantityReq modifyQuantityReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.modifyQuantity(modifyQuantityReq));
    }

    @Override
    public DubboResponse<Boolean> recycleFailRefund(@Valid OrderAfterSaleRecycleFailRefundReq recycleFailRefundReq) {
        return DubboResponse.getOK(orderAfterSaleCommandService.recycleFailRefund(recycleFailRefundReq));
    }
}
