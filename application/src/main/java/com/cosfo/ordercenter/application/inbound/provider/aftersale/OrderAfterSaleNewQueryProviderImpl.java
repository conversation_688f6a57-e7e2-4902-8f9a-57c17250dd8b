package com.cosfo.ordercenter.application.inbound.provider.aftersale;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.aftersale.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleCountParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.aftersale.param.QueryBillOrderAfterSaleParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAfterSaleNewQueryProviderImpl implements OrderAfterSaleQueryProvider {

    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;

    @Override
    public DubboResponse<List<OrderAfterSaleOutResp>> queryOrderAfterSaleInfo(OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO) {
        if (orderAfterSaleOutQueryDTO.getAfterSaleOrderNos().size() > 20) {
            throw new BizException("售后单数量不能大于20个");
        }
        return DubboResponse.getOK(orderAfterSaleQueryService.queryOrderAfterSaleInfo(orderAfterSaleOutQueryDTO));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleResp>> queryByOrderId(Long orderId, Long tenantId) {
        List<OrderAfterSaleEntity> orderAfterSaleEntities = orderAfterSaleQueryRepository.queryByOrderId(orderId, tenantId);
        return DubboResponse.getOK(OrderAfterSaleConverter.convertToRespList(orderAfterSaleEntities));
    }

    @Override
    public DubboResponse<Map<Long, OrderAfterSaleEnableResp>> queryEnableApply(OrderAfterSaleEnableApplyReq req) {
        OrderAfterSaleEnableApplyParam orderAfterSaleEnableApplyParam = new OrderAfterSaleEnableApplyParam();
        orderAfterSaleEnableApplyParam.setOrderId(req.getOrderId());
        orderAfterSaleEnableApplyParam.setTenantId(req.getTenantId());
        if (Objects.nonNull(req.getOrderItemId())) {
            orderAfterSaleEnableApplyParam.setOrderItemIds(Collections.singletonList(req.getOrderItemId()));
        }
        return DubboResponse.getOK(orderAfterSaleBizService.queryEnableApplyByApplyParam(orderAfterSaleEnableApplyParam));
    }

    @Override
    public DubboResponse<Integer> countOrderAfterSale(OrderAfterSaleCountReq req) {
        OrderAfterSaleCountParam orderAfterSaleCountParam = OrderAfterSaleConverter.convertToCountParam(req);
        return DubboResponse.getOK(orderAfterSaleQueryRepository.countOrderAfterSale(orderAfterSaleCountParam));
    }

    @Override
    public DubboResponse<Map<Long, Integer>> countOrderAfterSaleByOrderId(OrderAfterSaleCountReq req) {
        OrderAfterSaleCountParam orderAfterSaleCountParam = OrderAfterSaleConverter.convertToCountParam(req);
        return DubboResponse.getOK(orderAfterSaleQueryRepository.countOrderAfterSaleByOrderId(orderAfterSaleCountParam));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleResp>> queryList(OrderAfterSaleQueryReq req) {
        OrderAfterSaleListQueryParam orderAfterSaleListQueryParam = OrderAfterSaleConverter.convertToListQueryParam(req);
        List<OrderAfterSaleEntity> orderAfterSaleEntities = orderAfterSaleQueryRepository.queryList(orderAfterSaleListQueryParam);
        return DubboResponse.getOK(OrderAfterSaleConverter.convertToRespList(orderAfterSaleEntities));
    }

    @Override
    public DubboResponse<PageInfo<OrderAfterSaleWithOrderResp>> queryPage(OrderAfterSalePageQueryReq req) {
        return DubboResponse.getOK(orderAfterSaleQueryService.queryPage(req));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleResp>> queryByNos(List<String> orderAfterSaleNos) {
        return DubboResponse.getOK(orderAfterSaleQueryService.queryByNos(orderAfterSaleNos));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleResp>> queryByIds(List<Long> orderAfterSaleIds) {
        return DubboResponse.getOK(orderAfterSaleQueryService.queryByIds(orderAfterSaleIds));
    }

    @Override
    public DubboResponse<BigDecimal> calculateRefundPrice(OrderAfterSaleCalRefundPriceReq req) {
        return DubboResponse.getOK(orderAfterSaleQueryService.calculateRefundPrice(req));
    }

    @Override
    public DubboResponse<Long> getRecentlyUsedReturnAddressId(Long tenantId) {
        return DubboResponse.getOK(orderAfterSaleQueryRepository.getRecentlyUsedReturnAddressId(tenantId));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleResp>> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleReq req) {
        QueryBillOrderAfterSaleParam queryBillOrderAfterSaleParam = OrderAfterSaleConverter.convertToQueryBillOrderAfterSaleParam(req);
        return DubboResponse.getOK(OrderAfterSaleConverter.convertToRespList((orderAfterSaleQueryRepository.queryOrderAfterSaleForBill(queryBillOrderAfterSaleParam))));
    }

    @Override
    public DubboResponse<List<QueryResentOrderAfterSaleResp>> queryResentOrderAfterSaleForTms(@Valid QueryResentOrderAfterSaleReq req) {
        return DubboResponse.getOK(orderAfterSaleQueryService.queryResentOrderAfterSaleForTms(req));
    }
}
