package com.cosfo.ordercenter.application.inbound.provider.aftersale;

import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleRuleService;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleRuleCommandReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAfterSaleRuleCommandProviderImpl implements OrderAfterSaleRuleCommandProvider {

    @Resource
    private OrderAfterSaleRuleService orderAfterSaleRuleService;

    @Override
    public DubboResponse<Long> add(OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO) {
        return DubboResponse.getOK(orderAfterSaleRuleService.add(orderAfterSaleRuleDTO));
    }

    @Override
    public DubboResponse<Boolean> updateRule(OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO) {
        return DubboResponse.getOK(orderAfterSaleRuleService.updateRule(orderAfterSaleRuleDTO));
    }

    @Override
    public DubboResponse<Boolean> deleteRule(Long id) {
        return DubboResponse.getOK(orderAfterSaleRuleService.deleteRule(id));
    }
}
