package com.cosfo.ordercenter.application.inbound.provider.aftersale;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleRuleConverter;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleRuleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleRuleQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAfterSaleRuleQueryProviderImpl implements OrderAfterSaleRuleQueryProvider {

    @Resource
    private OrderAfterSaleRuleQueryRepository afterSaleRuleQueryRepository;

    @Override
    public DubboResponse<List<OrderAfterSaleRuleResp>> queryByTenantId(Long tenantId) {
        List<OrderAfterSaleRuleEntity> orderAfterSaleRuleEntities = afterSaleRuleQueryRepository.queryByTenantId(tenantId);
        return DubboResponse.getOK(OrderAfterSaleRuleConverter.convertToRespList(orderAfterSaleRuleEntities));
    }

}
