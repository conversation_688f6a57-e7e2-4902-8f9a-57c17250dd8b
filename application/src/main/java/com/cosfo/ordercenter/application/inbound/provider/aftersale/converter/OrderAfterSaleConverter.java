package com.cosfo.ordercenter.application.inbound.provider.aftersale.converter;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleOutResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleCountParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSalePageQueryParam;
import com.cosfo.ordercenter.domain.aftersale.param.QueryBillOrderAfterSaleParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.*;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderAfterSaleConverter {

    private OrderAfterSaleConverter() {
    }

    public static OrderAfterSaleOutResp buildOrderAfterSaleOutDTO(OrderAfterSaleEntity orderAfterSale, OrderItemEntity orderItem, OrderItemSnapshotEntity orderItemSnapshot) {

        if (orderAfterSale == null) {
            return null;
        }
        OrderAfterSaleOutResp orderAfterSaleOutDTO = new OrderAfterSaleOutResp();
        orderAfterSaleOutDTO.setId(orderAfterSale.getId());
        orderAfterSaleOutDTO.setOrderAfterSaleNo(orderAfterSale.getAfterSaleOrderNo());
        orderAfterSaleOutDTO.setAmount(orderAfterSale.getAmount());
        orderAfterSaleOutDTO.setTotalPrice(orderAfterSale.getTotalPrice());
        orderAfterSaleOutDTO.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleOutDTO.setSkuId(orderItemSnapshot.getSkuId());
        orderAfterSaleOutDTO.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
        orderAfterSaleOutDTO.setAreaItemId(orderItemSnapshot.getAreaItemId());
        orderAfterSaleOutDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
        orderAfterSaleOutDTO.setTitle(orderItemSnapshot.getTitle());
        orderAfterSaleOutDTO.setMainPicture(orderItemSnapshot.getMainPicture());
        orderAfterSaleOutDTO.setSpecification(orderItemSnapshot.getSpecification());
        orderAfterSaleOutDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
        orderAfterSaleOutDTO.setPrice(orderItem.getPayablePrice());
        return orderAfterSaleOutDTO;
    }

    public static OrderAfterSaleResp toOrderAfterSaleResp(OrderAfterSaleEntity orderAfterSaleEntity) {
        if (orderAfterSaleEntity == null) {
            return null;
        }
        OrderAfterSaleResp orderAfterSaleResp = new OrderAfterSaleResp();
        orderAfterSaleResp.setId(orderAfterSaleEntity.getId());
        orderAfterSaleResp.setTenantId(orderAfterSaleEntity.getTenantId());
        orderAfterSaleResp.setOrderId(orderAfterSaleEntity.getOrderId());
        orderAfterSaleResp.setOrderItemId(orderAfterSaleEntity.getOrderItemId());
        orderAfterSaleResp.setStoreId(orderAfterSaleEntity.getStoreId());
        orderAfterSaleResp.setAccountId(orderAfterSaleEntity.getAccountId());
        orderAfterSaleResp.setAfterSaleOrderNo(orderAfterSaleEntity.getAfterSaleOrderNo());
        orderAfterSaleResp.setAmount(orderAfterSaleEntity.getAmount());
        orderAfterSaleResp.setAfterSaleType(orderAfterSaleEntity.getAfterSaleType());
        orderAfterSaleResp.setServiceType(orderAfterSaleEntity.getServiceType());
        orderAfterSaleResp.setApplyPrice(orderAfterSaleEntity.getApplyPrice());
        orderAfterSaleResp.setTotalPrice(orderAfterSaleEntity.getTotalPrice());
        orderAfterSaleResp.setDeliveryFee(orderAfterSaleEntity.getDeliveryFee());
        orderAfterSaleResp.setReason(orderAfterSaleEntity.getReason());
        orderAfterSaleResp.setUserRemark(orderAfterSaleEntity.getUserRemark());
        orderAfterSaleResp.setProofPicture(orderAfterSaleEntity.getProofPicture());
        orderAfterSaleResp.setStatus(orderAfterSaleEntity.getStatus());
        orderAfterSaleResp.setHandleRemark(orderAfterSaleEntity.getHandleRemark());
        orderAfterSaleResp.setOperatorName(orderAfterSaleEntity.getOperatorName());
        orderAfterSaleResp.setCreateTime(orderAfterSaleEntity.getCreateTime());
        orderAfterSaleResp.setUpdateTime(orderAfterSaleEntity.getUpdateTime());
        orderAfterSaleResp.setFinishedTime(orderAfterSaleEntity.getFinishedTime());
        orderAfterSaleResp.setHandleTime(orderAfterSaleEntity.getHandleTime());
        orderAfterSaleResp.setRecycleTime(orderAfterSaleEntity.getRecycleTime());
        orderAfterSaleResp.setRecycleDetails(orderAfterSaleEntity.getRecycleDetails());
        orderAfterSaleResp.setResponsibilityType(orderAfterSaleEntity.getResponsibilityType());
        orderAfterSaleResp.setStoreNo(orderAfterSaleEntity.getStoreNo());
        orderAfterSaleResp.setWarehouseType(orderAfterSaleEntity.getWarehouseType());
        orderAfterSaleResp.setAutoFinishedTime(orderAfterSaleEntity.getAutoFinishedTime());
        orderAfterSaleResp.setApplyQuantity(orderAfterSaleEntity.getApplyQuantity());
        orderAfterSaleResp.setAdminRemark(orderAfterSaleEntity.getAdminRemark());
        orderAfterSaleResp.setAdminRemarkTime(orderAfterSaleEntity.getAdminRemarkTime());
        orderAfterSaleResp.setSecondHandleRemark(orderAfterSaleEntity.getSecondHandleRemark());
        orderAfterSaleResp.setReturnAddressId(orderAfterSaleEntity.getReturnAddressId());
        orderAfterSaleResp.setReturnWarehouseNo(orderAfterSaleEntity.getReturnWarehouseNo());
        orderAfterSaleResp.setCustomerAfterSaleOrderNo(orderAfterSaleEntity.getCustomerAfterSaleOrderNo());
        orderAfterSaleResp.setServiceProviderAuditTime(orderAfterSaleEntity.getServiceProviderAuditTime());
        orderAfterSaleResp.setRecyclePicture(orderAfterSaleEntity.getRecyclePicture());
        orderAfterSaleResp.setRefundReceipt(orderAfterSaleEntity.getRefundReceipt());
// Not mapped TO fields:
// orderNo
// reqSource
// Not mapped FROM fields:
// recycleQuantityDetail
        return orderAfterSaleResp;
    }

    public static List<OrderAfterSaleResp> convertToRespList(List<OrderAfterSaleEntity> orderAfterSaleEntities) {

        if (orderAfterSaleEntities == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleResp> orderAfterSaleRespList = new ArrayList<>();
        for (OrderAfterSaleEntity orderAfterSaleEntity : orderAfterSaleEntities) {
            orderAfterSaleRespList.add(toOrderAfterSaleResp(orderAfterSaleEntity));
        }
        return orderAfterSaleRespList;
    }

    public static OrderAfterSaleCountParam convertToCountParam(OrderAfterSaleCountReq countReq) {

        if (countReq == null) {
            return null;
        }
        OrderAfterSaleCountParam orderAfterSaleCountParam = new OrderAfterSaleCountParam();
        orderAfterSaleCountParam.setTenantId(countReq.getTenantId());
        orderAfterSaleCountParam.setStoreId(countReq.getStoreId());
        orderAfterSaleCountParam.setStatusList(countReq.getStatusList());
        orderAfterSaleCountParam.setOrderIds(countReq.getOrderIds());
        return orderAfterSaleCountParam;
    }

    public static OrderAfterSaleListQueryParam convertToListQueryParam(OrderAfterSaleQueryReq queryReq) {

        if (queryReq == null) {
            return null;
        }
        OrderAfterSaleListQueryParam orderAfterSaleListQueryParam = new OrderAfterSaleListQueryParam();
        orderAfterSaleListQueryParam.setStatusList(queryReq.getStatusList());
        orderAfterSaleListQueryParam.setServiceType(queryReq.getServiceType());
        orderAfterSaleListQueryParam.setTenantId(queryReq.getTenantId());
        orderAfterSaleListQueryParam.setSupplierIds(queryReq.getSupplierIds());
        orderAfterSaleListQueryParam.setFinishTimeStart(queryReq.getFinishTimeStart());
        orderAfterSaleListQueryParam.setFinishTimeEnd(queryReq.getFinishTimeEnd());
        orderAfterSaleListQueryParam.setOrderIds(queryReq.getOrderIds());
        orderAfterSaleListQueryParam.setStoreId(queryReq.getStoreId());
        orderAfterSaleListQueryParam.setApplyTime(queryReq.getApplyTime());
        orderAfterSaleListQueryParam.setOrderItemIds(queryReq.getOrderItemIds());
        orderAfterSaleListQueryParam.setAfterSaleType(queryReq.getAfterSaleType());
        return orderAfterSaleListQueryParam;
    }

    public static OrderAfterSalePageQueryParam convertToPageQueryParam(OrderAfterSalePageQueryReq pageQueryReq) {

        if (pageQueryReq == null) {
            return null;
        }
        OrderAfterSalePageQueryParam orderAfterSalePageQueryParam = new OrderAfterSalePageQueryParam();
        orderAfterSalePageQueryParam.setTenantId(pageQueryReq.getTenantId());
        orderAfterSalePageQueryParam.setOrderNo(pageQueryReq.getOrderNo());
        orderAfterSalePageQueryParam.setAfterSaleOrderNo(pageQueryReq.getAfterSaleOrderNo());
        orderAfterSalePageQueryParam.setStatusList(pageQueryReq.getStatusList());
        orderAfterSalePageQueryParam.setAfterSaleType(pageQueryReq.getAfterSaleType());
        orderAfterSalePageQueryParam.setServiceTypeList(pageQueryReq.getServiceTypeList());
        orderAfterSalePageQueryParam.setStoreIds(pageQueryReq.getStoreIds());
        orderAfterSalePageQueryParam.setAccountIds(pageQueryReq.getAccountIds());
        orderAfterSalePageQueryParam.setStartTime(pageQueryReq.getStartTime());
        orderAfterSalePageQueryParam.setEndTime(pageQueryReq.getEndTime());
        orderAfterSalePageQueryParam.setSupplierTenantId(pageQueryReq.getSupplierTenantId());
        orderAfterSalePageQueryParam.setSupplierIds(pageQueryReq.getSupplierIds());
        orderAfterSalePageQueryParam.setTenantIds(pageQueryReq.getTenantIds());
        orderAfterSalePageQueryParam.setItemIds(pageQueryReq.getItemIds());
        orderAfterSalePageQueryParam.setMaxId(pageQueryReq.getMaxId());
        orderAfterSalePageQueryParam.setBatchSize(pageQueryReq.getBatchSize());
        orderAfterSalePageQueryParam.setWarehouseNo(pageQueryReq.getWarehouseNo());
        orderAfterSalePageQueryParam.setWarehouseType(pageQueryReq.getWarehouseType());
        orderAfterSalePageQueryParam.setHandleTimeBegin(pageQueryReq.getHandleTimeBegin());
        orderAfterSalePageQueryParam.setHandleTimeEnd(pageQueryReq.getHandleTimeEnd());
        orderAfterSalePageQueryParam.setPageNum(pageQueryReq.getPageNum());
        orderAfterSalePageQueryParam.setPageSize(pageQueryReq.getPageSize());
        return orderAfterSalePageQueryParam;
    }

    public static OrderAfterSaleWithOrderResp convertToAfterSaleWithOrderResp(OrderAfterSaleEntity orderAfterSale) {

        if (orderAfterSale == null) {
            return null;
        }
        OrderAfterSaleWithOrderResp orderAfterSaleWithOrderResp = new OrderAfterSaleWithOrderResp();
        orderAfterSaleWithOrderResp.setId(orderAfterSale.getId());
        orderAfterSaleWithOrderResp.setTenantId(orderAfterSale.getTenantId());
        orderAfterSaleWithOrderResp.setOrderId(orderAfterSale.getOrderId());
        orderAfterSaleWithOrderResp.setOrderItemId(orderAfterSale.getOrderItemId());
        orderAfterSaleWithOrderResp.setStoreId(orderAfterSale.getStoreId());
        orderAfterSaleWithOrderResp.setAccountId(orderAfterSale.getAccountId());
        orderAfterSaleWithOrderResp.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
        orderAfterSaleWithOrderResp.setAmount(orderAfterSale.getAmount());
        orderAfterSaleWithOrderResp.setAfterSaleType(orderAfterSale.getAfterSaleType());
        orderAfterSaleWithOrderResp.setServiceType(orderAfterSale.getServiceType());
        orderAfterSaleWithOrderResp.setApplyPrice(orderAfterSale.getApplyPrice());
        orderAfterSaleWithOrderResp.setTotalPrice(orderAfterSale.getTotalPrice());
        orderAfterSaleWithOrderResp.setDeliveryFee(orderAfterSale.getDeliveryFee());
        orderAfterSaleWithOrderResp.setReason(orderAfterSale.getReason());
        orderAfterSaleWithOrderResp.setUserRemark(orderAfterSale.getUserRemark());
        orderAfterSaleWithOrderResp.setProofPicture(orderAfterSale.getProofPicture());
        orderAfterSaleWithOrderResp.setStatus(orderAfterSale.getStatus());
        orderAfterSaleWithOrderResp.setHandleRemark(orderAfterSale.getHandleRemark());
        orderAfterSaleWithOrderResp.setOperatorName(orderAfterSale.getOperatorName());
        orderAfterSaleWithOrderResp.setCreateTime(orderAfterSale.getCreateTime());
        orderAfterSaleWithOrderResp.setUpdateTime(orderAfterSale.getUpdateTime());
        orderAfterSaleWithOrderResp.setFinishedTime(orderAfterSale.getFinishedTime());
        orderAfterSaleWithOrderResp.setHandleTime(orderAfterSale.getHandleTime());
        orderAfterSaleWithOrderResp.setRecycleTime(orderAfterSale.getRecycleTime());
        orderAfterSaleWithOrderResp.setRecycleDetails(orderAfterSale.getRecycleDetails());
        orderAfterSaleWithOrderResp.setResponsibilityType(orderAfterSale.getResponsibilityType());
        orderAfterSaleWithOrderResp.setStoreNo(orderAfterSale.getStoreNo());
        orderAfterSaleWithOrderResp.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleWithOrderResp.setAutoFinishedTime(orderAfterSale.getAutoFinishedTime());
        orderAfterSaleWithOrderResp.setReturnAddressId(orderAfterSale.getReturnAddressId());
        orderAfterSaleWithOrderResp.setReturnWarehouseNo(orderAfterSale.getReturnWarehouseNo());
// Not mapped TO fields:
// title
// mainPicture
// specification
// specificationUnit
// orderAmount
// orderPrice
// price
// orderType
// combineOrderId
// itemId
// payablePrice
// skuId
// maxAfterSaleAmount
// afterSaleUnit
// supplierTenantId
// goodsType
// supplyPrice
// orderNo
// Not mapped FROM fields:
// applyQuantity
// adminRemark
// adminRemarkTime
// secondHandleRemark
// customerAfterSaleOrderNo
// serviceProviderAuditTime
// recyclePicture
// recycleQuantityDetail
        return orderAfterSaleWithOrderResp;
    }

    public static OrderAfterSaleWithOrderResp handleOrderAfterSaleDetailInfo(OrderAfterSaleEntity orderAfterSale, Map<Long, OrderItemEntity> orderItemMap, Map<Long, OrderItemSnapshotEntity> snapshotMap,
                                                                             Map<Long, OrderEntity> orderMap) {
        OrderAfterSaleWithOrderResp afterSaleWithOrderDTO = convertToAfterSaleWithOrderResp(orderAfterSale);
        OrderItemEntity orderItem = orderItemMap.get(orderAfterSale.getOrderItemId());
        OrderItemSnapshotEntity orderItemSnapshot = snapshotMap.get(orderAfterSale.getOrderItemId());
        OrderEntity order = orderMap.get(orderItem.getOrderId());
        afterSaleWithOrderDTO.setOrderType(order.getOrderType());
        afterSaleWithOrderDTO.setCombineOrderId(order.getCombineOrderId());
        afterSaleWithOrderDTO.setWarehouseType(order.getWarehouseType());
        afterSaleWithOrderDTO.setOrderNo(order.getOrderNo());

        afterSaleWithOrderDTO.setTitle(orderItemSnapshot.getTitle());
        afterSaleWithOrderDTO.setMainPicture(orderItemSnapshot.getMainPicture());
        afterSaleWithOrderDTO.setSpecification(orderItemSnapshot.getSpecification());
        afterSaleWithOrderDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
        afterSaleWithOrderDTO.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
        afterSaleWithOrderDTO.setGoodsType(orderItemSnapshot.getGoodsType());
        afterSaleWithOrderDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
        afterSaleWithOrderDTO.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
        afterSaleWithOrderDTO.setMaxAfterSaleAmount(orderItemSnapshot.getMaxAfterSaleAmount());
        afterSaleWithOrderDTO.setSkuId(orderItemSnapshot.getSkuId());
        afterSaleWithOrderDTO.setPresaleSwitch(orderItemSnapshot.getPresaleSwitch());

        afterSaleWithOrderDTO.setOrderAmount(orderItem.getAmount());
        afterSaleWithOrderDTO.setOrderPrice(orderItem.getTotalPrice());
        afterSaleWithOrderDTO.setPrice(orderItem.getPayablePrice());
        afterSaleWithOrderDTO.setPayablePrice(orderItem.getPayablePrice());
        afterSaleWithOrderDTO.setItemId(orderItem.getItemId());
        return afterSaleWithOrderDTO;
    }

    public static QueryBillOrderAfterSaleParam convertToQueryBillOrderAfterSaleParam(QueryBillOrderAfterSaleReq billOrderAfterSaleReq) {


        if (billOrderAfterSaleReq == null) {
            return null;
        }
        QueryBillOrderAfterSaleParam queryBillOrderAfterSaleParam = new QueryBillOrderAfterSaleParam();
        queryBillOrderAfterSaleParam.setTenantId(billOrderAfterSaleReq.getTenantId());
        queryBillOrderAfterSaleParam.setStartTime(billOrderAfterSaleReq.getStartTime());
        queryBillOrderAfterSaleParam.setEndTime(billOrderAfterSaleReq.getEndTime());
        return queryBillOrderAfterSaleParam;
    }

    public static OrderAfterSaleEntity convertToEntity(OrderAfterSaleUpdateReq addReq) {

        if (addReq == null) {
            return null;
        }
        OrderAfterSaleEntity orderAfterSaleEntity = new OrderAfterSaleEntity();
        orderAfterSaleEntity.setId(addReq.getId());
        orderAfterSaleEntity.setTenantId(addReq.getTenantId());
        orderAfterSaleEntity.setOrderId(addReq.getOrderId());
        orderAfterSaleEntity.setOrderItemId(addReq.getOrderItemId());
        orderAfterSaleEntity.setStoreId(addReq.getStoreId());
        orderAfterSaleEntity.setAccountId(addReq.getAccountId());
        orderAfterSaleEntity.setAfterSaleOrderNo(addReq.getAfterSaleOrderNo());
        orderAfterSaleEntity.setAmount(addReq.getAmount());
        orderAfterSaleEntity.setAfterSaleType(addReq.getAfterSaleType());
        orderAfterSaleEntity.setServiceType(addReq.getServiceType());
        orderAfterSaleEntity.setApplyPrice(addReq.getApplyPrice());
        orderAfterSaleEntity.setTotalPrice(addReq.getTotalPrice());
        orderAfterSaleEntity.setDeliveryFee(addReq.getDeliveryFee());
        orderAfterSaleEntity.setReason(addReq.getReason());
        orderAfterSaleEntity.setUserRemark(addReq.getUserRemark());
        orderAfterSaleEntity.setProofPicture(addReq.getProofPicture());
        orderAfterSaleEntity.setStatus(addReq.getStatus());
        orderAfterSaleEntity.setHandleRemark(addReq.getHandleRemark());
        orderAfterSaleEntity.setOperatorName(addReq.getOperatorName());
        orderAfterSaleEntity.setCreateTime(addReq.getCreateTime());
        orderAfterSaleEntity.setUpdateTime(addReq.getUpdateTime());
        orderAfterSaleEntity.setFinishedTime(addReq.getFinishedTime());
        orderAfterSaleEntity.setHandleTime(addReq.getHandleTime());
        orderAfterSaleEntity.setRecycleTime(addReq.getRecycleTime());
        orderAfterSaleEntity.setRecycleDetails(addReq.getRecycleDetails());
        orderAfterSaleEntity.setResponsibilityType(addReq.getResponsibilityType());
        orderAfterSaleEntity.setStoreNo(addReq.getStoreNo());
        orderAfterSaleEntity.setWarehouseType(addReq.getWarehouseType());
        orderAfterSaleEntity.setAutoFinishedTime(addReq.getAutoFinishedTime());
        orderAfterSaleEntity.setApplyQuantity(addReq.getApplyQuantity());
        orderAfterSaleEntity.setAdminRemark(addReq.getAdminRemark());
        orderAfterSaleEntity.setAdminRemarkTime(addReq.getAdminRemarkTime());
        orderAfterSaleEntity.setSecondHandleRemark(addReq.getSecondHandleRemark());
        orderAfterSaleEntity.setReturnAddressId(addReq.getReturnAddressId());
        orderAfterSaleEntity.setReturnWarehouseNo(addReq.getReturnWarehouseNo());
        orderAfterSaleEntity.setCustomerAfterSaleOrderNo(addReq.getCustomerAfterSaleOrderNo());
        orderAfterSaleEntity.setServiceProviderAuditTime(addReq.getServiceProviderAuditTime());
        orderAfterSaleEntity.setRecyclePicture(addReq.getRecyclePicture());
        orderAfterSaleEntity.setReqSource(addReq.getReqSource());
// Not mapped TO fields:
// recycleQuantityDetail
// Not mapped FROM fields:
// orderNo
        return orderAfterSaleEntity;
    }

    public static OrderAfterSaleEntity convertToEntity(OrderAfterSaleAddReq addReq) {

        if (addReq == null) {
            return null;
        }
        OrderAfterSaleEntity orderAfterSaleEntity = new OrderAfterSaleEntity();
        orderAfterSaleEntity.setId(addReq.getId());
        orderAfterSaleEntity.setTenantId(addReq.getTenantId());
        orderAfterSaleEntity.setOrderId(addReq.getOrderId());
        orderAfterSaleEntity.setOrderItemId(addReq.getOrderItemId());
        orderAfterSaleEntity.setStoreId(addReq.getStoreId());
        orderAfterSaleEntity.setAccountId(addReq.getAccountId());
        orderAfterSaleEntity.setAfterSaleOrderNo(addReq.getAfterSaleOrderNo());
        orderAfterSaleEntity.setAmount(addReq.getAmount());
        orderAfterSaleEntity.setAfterSaleType(addReq.getAfterSaleType());
        orderAfterSaleEntity.setServiceType(addReq.getServiceType());
        orderAfterSaleEntity.setApplyPrice(addReq.getApplyPrice());
        orderAfterSaleEntity.setTotalPrice(addReq.getTotalPrice());
        orderAfterSaleEntity.setDeliveryFee(addReq.getDeliveryFee());
        orderAfterSaleEntity.setReason(addReq.getReason());
        orderAfterSaleEntity.setUserRemark(addReq.getUserRemark());
        orderAfterSaleEntity.setProofPicture(addReq.getProofPicture());
        orderAfterSaleEntity.setStatus(addReq.getStatus());
        orderAfterSaleEntity.setHandleRemark(addReq.getHandleRemark());
        orderAfterSaleEntity.setOperatorName(addReq.getOperatorName());
        orderAfterSaleEntity.setCreateTime(addReq.getCreateTime());
        orderAfterSaleEntity.setUpdateTime(addReq.getUpdateTime());
        orderAfterSaleEntity.setFinishedTime(addReq.getFinishedTime());
        orderAfterSaleEntity.setHandleTime(addReq.getHandleTime());
        orderAfterSaleEntity.setRecycleTime(addReq.getRecycleTime());
        orderAfterSaleEntity.setRecycleDetails(addReq.getRecycleDetails());
        orderAfterSaleEntity.setResponsibilityType(addReq.getResponsibilityType());
        orderAfterSaleEntity.setStoreNo(addReq.getStoreNo());
        orderAfterSaleEntity.setWarehouseType(addReq.getWarehouseType());
        orderAfterSaleEntity.setAutoFinishedTime(addReq.getAutoFinishedTime());
        orderAfterSaleEntity.setApplyQuantity(addReq.getApplyQuantity());
        orderAfterSaleEntity.setAdminRemark(addReq.getAdminRemark());
        orderAfterSaleEntity.setAdminRemarkTime(addReq.getAdminRemarkTime());
        orderAfterSaleEntity.setSecondHandleRemark(addReq.getSecondHandleRemark());
        orderAfterSaleEntity.setReturnAddressId(addReq.getReturnAddressId());
        orderAfterSaleEntity.setReturnWarehouseNo(addReq.getReturnWarehouseNo());
        orderAfterSaleEntity.setCustomerAfterSaleOrderNo(addReq.getCustomerAfterSaleOrderNo());
        orderAfterSaleEntity.setServiceProviderAuditTime(addReq.getServiceProviderAuditTime());
        orderAfterSaleEntity.setRecyclePicture(addReq.getRecyclePicture());
        orderAfterSaleEntity.setReqSource(addReq.getReqSource());
// Not mapped TO fields:
// recycleQuantityDetail
// Not mapped FROM fields:
// orderNo
        return orderAfterSaleEntity;
    }

    public static List<OrderAfterSaleEntity> convertPOToEntityList(List<OrderAfterSale> afterSales) {

        if (afterSales == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleEntity> orderAfterSaleEntityList = new ArrayList<>();
        for (OrderAfterSale orderAfterSale : afterSales) {
            orderAfterSaleEntityList.add(toOrderAfterSaleEntity(orderAfterSale));
        }
        return orderAfterSaleEntityList;
    }


    public static List<OrderAfterSaleEntity> convertToEntityList(List<OrderAfterSaleAddReq> addReqs) {

        if (addReqs == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleEntity> orderAfterSaleEntityList = new ArrayList<>();
        for (OrderAfterSaleAddReq orderAfterSaleAddReq : addReqs) {
            orderAfterSaleEntityList.add(convertToEntity(orderAfterSaleAddReq));
        }
        return orderAfterSaleEntityList;
    }

    public static OrderAfterSaleEntity buildOrderAfterSaleDTO(OrderEntity combineOrder, OrderItemEntity orderItem, Integer afterSaleType, Integer serviceType, String reason) {
        OrderAfterSaleEntity orderAfterSale = new OrderAfterSaleEntity();
        orderAfterSale.setTenantId(combineOrder.getTenantId());
        orderAfterSale.setOrderId(combineOrder.getId());
        orderAfterSale.setOrderItemId(orderItem.getId());
        orderAfterSale.setAmount(orderItem.getAmount());
        orderAfterSale.setAfterSaleType(afterSaleType);
        orderAfterSale.setServiceType(serviceType);
        orderAfterSale.setApplyPrice(orderItem.getTotalPrice());
        orderAfterSale.setWarehouseType(combineOrder.getWarehouseType());
        orderAfterSale.setReason(reason);
        orderAfterSale.setAutoFinishedTime(LocalDateTime.now().plusDays(combineOrder.getAutoFinishedTime()));

        return orderAfterSale;
    }

    public static OrderAfterSaleStatusUpdateCommandParam convertToStatusUpdateCommandParam(OrderAfterSaleStatusUpdateReq statusUpdateReq) {

        if (statusUpdateReq == null) {
            return null;
        }
        OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateCommandParam = new OrderAfterSaleStatusUpdateCommandParam();
        orderAfterSaleStatusUpdateCommandParam.setAfterSaleId(statusUpdateReq.getAfterSaleId());
        orderAfterSaleStatusUpdateCommandParam.setSourceStatus(statusUpdateReq.getSourceStatus());
        orderAfterSaleStatusUpdateCommandParam.setTargetStatus(statusUpdateReq.getTargetStatus());
        orderAfterSaleStatusUpdateCommandParam.setHandleRemark(statusUpdateReq.getHandleRemark());
        return orderAfterSaleStatusUpdateCommandParam;
    }

    public static List<OrderAfterSaleProcessFinishParam> convertToFinishParam(List<OrderAfterSaleProcessFinishReq> finishReqs) {

        if (finishReqs == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleProcessFinishParam> orderAfterSaleProcessFinishParamList = new ArrayList<>();
        for (OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishReq : finishReqs) {
            orderAfterSaleProcessFinishParamList.add(toOrderAfterSaleProcessFinishParam(orderAfterSaleProcessFinishReq));
        }
        return orderAfterSaleProcessFinishParamList;
    }

    public static OrderAfterSaleProcessFinishParam toOrderAfterSaleProcessFinishParam(OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishReq) {
        if (orderAfterSaleProcessFinishReq == null) {
            return null;
        }
        OrderAfterSaleProcessFinishParam orderAfterSaleProcessFinishParam = new OrderAfterSaleProcessFinishParam();
        orderAfterSaleProcessFinishParam.setOrderAfterSaleNo(orderAfterSaleProcessFinishReq.getOrderAfterSaleNo());
        orderAfterSaleProcessFinishParam.setSku(orderAfterSaleProcessFinishReq.getSku());
        orderAfterSaleProcessFinishParam.setShouldCount(orderAfterSaleProcessFinishReq.getShouldCount());
        orderAfterSaleProcessFinishParam.setDeliveryType(orderAfterSaleProcessFinishReq.getDeliveryType());
        orderAfterSaleProcessFinishParam.setShortCount(orderAfterSaleProcessFinishReq.getShortCount());
        orderAfterSaleProcessFinishParam.setState(orderAfterSaleProcessFinishReq.getState());
        orderAfterSaleProcessFinishParam.setRemark(orderAfterSaleProcessFinishReq.getRemark());
        orderAfterSaleProcessFinishParam.setRecyclePicture(orderAfterSaleProcessFinishReq.getRecyclePicture());
        orderAfterSaleProcessFinishParam.setRecycleDetailMessage(orderAfterSaleProcessFinishReq.getRecycleDetailMessage());
        orderAfterSaleProcessFinishParam.setRecycleQuantityDetail(orderAfterSaleProcessFinishReq.getRecycleQuantityDetail());
        orderAfterSaleProcessFinishParam.setItemFinishType(orderAfterSaleProcessFinishReq.getItemFinishType());
        return orderAfterSaleProcessFinishParam;
    }

    public static OrderAfterSaleAuditCommandParam convertToAuditCommandParam(OrderAfterSaleAuditReq auditReq) {

        if (auditReq == null) {
            return null;
        }
        OrderAfterSaleAuditCommandParam orderAfterSaleAuditCommandParam = new OrderAfterSaleAuditCommandParam();
        orderAfterSaleAuditCommandParam.setAfterSaleOrderNo(auditReq.getAfterSaleOrderNo());
        orderAfterSaleAuditCommandParam.setOrderAfterSaleId(auditReq.getOrderAfterSaleId());
        orderAfterSaleAuditCommandParam.setHandleRemark(auditReq.getHandleRemark());
        orderAfterSaleAuditCommandParam.setAuditStatus(auditReq.getAuditStatus());
        orderAfterSaleAuditCommandParam.setRecycleTime(auditReq.getRecycleTime());
        orderAfterSaleAuditCommandParam.setAmount(auditReq.getAmount());
        orderAfterSaleAuditCommandParam.setTotalPrice(auditReq.getTotalPrice());
        orderAfterSaleAuditCommandParam.setOperatorName(auditReq.getOperatorName());
        orderAfterSaleAuditCommandParam.setResponsibilityType(auditReq.getResponsibilityType());
        orderAfterSaleAuditCommandParam.setDeliveryType(auditReq.getDeliveryType());
        orderAfterSaleAuditCommandParam.setDeliveryCompany(auditReq.getDeliveryCompany());
        orderAfterSaleAuditCommandParam.setDeliveryNo(auditReq.getDeliveryNo());
        orderAfterSaleAuditCommandParam.setRemark(auditReq.getRemark());
        orderAfterSaleAuditCommandParam.setQuantity(auditReq.getQuantity());
        orderAfterSaleAuditCommandParam.setActualQuantity(auditReq.getActualQuantity());
        orderAfterSaleAuditCommandParam.setReturnAddressId(auditReq.getReturnAddressId());
        orderAfterSaleAuditCommandParam.setReturnWarehouseNo(auditReq.getReturnWarehouseNo());
        orderAfterSaleAuditCommandParam.setSupplierApplyPrice(auditReq.getSupplierApplyPrice());
        orderAfterSaleAuditCommandParam.setSupplierTotalRefundPrice(auditReq.getSupplierTotalRefundPrice());
        orderAfterSaleAuditCommandParam.setSystemSource(auditReq.getSystemSource());
        orderAfterSaleAuditCommandParam.setWarehouseType(auditReq.getWarehouseType());
        orderAfterSaleAuditCommandParam.setGoodsType(auditReq.getGoodsType());
        orderAfterSaleAuditCommandParam.setNeedServiceProviderAudit(auditReq.isNeedServiceProviderAudit());
        orderAfterSaleAuditCommandParam.setRefundReceipt(auditReq.getRefundReceipt());
        return orderAfterSaleAuditCommandParam;
    }

    public static OrderAfterSaleEntity convertToEntity(OrderAfterSaleDTO orderAfterSaleDTO) {


        if (orderAfterSaleDTO == null) {
            return null;
        }
        OrderAfterSaleEntity orderAfterSaleEntity = new OrderAfterSaleEntity();
        orderAfterSaleEntity.setId(orderAfterSaleDTO.getId());
        orderAfterSaleEntity.setTenantId(orderAfterSaleDTO.getTenantId());
        orderAfterSaleEntity.setOrderId(orderAfterSaleDTO.getOrderId());
        orderAfterSaleEntity.setOrderItemId(orderAfterSaleDTO.getOrderItemId());
        orderAfterSaleEntity.setStoreId(orderAfterSaleDTO.getStoreId());
        orderAfterSaleEntity.setAccountId(orderAfterSaleDTO.getAccountId());
        orderAfterSaleEntity.setAfterSaleOrderNo(orderAfterSaleDTO.getAfterSaleOrderNo());
        orderAfterSaleEntity.setAmount(orderAfterSaleDTO.getAmount());
        orderAfterSaleEntity.setAfterSaleType(orderAfterSaleDTO.getAfterSaleType());
        orderAfterSaleEntity.setServiceType(orderAfterSaleDTO.getServiceType());
        orderAfterSaleEntity.setApplyPrice(orderAfterSaleDTO.getApplyPrice());
        orderAfterSaleEntity.setTotalPrice(orderAfterSaleDTO.getTotalPrice());
        orderAfterSaleEntity.setDeliveryFee(orderAfterSaleDTO.getDeliveryFee());
        orderAfterSaleEntity.setReason(orderAfterSaleDTO.getReason());
        orderAfterSaleEntity.setUserRemark(orderAfterSaleDTO.getUserRemark());
        orderAfterSaleEntity.setProofPicture(orderAfterSaleDTO.getProofPicture());
        orderAfterSaleEntity.setStatus(orderAfterSaleDTO.getStatus());
        orderAfterSaleEntity.setHandleRemark(orderAfterSaleDTO.getHandleRemark());
        orderAfterSaleEntity.setOperatorName(orderAfterSaleDTO.getOperatorName());
        orderAfterSaleEntity.setCreateTime(orderAfterSaleDTO.getCreateTime());
        orderAfterSaleEntity.setUpdateTime(orderAfterSaleDTO.getUpdateTime());
        orderAfterSaleEntity.setFinishedTime(orderAfterSaleDTO.getFinishedTime());
        orderAfterSaleEntity.setHandleTime(orderAfterSaleDTO.getHandleTime());
        orderAfterSaleEntity.setRecycleTime(orderAfterSaleDTO.getRecycleTime());
        orderAfterSaleEntity.setRecycleDetails(orderAfterSaleDTO.getRecycleDetails());
        orderAfterSaleEntity.setResponsibilityType(orderAfterSaleDTO.getResponsibilityType());
        orderAfterSaleEntity.setStoreNo(orderAfterSaleDTO.getStoreNo());
        orderAfterSaleEntity.setWarehouseType(orderAfterSaleDTO.getWarehouseType());
        orderAfterSaleEntity.setAutoFinishedTime(orderAfterSaleDTO.getAutoFinishedTime());
        orderAfterSaleEntity.setApplyQuantity(orderAfterSaleDTO.getApplyQuantity());
        orderAfterSaleEntity.setAdminRemark(orderAfterSaleDTO.getAdminRemark());
        orderAfterSaleEntity.setAdminRemarkTime(orderAfterSaleDTO.getAdminRemarkTime());
        orderAfterSaleEntity.setSecondHandleRemark(orderAfterSaleDTO.getSecondHandleRemark());
        orderAfterSaleEntity.setReturnAddressId(orderAfterSaleDTO.getReturnAddressId());
        orderAfterSaleEntity.setReturnWarehouseNo(orderAfterSaleDTO.getReturnWarehouseNo());
        orderAfterSaleEntity.setCustomerAfterSaleOrderNo(orderAfterSaleDTO.getCustomerAfterSaleOrderNo());
        orderAfterSaleEntity.setServiceProviderAuditTime(orderAfterSaleDTO.getServiceProviderAuditTime());
        orderAfterSaleEntity.setRecyclePicture(orderAfterSaleDTO.getRecyclePicture());
        orderAfterSaleEntity.setReqSource(orderAfterSaleDTO.getReqSource());
// Not mapped TO fields:
// recycleQuantityDetail
// Not mapped FROM fields:
// orderNo
        return orderAfterSaleEntity;
    }

    public static List<OrderAfterSale> convertToPOList(List<OrderAfterSaleEntity> afterSaleEntities) {

        if (afterSaleEntities == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSale> orderAfterSaleList = new ArrayList<>();
        for (OrderAfterSaleEntity orderAfterSaleEntity : afterSaleEntities) {
            orderAfterSaleList.add(convertToPO(orderAfterSaleEntity));
        }
        return orderAfterSaleList;
    }

    public static OrderAfterSale convertToPO(OrderAfterSaleEntity afterSaleEntity) {

        if (afterSaleEntity == null) {
            return null;
        }
        OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(afterSaleEntity.getId());
        orderAfterSale.setTenantId(afterSaleEntity.getTenantId());
        orderAfterSale.setOrderId(afterSaleEntity.getOrderId());
        orderAfterSale.setOrderItemId(afterSaleEntity.getOrderItemId());
        orderAfterSale.setStoreId(afterSaleEntity.getStoreId());
        orderAfterSale.setAccountId(afterSaleEntity.getAccountId());
        orderAfterSale.setAfterSaleOrderNo(afterSaleEntity.getAfterSaleOrderNo());
        orderAfterSale.setAmount(afterSaleEntity.getAmount());
        orderAfterSale.setAfterSaleType(afterSaleEntity.getAfterSaleType());
        orderAfterSale.setServiceType(afterSaleEntity.getServiceType());
        orderAfterSale.setApplyPrice(afterSaleEntity.getApplyPrice());
        orderAfterSale.setTotalPrice(afterSaleEntity.getTotalPrice());
        orderAfterSale.setDeliveryFee(afterSaleEntity.getDeliveryFee());
        orderAfterSale.setReason(afterSaleEntity.getReason());
        orderAfterSale.setUserRemark(afterSaleEntity.getUserRemark());
        orderAfterSale.setProofPicture(afterSaleEntity.getProofPicture());
        orderAfterSale.setStatus(afterSaleEntity.getStatus());
        orderAfterSale.setHandleRemark(afterSaleEntity.getHandleRemark());
        orderAfterSale.setOperatorName(afterSaleEntity.getOperatorName());
        orderAfterSale.setCreateTime(afterSaleEntity.getCreateTime());
        orderAfterSale.setUpdateTime(afterSaleEntity.getUpdateTime());
        orderAfterSale.setFinishedTime(afterSaleEntity.getFinishedTime());
        orderAfterSale.setHandleTime(afterSaleEntity.getHandleTime());
        orderAfterSale.setRecycleTime(afterSaleEntity.getRecycleTime());
        orderAfterSale.setRecycleDetails(afterSaleEntity.getRecycleDetails());
        orderAfterSale.setResponsibilityType(afterSaleEntity.getResponsibilityType());
        orderAfterSale.setStoreNo(afterSaleEntity.getStoreNo());
        orderAfterSale.setWarehouseType(afterSaleEntity.getWarehouseType());
        orderAfterSale.setAutoFinishedTime(afterSaleEntity.getAutoFinishedTime());
        orderAfterSale.setApplyQuantity(afterSaleEntity.getApplyQuantity());
        orderAfterSale.setAdminRemark(afterSaleEntity.getAdminRemark());
        orderAfterSale.setAdminRemarkTime(afterSaleEntity.getAdminRemarkTime());
        orderAfterSale.setSecondHandleRemark(afterSaleEntity.getSecondHandleRemark());
        orderAfterSale.setReturnAddressId(afterSaleEntity.getReturnAddressId());
        orderAfterSale.setReturnWarehouseNo(afterSaleEntity.getReturnWarehouseNo());
        orderAfterSale.setCustomerAfterSaleOrderNo(afterSaleEntity.getCustomerAfterSaleOrderNo());
        orderAfterSale.setServiceProviderAuditTime(afterSaleEntity.getServiceProviderAuditTime());
        orderAfterSale.setRecyclePicture(afterSaleEntity.getRecyclePicture());
        orderAfterSale.setRecycleQuantityDetail(afterSaleEntity.getRecycleQuantityDetail());
// Not mapped FROM fields:
// reqSource
        return orderAfterSale;
    }

    public static OrderAfterSaleEntity toOrderAfterSaleEntity(OrderAfterSale orderAfterSale) {
        if (orderAfterSale == null) {
            return null;
        }
        OrderAfterSaleEntity orderAfterSaleEntity = new OrderAfterSaleEntity();
        orderAfterSaleEntity.setId(orderAfterSale.getId());
        orderAfterSaleEntity.setTenantId(orderAfterSale.getTenantId());
        orderAfterSaleEntity.setOrderId(orderAfterSale.getOrderId());
        orderAfterSaleEntity.setOrderItemId(orderAfterSale.getOrderItemId());
        orderAfterSaleEntity.setStoreId(orderAfterSale.getStoreId());
        orderAfterSaleEntity.setAccountId(orderAfterSale.getAccountId());
        orderAfterSaleEntity.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
        orderAfterSaleEntity.setAmount(orderAfterSale.getAmount());
        orderAfterSaleEntity.setAfterSaleType(orderAfterSale.getAfterSaleType());
        orderAfterSaleEntity.setServiceType(orderAfterSale.getServiceType());
        orderAfterSaleEntity.setApplyPrice(orderAfterSale.getApplyPrice());
        orderAfterSaleEntity.setTotalPrice(orderAfterSale.getTotalPrice());
        orderAfterSaleEntity.setDeliveryFee(orderAfterSale.getDeliveryFee());
        orderAfterSaleEntity.setReason(orderAfterSale.getReason());
        orderAfterSaleEntity.setUserRemark(orderAfterSale.getUserRemark());
        orderAfterSaleEntity.setProofPicture(orderAfterSale.getProofPicture());
        orderAfterSaleEntity.setStatus(orderAfterSale.getStatus());
        orderAfterSaleEntity.setHandleRemark(orderAfterSale.getHandleRemark());
        orderAfterSaleEntity.setOperatorName(orderAfterSale.getOperatorName());
        orderAfterSaleEntity.setCreateTime(orderAfterSale.getCreateTime());
        orderAfterSaleEntity.setUpdateTime(orderAfterSale.getUpdateTime());
        orderAfterSaleEntity.setFinishedTime(orderAfterSale.getFinishedTime());
        orderAfterSaleEntity.setHandleTime(orderAfterSale.getHandleTime());
        orderAfterSaleEntity.setRecycleTime(orderAfterSale.getRecycleTime());
        orderAfterSaleEntity.setRecycleDetails(orderAfterSale.getRecycleDetails());
        orderAfterSaleEntity.setResponsibilityType(orderAfterSale.getResponsibilityType());
        orderAfterSaleEntity.setStoreNo(orderAfterSale.getStoreNo());
        orderAfterSaleEntity.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleEntity.setAutoFinishedTime(orderAfterSale.getAutoFinishedTime());
        orderAfterSaleEntity.setApplyQuantity(orderAfterSale.getApplyQuantity());
        orderAfterSaleEntity.setAdminRemark(orderAfterSale.getAdminRemark());
        orderAfterSaleEntity.setAdminRemarkTime(orderAfterSale.getAdminRemarkTime());
        orderAfterSaleEntity.setSecondHandleRemark(orderAfterSale.getSecondHandleRemark());
        orderAfterSaleEntity.setReturnAddressId(orderAfterSale.getReturnAddressId());
        orderAfterSaleEntity.setReturnWarehouseNo(orderAfterSale.getReturnWarehouseNo());
        orderAfterSaleEntity.setCustomerAfterSaleOrderNo(orderAfterSale.getCustomerAfterSaleOrderNo());
        orderAfterSaleEntity.setServiceProviderAuditTime(orderAfterSale.getServiceProviderAuditTime());
        orderAfterSaleEntity.setRecyclePicture(orderAfterSale.getRecyclePicture());
        orderAfterSaleEntity.setRecycleQuantityDetail(orderAfterSale.getRecycleQuantityDetail());
// Not mapped TO fields:
// reqSource
        return orderAfterSaleEntity;
    }

    public static OrderAfterSaleModifyQuantityParam convertToParam(OrderAfterSaleModifyQuantityReq req) {

        if (req == null) {
            return null;
        }
        OrderAfterSaleModifyQuantityParam orderAfterSaleModifyQuantityParam = new OrderAfterSaleModifyQuantityParam();
        orderAfterSaleModifyQuantityParam.setAfterSaleId(req.getAfterSaleId());
        orderAfterSaleModifyQuantityParam.setOperatorName(req.getOperatorName());
        orderAfterSaleModifyQuantityParam.setUpdateTime(req.getUpdateTime());
        orderAfterSaleModifyQuantityParam.setAmount(req.getAmount());
        orderAfterSaleModifyQuantityParam.setApplyPrice(req.getApplyPrice());
        orderAfterSaleModifyQuantityParam.setTotalPrice(req.getTotalPrice());
        return orderAfterSaleModifyQuantityParam;
    }

    public static OrderAfterSaleRecycleFailRefundParam convertToParam(OrderAfterSaleRecycleFailRefundReq recycleFailRefundReq) {

        if (recycleFailRefundReq == null) {
            return null;
        }
        OrderAfterSaleRecycleFailRefundParam orderAfterSaleRecycleFailRefundParam = new OrderAfterSaleRecycleFailRefundParam();
        orderAfterSaleRecycleFailRefundParam.setAfterSaleId(recycleFailRefundReq.getAfterSaleId());
        orderAfterSaleRecycleFailRefundParam.setTotalPrice(recycleFailRefundReq.getTotalPrice());
        return orderAfterSaleRecycleFailRefundParam;
    }
}
