package com.cosfo.ordercenter.application.inbound.provider.aftersale.converter;

import com.cosfo.ordercenter.client.req.OrderAfterSaleRuleCommandReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleRuleResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderAfterSaleRuleConverter {
    private OrderAfterSaleRuleConverter() {
    }

    public static List<OrderAfterSaleRuleResp> convertToRespList(List<OrderAfterSaleRuleEntity> afterSaleRuleEntities) {

        if (afterSaleRuleEntities == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleRuleResp> orderAfterSaleRuleRespList = new ArrayList<>();
        for (OrderAfterSaleRuleEntity orderAfterSaleRuleEntity : afterSaleRuleEntities) {
            orderAfterSaleRuleRespList.add(toOrderAfterSaleRuleResp(orderAfterSaleRuleEntity));
        }
        return orderAfterSaleRuleRespList;
    }

    public static OrderAfterSaleRuleResp toOrderAfterSaleRuleResp(OrderAfterSaleRuleEntity orderAfterSaleRuleEntity) {
        if (orderAfterSaleRuleEntity == null) {
            return null;
        }
        OrderAfterSaleRuleResp orderAfterSaleRuleResp = new OrderAfterSaleRuleResp();
        orderAfterSaleRuleResp.setId(orderAfterSaleRuleEntity.getId());
        orderAfterSaleRuleResp.setTenantId(orderAfterSaleRuleEntity.getTenantId());
        orderAfterSaleRuleResp.setDeliveryType(orderAfterSaleRuleEntity.getDeliveryType());
        orderAfterSaleRuleResp.setApplyEndTime(orderAfterSaleRuleEntity.getApplyEndTime());
        orderAfterSaleRuleResp.setAutoFinishedTime(orderAfterSaleRuleEntity.getAutoFinishedTime());
        orderAfterSaleRuleResp.setDealType(orderAfterSaleRuleEntity.getDealType());
        orderAfterSaleRuleResp.setCreateTime(orderAfterSaleRuleEntity.getCreateTime());
        orderAfterSaleRuleResp.setUpdateTime(orderAfterSaleRuleEntity.getUpdateTime());
        orderAfterSaleRuleResp.setType(orderAfterSaleRuleEntity.getType());
        orderAfterSaleRuleResp.setDefaultFlag(orderAfterSaleRuleEntity.getDefaultFlag());
        orderAfterSaleRuleResp.setRule(orderAfterSaleRuleEntity.getRule());
        return orderAfterSaleRuleResp;
    }


    public static OrderAfterSaleRuleCommandParam convertToCommandParam(OrderAfterSaleRuleCommandReq commandReq) {

        if (commandReq == null) {
            return null;
        }
        OrderAfterSaleRuleCommandParam orderAfterSaleRuleCommandParam = new OrderAfterSaleRuleCommandParam();
        orderAfterSaleRuleCommandParam.setId(commandReq.getId());
        orderAfterSaleRuleCommandParam.setTenantId(commandReq.getTenantId());
        orderAfterSaleRuleCommandParam.setDeliveryType(commandReq.getDeliveryType());
        orderAfterSaleRuleCommandParam.setApplyEndTime(commandReq.getApplyEndTime());
        orderAfterSaleRuleCommandParam.setAutoFinishedTime(commandReq.getAutoFinishedTime());
        orderAfterSaleRuleCommandParam.setDealType(commandReq.getDealType());
        orderAfterSaleRuleCommandParam.setCreateTime(commandReq.getCreateTime());
        orderAfterSaleRuleCommandParam.setUpdateTime(commandReq.getUpdateTime());
        orderAfterSaleRuleCommandParam.setType(commandReq.getType());
        orderAfterSaleRuleCommandParam.setDefaultFlag(commandReq.getDefaultFlag());
        orderAfterSaleRuleCommandParam.setRule(commandReq.getRule());
        return orderAfterSaleRuleCommandParam;
    }
}
