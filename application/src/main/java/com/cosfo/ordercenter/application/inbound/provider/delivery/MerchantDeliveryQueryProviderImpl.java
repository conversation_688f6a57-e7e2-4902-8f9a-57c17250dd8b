package com.cosfo.ordercenter.application.inbound.provider.delivery;

import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.MerchantDeliveryRuleConverter;
import com.cosfo.ordercenter.application.service.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.application.service.delivery.chain.DeliveryFeeHandlerChain;
import com.cosfo.ordercenter.client.provider.MerchantDeliveryProvider;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoResp;
import com.cosfo.ordercenter.client.validgroup.QueryRuleGroup;
import com.cosfo.ordercenter.common.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class MerchantDeliveryQueryProviderImpl implements MerchantDeliveryProvider {

    @Resource
    private DeliveryFeeService deliveryFeeService;
    @Resource
    private DeliveryFeeHandlerChain deliveryFeeHandlerChain;

    @Override
    public DubboResponse<List<MerchantDeliveryRuleInfoResp>> queryAllRuleList(MerchantDeliveryRuleQueryReq queryReq) {
        return DubboResponse.getOK(MerchantDeliveryRuleConverter.converterTORuleInfoRespList(deliveryFeeService.queryAllDeliveryRule(queryReq)));
    }


    @Override
    public DubboResponse<MerchantDeliveryFeeSnapshotResp> queryMerchantDeliveryFee(@Valid DeliveryTotalReq deliveryTotalReq) {
        // 校验参数有效性
        ValidatorUtils.validateEntity(deliveryTotalReq, QueryRuleGroup.class);

        MerchantDeliveryFeeSnapshotResp resp = deliveryFeeHandlerChain.executeChain(deliveryTotalReq);
        return DubboResponse.getOK(resp);
    }
}
