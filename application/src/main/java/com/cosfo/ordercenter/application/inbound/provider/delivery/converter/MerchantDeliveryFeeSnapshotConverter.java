package com.cosfo.ordercenter.application.inbound.provider.delivery.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;

/**
 * <AUTHOR>
 */
public class MerchantDeliveryFeeSnapshotConverter {
    private MerchantDeliveryFeeSnapshotConverter() {
    }

    public static MerchantDeliveryFeeSnapshotResp convertToSnapshotResp(MerchantDeliveryFeeSnapshotDTO snapshotDTO) {

        if (snapshotDTO == null) {
            return null;
        }
        MerchantDeliveryFeeSnapshotResp merchantDeliveryFeeSnapshotResp = new MerchantDeliveryFeeSnapshotResp();
        merchantDeliveryFeeSnapshotResp.setTenantId(snapshotDTO.getTenantId());
        merchantDeliveryFeeSnapshotResp.setOrderId(snapshotDTO.getOrderId());
        merchantDeliveryFeeSnapshotResp.setDeliveryFee(snapshotDTO.getDeliveryFee());
        merchantDeliveryFeeSnapshotResp.setScene(snapshotDTO.getScene());
        merchantDeliveryFeeSnapshotResp.setOrderInfo(snapshotDTO.getOrderInfo());
        merchantDeliveryFeeSnapshotResp.setRuleList(snapshotDTO.getRuleList());
        merchantDeliveryFeeSnapshotResp.setHitRuleList(snapshotDTO.getHitRuleList());
        merchantDeliveryFeeSnapshotResp.setRemark(snapshotDTO.getRemark());
        return merchantDeliveryFeeSnapshotResp;
    }

    public static OrderDeliveryFeeSnapshotEntity convertToEntity(MerchantDeliveryFeeSnapshotDTO snapshotDTO) {

        if (snapshotDTO == null) {
            return null;
        }

        OrderDeliveryFeeSnapshotEntity orderDeliveryFeeSnapshotEntity = new OrderDeliveryFeeSnapshotEntity();
        orderDeliveryFeeSnapshotEntity.setTenantId(snapshotDTO.getTenantId());
        orderDeliveryFeeSnapshotEntity.setOrderId(snapshotDTO.getOrderId());
        orderDeliveryFeeSnapshotEntity.setRemark(snapshotDTO.getRemark());
        orderDeliveryFeeSnapshotEntity.setScene(snapshotDTO.getScene());
        orderDeliveryFeeSnapshotEntity.setOrderDeliveryFee(snapshotDTO.getDeliveryFee());
        orderDeliveryFeeSnapshotEntity.setOrderInfo(JSON.toJSONString(snapshotDTO.getOrderInfo()));
        orderDeliveryFeeSnapshotEntity.setRuleInfo(JSON.toJSONString(snapshotDTO.getRuleList()));
        orderDeliveryFeeSnapshotEntity.setHitRuleFee(JSON.toJSONString(snapshotDTO.getHitRuleList()));

        return orderDeliveryFeeSnapshotEntity;
    }
}
