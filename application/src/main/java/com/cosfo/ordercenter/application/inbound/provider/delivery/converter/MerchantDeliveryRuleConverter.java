package com.cosfo.ordercenter.application.inbound.provider.delivery.converter;

import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoResp;
import com.cosfo.ordercenter.domain.delivery.param.MerchantDeliveryRuleQueryParam;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryFeeRule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MerchantDeliveryRuleConverter {
    private MerchantDeliveryRuleConverter() {
    }

    public static MerchantDeliveryRuleQueryParam converterQueryParam(MerchantDeliveryRuleQueryReq queryReq) {

        if (queryReq == null) {
            return null;
        }
        MerchantDeliveryRuleQueryParam merchantDeliveryRuleQueryParam = new MerchantDeliveryRuleQueryParam();
        merchantDeliveryRuleQueryParam.setTenantId(queryReq.getTenantId());
        merchantDeliveryRuleQueryParam.setWarehouseType(queryReq.getWarehouseType());
        merchantDeliveryRuleQueryParam.setWarehouseNo(queryReq.getWarehouseNo());
        merchantDeliveryRuleQueryParam.setDefaultType(queryReq.getDefaultType());
        return merchantDeliveryRuleQueryParam;
    }

    public static MerchantDeliveryRuleInfoResp convertToResp(MerchantDeliveryFeeRule deliveryFeeRule) {

        if (deliveryFeeRule == null) {
            return null;
        }
        MerchantDeliveryRuleInfoResp merchantDeliveryRuleInfoResp = new MerchantDeliveryRuleInfoResp();
        merchantDeliveryRuleInfoResp.setTenantId(deliveryFeeRule.getTenantId());
        merchantDeliveryRuleInfoResp.setRuleType(deliveryFeeRule.getRuleType());
        merchantDeliveryRuleInfoResp.setDefaultType(deliveryFeeRule.getDefaultType());
        merchantDeliveryRuleInfoResp.setPriority(deliveryFeeRule.getPriority());
        merchantDeliveryRuleInfoResp.setHitItemIds(deliveryFeeRule.getHitItemIds());
        merchantDeliveryRuleInfoResp.setIncludeNewFlag(deliveryFeeRule.getIncludeNewFlag());
        merchantDeliveryRuleInfoResp.setPriceType(deliveryFeeRule.getPriceType());
        merchantDeliveryRuleInfoResp.setRelateNumber(deliveryFeeRule.getRelateNumber());
// Not mapped TO fields:
// ruleId
// warehouseType
// deliveryType
// stepFeeDescList
// hitAreaList
// warehouseNo
// Not mapped FROM fields:
// id
// type
// deliveryFee
// freeDeliveryPrice
// createTime
// updateTime
// freeDeliveryType
// freeDeliveryQuantity
// hitAreas
        return merchantDeliveryRuleInfoResp;
    }

    public static List<MerchantDeliveryRuleInfoResp> converterTORuleInfoRespList(List<MerchantDeliveryRuleInfoDTO> ruleInfoDTOS) {

        if (ruleInfoDTOS == null) {
            return Collections.emptyList();
        }
        List<MerchantDeliveryRuleInfoResp> merchantDeliveryRuleInfoRespList = new ArrayList<>();
        for (MerchantDeliveryRuleInfoDTO merchantDeliveryRuleInfoDTO : ruleInfoDTOS) {
            merchantDeliveryRuleInfoRespList.add(toMerchantDeliveryRuleInfoResp(merchantDeliveryRuleInfoDTO));
        }
        return merchantDeliveryRuleInfoRespList;
    }

    public static MerchantDeliveryRuleInfoResp toMerchantDeliveryRuleInfoResp(MerchantDeliveryRuleInfoDTO merchantDeliveryRuleInfoDTO) {
        if (merchantDeliveryRuleInfoDTO == null) {
            return null;
        }
        MerchantDeliveryRuleInfoResp merchantDeliveryRuleInfoResp = new MerchantDeliveryRuleInfoResp();
        merchantDeliveryRuleInfoResp.setRuleId(merchantDeliveryRuleInfoDTO.getRuleId());
        merchantDeliveryRuleInfoResp.setTenantId(merchantDeliveryRuleInfoDTO.getTenantId());
        merchantDeliveryRuleInfoResp.setWarehouseType(merchantDeliveryRuleInfoDTO.getWarehouseType());
        merchantDeliveryRuleInfoResp.setRuleType(merchantDeliveryRuleInfoDTO.getRuleType());
        merchantDeliveryRuleInfoResp.setDefaultType(merchantDeliveryRuleInfoDTO.getDefaultType());
        merchantDeliveryRuleInfoResp.setDeliveryType(merchantDeliveryRuleInfoDTO.getDeliveryType());
        merchantDeliveryRuleInfoResp.setStepFeeDescList(merchantDeliveryRuleInfoDTO.getStepFeeDescList());
        merchantDeliveryRuleInfoResp.setPriority(merchantDeliveryRuleInfoDTO.getPriority());
        merchantDeliveryRuleInfoResp.setHitItemIds(merchantDeliveryRuleInfoDTO.getHitItemIds());
        merchantDeliveryRuleInfoResp.setHitAreaList(merchantDeliveryRuleInfoDTO.getHitAreaList());
        merchantDeliveryRuleInfoResp.setWarehouseNo(merchantDeliveryRuleInfoDTO.getWarehouseNo());
        merchantDeliveryRuleInfoResp.setIncludeNewFlag(merchantDeliveryRuleInfoDTO.getIncludeNewFlag());
        merchantDeliveryRuleInfoResp.setPriceType(merchantDeliveryRuleInfoDTO.getPriceType());
        merchantDeliveryRuleInfoResp.setRelateNumber(merchantDeliveryRuleInfoDTO.getRelateNumber());
        return merchantDeliveryRuleInfoResp;
    }

    public static DeliveryTotalDTO converterToDTO(DeliveryTotalReq totalReq) {

        if (totalReq == null) {
            return null;
        }
        DeliveryTotalDTO deliveryTotalDTO = new DeliveryTotalDTO();
        deliveryTotalDTO.setTenantId(totalReq.getTenantId());
        deliveryTotalDTO.setStoreId(totalReq.getStoreId());
        deliveryTotalDTO.setSupplierTenantId(totalReq.getSupplierTenantId());
        deliveryTotalDTO.setDeliveryTime(totalReq.getDeliveryTime());
        deliveryTotalDTO.setOrderInfoDTO(totalReq.getOrderInfoDTO());
        deliveryTotalDTO.setOrderItemInfoDTOList(totalReq.getOrderItemInfoDTOList());
        deliveryTotalDTO.setRelatedOrderInfoDTO(totalReq.getRelatedOrderInfoDTO());
        return deliveryTotalDTO;
    }

    public static MerchantDeliveryRuleInfoDTO converterToDTO(MerchantDeliveryFeeRule deliveryFeeRule) {

        if (deliveryFeeRule == null) {
            return null;
        }
        MerchantDeliveryRuleInfoDTO merchantDeliveryRuleInfoDTO = new MerchantDeliveryRuleInfoDTO();
        merchantDeliveryRuleInfoDTO.setRuleId(deliveryFeeRule.getId());
        merchantDeliveryRuleInfoDTO.setTenantId(deliveryFeeRule.getTenantId());
        merchantDeliveryRuleInfoDTO.setRuleType(deliveryFeeRule.getRuleType());
        merchantDeliveryRuleInfoDTO.setDefaultType(deliveryFeeRule.getDefaultType());
        merchantDeliveryRuleInfoDTO.setPriority(deliveryFeeRule.getPriority());
        merchantDeliveryRuleInfoDTO.setHitItemIds(deliveryFeeRule.getHitItemIds());
        merchantDeliveryRuleInfoDTO.setIncludeNewFlag(deliveryFeeRule.getIncludeNewFlag());
        merchantDeliveryRuleInfoDTO.setPriceType(deliveryFeeRule.getPriceType());
        merchantDeliveryRuleInfoDTO.setRelateNumber(deliveryFeeRule.getRelateNumber());
        merchantDeliveryRuleInfoDTO.setMatchRegionType(deliveryFeeRule.getMatchRegionType());
        merchantDeliveryRuleInfoDTO.setHitStoreIds(deliveryFeeRule.getHitStoreIds());
        merchantDeliveryRuleInfoDTO.setIncludeAllStoreFlag(deliveryFeeRule.getIncludeAllStoreFlag());
        merchantDeliveryRuleInfoDTO.setMatchItemType(deliveryFeeRule.getMatchItemType());
        merchantDeliveryRuleInfoDTO.setHitGoodsSource(deliveryFeeRule.getHitGoodsSource());
        merchantDeliveryRuleInfoDTO.setFulfillmentType(deliveryFeeRule.getFulfillmentType());
        merchantDeliveryRuleInfoDTO.setWarehouseType(deliveryFeeRule.getType());
        merchantDeliveryRuleInfoDTO.setDeliveryType(deliveryFeeRule.getFreeDeliveryType());
        merchantDeliveryRuleInfoDTO.setHitAreaList(deliveryFeeRule.getHitAreas());
// Not mapped TO fields:
// warehouseType
// deliveryType
// stepFeeDescList
// hitAreaList
// warehouseNo
// Not mapped FROM fields:
// id
// type
// deliveryFee
// freeDeliveryPrice
// createTime
// updateTime
// freeDeliveryType
// freeDeliveryQuantity
// hitAreas
        return merchantDeliveryRuleInfoDTO;
    }
}
