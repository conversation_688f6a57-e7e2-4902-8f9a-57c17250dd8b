package com.cosfo.ordercenter.application.inbound.provider.delivery.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;

/**
 * <AUTHOR>
 */
public class OrderDeliveryFeeSnapshotConverter {

    private OrderDeliveryFeeSnapshotConverter() {
    }

    public static OrderDeliveryFeeSnapshotEntity convertToEntity(MerchantDeliveryFeeSnapshotDTO snapshotDTO) {
        if (snapshotDTO == null) {
            return null;
        }
        OrderDeliveryFeeSnapshotEntity orderDeliveryFeeSnapshotEntity = new OrderDeliveryFeeSnapshotEntity();
        orderDeliveryFeeSnapshotEntity.setTenantId(snapshotDTO.getTenantId());
        orderDeliveryFeeSnapshotEntity.setOrderId(snapshotDTO.getOrderId());
        orderDeliveryFeeSnapshotEntity.setRemark(snapshotDTO.getRemark());
        orderDeliveryFeeSnapshotEntity.setScene(snapshotDTO.getScene());
        orderDeliveryFeeSnapshotEntity.setOrderDeliveryFee(snapshotDTO.getDeliveryFee());
        orderDeliveryFeeSnapshotEntity.setOrderInfo(JSON.toJSONString(snapshotDTO.getOrderInfo()));
        orderDeliveryFeeSnapshotEntity.setRuleInfo(JSON.toJSONString(snapshotDTO.getRuleList()));
        orderDeliveryFeeSnapshotEntity.setHitRuleFee(JSON.toJSONString(snapshotDTO.getHitRuleList()));

        return orderDeliveryFeeSnapshotEntity;
    }
}
