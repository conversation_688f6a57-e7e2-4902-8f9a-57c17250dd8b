package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.CombineOrderCommandService;
import com.cosfo.ordercenter.client.provider.CombineOrderCommandProvider;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@DubboService
public class CombineOrderCommandProviderImpl implements CombineOrderCommandProvider {

   @Resource
   private CombineOrderCommandService combineOrderCommandService;

    @Override
    public DubboResponse<Long> add(Long combineItemId, Long tenantId) {
        return DubboResponse.getOK(combineOrderCommandService.add(combineItemId, tenantId));
    }
}
