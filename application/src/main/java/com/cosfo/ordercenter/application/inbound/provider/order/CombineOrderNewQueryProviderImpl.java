package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.CombineOrderQueryService;
import com.cosfo.ordercenter.client.provider.CombineOrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class CombineOrderNewQueryProviderImpl implements CombineOrderQueryProvider {

    @Resource
    private CombineOrderQueryService combineOrderQueryService;

    @Override
    public DubboResponse<List<OrderResp>> queryByCombineId(Long combineItemId, Long tenantId) {
        return DubboResponse.getOK(combineOrderQueryService.queryByCombineId(combineItemId, tenantId));
    }

    @Override
    public DubboResponse<List<OrderResp>> queryByCombineIds(Collection<Long> combineItemId, Long tenantId) {
        return DubboResponse.getOK(combineOrderQueryService.queryByCombineIds(combineItemId, tenantId));
    }
}
