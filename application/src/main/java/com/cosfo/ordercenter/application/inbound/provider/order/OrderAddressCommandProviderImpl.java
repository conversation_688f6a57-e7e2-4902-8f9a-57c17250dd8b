package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderAddressConverter;
import com.cosfo.ordercenter.application.service.order.OrderAddressCommandService;
import com.cosfo.ordercenter.client.provider.OrderAddressCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAddressAddReq;
import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAddressCommandProviderImpl implements OrderAddressCommandProvider {

    @Resource
    private OrderAddressCommandService orderAddressCommandService;

    @Override
    public DubboResponse<Long> add(OrderAddressAddReq orderAddressAddReq) {
        OrderAddressCommandParam orderAddressCommandParam = OrderAddressConverter.convertToCommandParam(orderAddressAddReq);
        return DubboResponse.getOK(orderAddressCommandService.add(orderAddressCommandParam));
    }
}
