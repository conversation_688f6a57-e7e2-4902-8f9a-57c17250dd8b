package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.OrderAddressQueryService;
import com.cosfo.ordercenter.client.provider.OrderAddressQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderAddressNewQueryProviderImpl implements OrderAddressQueryProvider {

    @Resource
    private OrderAddressQueryService orderAddressQueryService;

    @Override
    public DubboResponse<OrderAddressResp> queryByOrderId(Long tenantId, Long orderId) {
        return DubboResponse.getOK(orderAddressQueryService.queryByOrderId(tenantId, orderId));
    }

    @Override
    public DubboResponse<List<OrderAddressResp>> queryByOrderIds(Long tenantId, List<Long> orderIds) {
        return DubboResponse.getOK(orderAddressQueryService.queryByOrderIds(tenantId, orderIds));
    }
}
