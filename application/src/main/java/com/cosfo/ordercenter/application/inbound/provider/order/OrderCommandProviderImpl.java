package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderParamConverter;
import com.cosfo.ordercenter.application.service.order.OrderCommandService;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.LockStockSuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderAuditReq;
import com.cosfo.ordercenter.client.req.event.OrderCancelReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.req.event.OrderConfirmReq;
import com.cosfo.ordercenter.client.req.event.OrderCreateReq;
import com.cosfo.ordercenter.client.req.event.OrderFulfillmentOrderCreateReq;
import com.cosfo.ordercenter.client.req.event.OrderPaySuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.domain.order.param.command.OrderCommandParam;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderCommandProviderImpl implements OrderCommandProvider {

    @Resource
    private OrderCommandService orderCommandService;

    @Override
    public DubboResponse<Boolean> updateById(OrderUpdateReq orderUpdateReq) {
        OrderCommandParam orderCommandParam = OrderParamConverter.converterToOrderCommandParam(orderUpdateReq);
        return DubboResponse.getOK(orderCommandService.updateById(orderCommandParam));
    }

    @Override
    public DubboResponse<Boolean> updatePayType(OrderUpdateReq orderUpdateReq) {
        OrderCommandParam orderCommandParam = OrderParamConverter.converterToOrderCommandParam(orderUpdateReq);
        return DubboResponse.getOK(orderCommandService.updatePayType(orderCommandParam));
    }

    @Override
    public DubboResponse<Boolean> updateStatus(@Valid OrderStatusUpdateReq orderStatusUpdateReq) {
        return DubboResponse.getOK(orderCommandService.updateStatus(orderStatusUpdateReq));
    }

    @Override
    public DubboResponse<Integer> batchUpdateStatus(@Valid OrderStatusBatchUpdateReq orderStatusBatchUpdateReq) {

        return DubboResponse.getOK(orderCommandService.batchUpdateStatus(orderStatusBatchUpdateReq));
    }

    @Override
    public DubboResponse<Boolean> selfLifting(@Valid OrderSelfLiftReq orderSelfLiftReq) {
        return DubboResponse.getOK(orderCommandService.selfLifting(orderSelfLiftReq));
    }

    @Override
    public DubboResponse<Boolean> batchUpdateProfitSharingFinishTime(@Valid ProfitSharingFinishTimeReq req) {
        return DubboResponse.getOK(orderCommandService.batchUpdateProfitSharingFinishTime(req));
    }

    @Override
    public DubboResponse<Boolean> close(@Valid OrderCloseReq req) {
        return DubboResponse.getOK(orderCommandService.close(req));
    }

    @Override
    public DubboResponse<Boolean> auditSuccess(@Valid OrderAuditReq req) {
        return DubboResponse.getOK(orderCommandService.auditSuccess(req));
    }

    @Override
    public DubboResponse<Long> create(@Valid OrderCreateReq req) {
        return DubboResponse.getOK(orderCommandService.create(req));
    }

    @Override
    public DubboResponse<Boolean> setDeliveryDatePresaleOrder(@Valid OrderPresaleSetDeliveryDateReq req) {
        return DubboResponse.getOK(orderCommandService.setDeliveryDatePresaleOrder(req));
    }

    @Override
    public DubboResponse<Boolean> cancel(@Valid OrderCancelReq req) {
        return DubboResponse.getOK(orderCommandService.cancel(req));
    }

    @Override
    public DubboResponse<Boolean> lockStockSuccess(@Valid LockStockSuccessReq req) {
        return DubboResponse.getOK(orderCommandService.lockStockSuccess(req));
    }

    @Override
    public DubboResponse<Boolean> confirm(@Valid OrderConfirmReq req) {
        return DubboResponse.getOK(orderCommandService.confirm(req));
    }

    @Override
    public DubboResponse<Boolean> fulfillmentOrderCreate(@Valid OrderFulfillmentOrderCreateReq req) {
        return DubboResponse.getOK(orderCommandService.fulfillmentOrderCreate(req));
    }

    @Override
    public DubboResponse<Boolean> finish() {
        return DubboResponse.getOK(orderCommandService.finish());
    }

    @Override
    public DubboResponse<Boolean> paySuccess(@Valid OrderPaySuccessReq req) {
        return DubboResponse.getOK(orderCommandService.paySuccess(req));
    }

    @Override
    public DubboResponse<Integer> updateOrderDeliveryTime(@Valid OrderUpdateDelivertDateReq req) {
        return DubboResponse.getOK(orderCommandService.updateOrderDeliveryTime(req));
    }

    @Override
    public DubboResponse<Boolean> selfLiftingFinish(@Valid OrderSelfLiftingFinishReq req) {
        return DubboResponse.getOK(orderCommandService.selfLiftingFinish(req));
    }

    @Override
    public DubboResponse<Boolean> updateOrderStoreNo(@Valid OrderUpdateStoreNoReq req) {
        return DubboResponse.getOK(orderCommandService.updateOrderStoreNo(req));
    }

    @Override
    public DubboResponse<OrderResp> refreshOrderAmount(@Valid RefreshOrderAmountReq req) {
        return DubboResponse.getOK(orderCommandService.refreshOrderAmount(req));
    }
}
