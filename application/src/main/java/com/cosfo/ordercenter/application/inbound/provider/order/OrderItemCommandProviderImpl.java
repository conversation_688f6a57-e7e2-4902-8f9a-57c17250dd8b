package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.OrderItemCommandService;
import com.cosfo.ordercenter.client.provider.OrderItemCommandProvider;
import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemCommandProviderImpl implements OrderItemCommandProvider {

    @Resource
    private OrderItemCommandService orderItemCommandService;

    @Override
    public DubboResponse<Boolean> updateAfterSaleExpiryTime(@Valid OrderItemUpdateReq dto) {
        return DubboResponse.getOK(orderItemCommandService.updateAfterSaleExpiryTime(dto));
    }

    @Override
    public DubboResponse<Boolean> updateDeliveryQuantity(@Valid OrderItemUpdateDeliveryQuantityReq dto) {
        return DubboResponse.getOK(orderItemCommandService.updateDeliveryQuantity(dto));
    }

    @Override
    public DubboResponse<Boolean> updateStatus(OrderItemStatusUpdateReq req) {
        return DubboResponse.getOK(orderItemCommandService.updateStatus(req));
    }
}
