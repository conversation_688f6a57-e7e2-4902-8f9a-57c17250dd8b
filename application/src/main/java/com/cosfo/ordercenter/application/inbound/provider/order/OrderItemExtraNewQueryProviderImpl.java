package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.OrderItemExtraQueryService;
import com.cosfo.ordercenter.client.provider.OrderItemExtraQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemExtraNewQueryProviderImpl implements OrderItemExtraQueryProvider {

    @Resource
    private OrderItemExtraQueryService orderItemExtraQueryService;

    @Override
    public DubboResponse<List<OrderItemExtraResp>> queryOrderItemExtraList(@Valid OrderItemExtraQueryReq orderItemExtraQueryReq) {
        return DubboResponse.getOK(orderItemExtraQueryService.queryOrderItemExtraList(orderItemExtraQueryReq));
    }
}
