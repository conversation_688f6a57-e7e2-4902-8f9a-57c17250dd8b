package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderItemConverter;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemNewQueryProviderImpl implements OrderItemQueryProvider {

    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;

    @Override
    public DubboResponse<OrderItemResp> queryById(Long orderItemId) {
        OrderItemEntity orderItemEntity = orderItemQueryRepository.queryById(orderItemId);
        return DubboResponse.getOK(OrderItemConverter.convertToResp(orderItemEntity));
    }

    @Override
    public DubboResponse<List<OrderItemResp>> queryByIds(List<Long> orderItemIds) {
        List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.queryByIds(orderItemIds);
        return DubboResponse.getOK(OrderItemConverter.convertToRespList(orderItemEntities));
    }

    @Override
    public DubboResponse<OrderItemAndSnapshotResp> queryDetailById(Long orderItemId) {
        OrderItemEntity orderItemEntity = orderItemQueryRepository.queryById(orderItemId);
        OrderItemSnapshotEntity orderItemSnapshotEntity = orderItemSnapshotQueryRepository.queryByOrderItemId(orderItemId);

        return DubboResponse.getOK(OrderItemConverter.convertToItemAndSnapshotResp(orderItemEntity, orderItemSnapshotEntity));
    }

    @Override
    public DubboResponse<List<OrderItemAndSnapshotResp>> queryByOrderId(Long orderId) {
        List<OrderItemEntity> orderItems = orderItemQueryRepository.queryByOrderId(orderId);
        List<Long> orderItemIds = orderItems.stream().map(OrderItemEntity::getId).collect(Collectors.toList());
        List<OrderItemSnapshotEntity> snapshotList = orderItemSnapshotQueryRepository.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshotEntity> snapshotMap = snapshotList.stream().collect(Collectors.toMap(OrderItemSnapshotEntity::getOrderItemId, snapshot -> snapshot));
        List<OrderItemAndSnapshotResp> withSnapshotDTOList = OrderItemConverter.convertToItemAndSnapshotRespList(orderItems, snapshotMap);
        return DubboResponse.getOK(withSnapshotDTOList);
    }

    @Override
    public DubboResponse<List<OrderItemResp>> queryOrderItemList(Long orderId) {
        List<OrderItemEntity> orderItemEntities = orderItemQueryRepository.queryByOrderId(orderId);
        return DubboResponse.getOK(OrderItemConverter.convertToRespList(orderItemEntities));
    }

    @Override
    public DubboResponse<List<OrderItemAndSnapshotResp>> queryOrderItemList(@Valid OrderItemQueryReq orderItemQueryReq) {
        List<OrderItemWithSnapshotEntity> orderItemWithSnapshotEntities = orderItemQueryRepository.batchQueryOrderItemDetail(orderItemQueryReq.getOrderIds(), orderItemQueryReq.getSupplierIds());
        return DubboResponse.getOK(OrderItemConverter.convertToItemAndSnapshotRespList(orderItemWithSnapshotEntities));
    }
}
