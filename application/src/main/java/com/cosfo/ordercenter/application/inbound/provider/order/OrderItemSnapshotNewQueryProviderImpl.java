package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderItemSnapshotConverter;
import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemSnapshotQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemSnapshotNewQueryProviderImpl implements OrderItemSnapshotQueryProvider {

    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;

    @Override
    public DubboResponse<OrderItemSnapshotResp> queryByOrderItemId(Long orderItemId) {
        OrderItemSnapshotEntity orderItemSnapshotEntity = orderItemSnapshotQueryRepository.queryByOrderItemId(orderItemId);
        return DubboResponse.getOK(OrderItemSnapshotConverter.convertToSnapshotResp(orderItemSnapshotEntity));
    }

    @Override
    public DubboResponse<List<OrderItemSnapshotResp>> queryByOrderItemIds(List<Long> orderItemIds) {
        List<OrderItemSnapshotEntity> orderItemSnapshotEntities = orderItemSnapshotQueryRepository.queryByOrderItemIds(orderItemIds);
        return DubboResponse.getOK(OrderItemSnapshotConverter.convertToRespList(orderItemSnapshotEntities));
    }

    @Override
    public DubboResponse<List<OrderItemSnapshotResp>> queryList(@Valid OrderItemSnapshotQueryReq queryReq) {
        OrderItemSnapshotQueryParam orderItemSnapshotQueryParam = OrderItemSnapshotConverter.convertToParam(queryReq);
        List<OrderItemSnapshotEntity> orderItemSnapshotEntities = orderItemSnapshotQueryRepository.queryList(orderItemSnapshotQueryParam);
        return DubboResponse.getOK(OrderItemSnapshotConverter.convertToRespList(orderItemSnapshotEntities));
    }
}
