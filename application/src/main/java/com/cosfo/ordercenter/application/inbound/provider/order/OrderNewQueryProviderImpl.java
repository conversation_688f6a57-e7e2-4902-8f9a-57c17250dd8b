package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderParamConverter;
import com.cosfo.ordercenter.application.service.order.OrderQueryService;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.order.OrderAggResp;
import com.cosfo.ordercenter.client.resp.order.OrderOutResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.common.config.RequestSourceConfig;
import com.cosfo.ordercenter.common.constants.CommonConstants;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderNewQueryProviderImpl implements OrderQueryProvider {

    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private RequestSourceConfig requestSourceConfig;
    @Resource
    private OrderQueryService orderQueryService;

    @Override
    public DubboResponse<OrderResp> queryById(Long orderId) {
        OrderEntity order = orderQueryRepository.queryById(orderId);
        return DubboResponse.getOK(OrderConverter.convertToOrderResp(order));
    }

    @Override
    public DubboResponse<OrderResp> queryByNo(String orderNo) {
        OrderEntity order = orderQueryRepository.queryByNo(orderNo);
        return DubboResponse.getOK(OrderConverter.convertToOrderResp(order));
    }

    @Override
    public DubboResponse<List<OrderResp>> queryByIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<OrderEntity> orders = orderQueryRepository.queryByIds(orderIds);
        return DubboResponse.getOK(OrderConverter.convertToOrderRespList(orders));
    }

    @Override
    public DubboResponse<List<OrderResp>> queryByNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<OrderEntity> orders = orderQueryRepository.queryByOrderNos(orderNos);
        return DubboResponse.getOK(OrderConverter.convertToOrderRespList(orders));
    }

    @Override
    public DubboResponse<List<OrderResp>> queryOrderList(@Valid OrderQueryReq orderQueryReq) {
        int batchSize = CommonConstants.DEFAULT_SIZE;
        if (orderQueryReq.getBatchSize() != null && orderQueryReq.getBatchSize() < batchSize) {
            batchSize = orderQueryReq.getBatchSize();
        }
        orderQueryReq.setBatchSize(batchSize);
        List<OrderEntity> list = orderQueryRepository.queryList(OrderParamConverter.convertToOrderQueryParam(orderQueryReq));
        return DubboResponse.getOK(OrderConverter.convertToOrderRespList(list));
    }

    @Override
    public DubboResponse<PageInfo<OrderResp>> queryOrderPage(@Valid OrderQueryReq orderQueryReq) {
        PageInfo<OrderEntity> orderPage = orderQueryRepository.queryPage(OrderParamConverter.convertToOrderQueryParam(orderQueryReq));
        return DubboResponse.getOK(OrderConverter.converterToPageInfo(orderPage));
    }

    @Override
    public DubboResponse<PageInfo<OrderResp>> queryOmsOrderPage(@Valid OrderOmsQueryReq orderQueryReq) {
        String attachment = RpcContext.getContext().getAttachment(org.apache.dubbo.common.constants.CommonConstants.REMOTE_APPLICATION_KEY);
        if (StringUtils.isEmpty(attachment) || !requestSourceConfig.getOms().equals(attachment)) {
            log.warn("订单列表接口调用错误，调用端:{}", attachment);
            throw new BizException("不支持此操作");
        }
        PageInfo<OrderEntity> orderPage = orderQueryRepository.queryPage(OrderParamConverter.convertToOrderOmsQueryParam(orderQueryReq));
        return DubboResponse.getOK(OrderConverter.converterToPageInfo(orderPage));
    }

    @Override
    public DubboResponse<List<String>> queryNeedDeliveryOrder(@Valid OrderNeedDeliveryReq needDeliveryReq) {
        List<String> orderNoList = orderQueryRepository.queryNeedDeliveryOrder(OrderParamConverter.converterToDeliveryQueryParam(needDeliveryReq));
        return DubboResponse.getOK(orderNoList);
    }

    @Override
    public DubboResponse<List<OrderOutResp>> queryOrderInfo(@Valid OrderOutQueryReq orderOutQueryReq) {
        if (orderOutQueryReq.getOrderNos().size() > 20) {
            throw new BizException("订单数量不能大于20个");
        }
        return DubboResponse.getOK(orderQueryService.queryOrderInfo(orderOutQueryReq));
    }

    @Override
    public DubboResponse<List<OrderAggResp>> queryOrderAggByNos(@Valid OrderAggQueryReq orderAggQueryReq) {
        return DubboResponse.getOK(orderQueryService.queryOrderAggByNos(orderAggQueryReq));
    }
}
