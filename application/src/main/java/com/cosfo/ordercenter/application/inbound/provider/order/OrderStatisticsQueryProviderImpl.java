package com.cosfo.ordercenter.application.inbound.provider.order;

import com.cosfo.ordercenter.application.service.order.OrderStatisticsQueryService;
import com.cosfo.ordercenter.client.provider.OrderStatisticsQueryProvider;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderStatisticsQueryProviderImpl implements OrderStatisticsQueryProvider {

    @Resource
    private OrderStatisticsQueryService orderStatisticsQueryService;

    @Override
    public DubboResponse<OrderSummaryResp> queryOrderSummary(@Valid OrderSummaryReq orderSummaryReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.queryOrderSummary(orderSummaryReq));
    }

    @Override
    public DubboResponse<Integer> getWaitDeliveryQuantity(Long tenantId) {
        return DubboResponse.getOK(orderStatisticsQueryService.getWaitDeliveryQuantity(tenantId));
    }

    @Override
    public DubboResponse<Integer> getWaitAuditQuantity(Long tenantId) {
        return DubboResponse.getOK(orderStatisticsQueryService.getWaitAuditQuantity(tenantId));
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityResp>> querySkuSaleQuantity(@Valid OrderSkuSaleReq orderSkuSaleReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.querySkuSaleQuantity(orderSkuSaleReq));
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityResp>> querySkuSaleWithStoreNoQuantity(@Valid OrderSkuSaleReq orderSkuSaleReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq));
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityResp>> querySkuSaleWithCityQuantity(@Valid OrderSkuSaleReq orderSkuSaleReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.querySkuSaleWithCityQuantity(orderSkuSaleReq));
    }

    @Override
    public DubboResponse<List<OrderDetailResp>> queryOrderDetail(@Valid OrderDetailReq orderDetailReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.queryOrderDetail(orderDetailReq));
    }

    @Override
    public DubboResponse<OrderItemSaleResp> querySkuSaleQuantity(List<Long> orderIds, Long tenantId) {
        return DubboResponse.getOK(orderStatisticsQueryService.querySkuSaleQuantity(orderIds, tenantId));
    }

    @Override
    public DubboResponse<Integer> countOrderQuantity(OrderCountReq orderCountReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.countOrderQuantity(orderCountReq));
    }

    @Override
    public DubboResponse<Map<Long, Integer>> countItemSaleQuantity(@Valid ItemSaleQuantityReq itemSaleQuantityReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.countItemSaleQuantity(itemSaleQuantityReq));
    }

    @Override
    public DubboResponse<List<SupplierOrderTotalResp>> querySupplierOrderSummary(@Valid SupplierOrderTotalReq supplierOrderTotalReq) {
        return DubboResponse.getOK(orderStatisticsQueryService.querySupplierOrderSummary(supplierOrderTotalReq));
    }
}
