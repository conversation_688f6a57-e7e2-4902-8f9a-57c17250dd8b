package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.OrderAddressAddReq;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderAddressConverter {
    private OrderAddressConverter() {
    }

    public static OrderAddressResp converterToResp(OrderAddressEntity orderAddress) {

        if (orderAddress == null) {
            return null;
        }
        OrderAddressResp orderAddressResp = new OrderAddressResp();
        orderAddressResp.setId(orderAddress.getId());
        orderAddressResp.setTenantId(orderAddress.getTenantId());
        orderAddressResp.setOrderId(orderAddress.getOrderId());
        orderAddressResp.setContactName(orderAddress.getContactName());
        orderAddressResp.setContactPhone(orderAddress.getContactPhone());
        orderAddressResp.setProvince(orderAddress.getProvince());
        orderAddressResp.setCity(orderAddress.getCity());
        orderAddressResp.setArea(orderAddress.getArea());
        orderAddressResp.setAddress(orderAddress.getAddress());
        orderAddressResp.setCreateTime(orderAddress.getCreateTime());
        orderAddressResp.setUpdateTime(orderAddress.getUpdateTime());
        orderAddressResp.setPoiNote(orderAddress.getPoiNote());
        return orderAddressResp;
    }

    public static OrderAddressCommandParam convertToCommandParam(OrderAddressAddReq addressAddReq) {

        if (addressAddReq == null) {
            return null;
        }
        OrderAddressCommandParam orderAddressCommandParam = new OrderAddressCommandParam();
        orderAddressCommandParam.setId(addressAddReq.getId());
        orderAddressCommandParam.setTenantId(addressAddReq.getTenantId());
        orderAddressCommandParam.setOrderId(addressAddReq.getOrderId());
        orderAddressCommandParam.setContactName(addressAddReq.getContactName());
        orderAddressCommandParam.setContactPhone(addressAddReq.getContactPhone());
        orderAddressCommandParam.setProvince(addressAddReq.getProvince());
        orderAddressCommandParam.setCity(addressAddReq.getCity());
        orderAddressCommandParam.setArea(addressAddReq.getArea());
        orderAddressCommandParam.setAddress(addressAddReq.getAddress());
        orderAddressCommandParam.setCreateTime(addressAddReq.getCreateTime());
        orderAddressCommandParam.setUpdateTime(addressAddReq.getUpdateTime());
        orderAddressCommandParam.setPoiNote(addressAddReq.getPoiNote());
        return orderAddressCommandParam;
    }

    public static List<OrderAddressResp> convertToRespList(List<OrderAddressEntity> entityList) {

        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderAddressResp> orderAddressRespList = new ArrayList<>();
        for (OrderAddressEntity orderAddressEntity : entityList) {
            orderAddressRespList.add(converterToResp(orderAddressEntity));
        }
        return orderAddressRespList;
    }

    public static OrderAddressCommandParam convertToCommandParam(OrderAddressDTO orderAddressDTO) {

        if (orderAddressDTO == null) {
            return null;
        }
        OrderAddressCommandParam orderAddressCommandParam = new OrderAddressCommandParam();
        orderAddressCommandParam.setId(orderAddressDTO.getId());
        orderAddressCommandParam.setTenantId(orderAddressDTO.getTenantId());
        orderAddressCommandParam.setOrderId(orderAddressDTO.getOrderId());
        orderAddressCommandParam.setContactName(orderAddressDTO.getContactName());
        orderAddressCommandParam.setContactPhone(orderAddressDTO.getContactPhone());
        orderAddressCommandParam.setProvince(orderAddressDTO.getProvince());
        orderAddressCommandParam.setCity(orderAddressDTO.getCity());
        orderAddressCommandParam.setArea(orderAddressDTO.getArea());
        orderAddressCommandParam.setAddress(orderAddressDTO.getAddress());
        orderAddressCommandParam.setCreateTime(orderAddressDTO.getCreateTime());
        orderAddressCommandParam.setUpdateTime(orderAddressDTO.getUpdateTime());
        orderAddressCommandParam.setPoiNote(orderAddressDTO.getPoiNote());
        return orderAddressCommandParam;
    }
}
