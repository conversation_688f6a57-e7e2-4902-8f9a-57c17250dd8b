package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderAggResp;
import com.cosfo.ordercenter.client.resp.order.OrderOutResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.domain.order.entity.OrderDetailEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderSelfLiftingFinishParam;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderDetail;
import com.github.pagehelper.PageInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderConverter {

    private OrderConverter() {
    }

    public static OrderResp convertToOrderResp(OrderEntity orderEntity) {
        if (orderEntity == null) {
            return null;
        }
        OrderResp orderResp = new OrderResp();
        orderResp.setId(orderEntity.getId());
        orderResp.setTenantId(orderEntity.getTenantId());
        orderResp.setStoreId(orderEntity.getStoreId());
        orderResp.setAccountId(orderEntity.getAccountId());
        orderResp.setSupplierTenantId(orderEntity.getSupplierTenantId());
        orderResp.setOrderNo(orderEntity.getOrderNo());
        orderResp.setWarehouseType(orderEntity.getWarehouseType());
        orderResp.setPayablePrice(orderEntity.getPayablePrice());
        orderResp.setDeliveryFee(orderEntity.getDeliveryFee());
        orderResp.setTotalPrice(orderEntity.getTotalPrice());
        orderResp.setStatus(orderEntity.getStatus());
        orderResp.setPayType(orderEntity.getPayType());
        orderResp.setOnlinePayChannel(orderEntity.getOnlinePayChannel());
        orderResp.setPayTime(orderEntity.getPayTime());
        orderResp.setDeliveryTime(orderEntity.getDeliveryTime());
        orderResp.setFinishedTime(orderEntity.getFinishedTime());
        orderResp.setCreateTime(orderEntity.getCreateTime());
        orderResp.setUpdateTime(orderEntity.getUpdateTime());
        orderResp.setRemark(orderEntity.getRemark());
        orderResp.setApplyEndTime(orderEntity.getApplyEndTime());
        orderResp.setAutoFinishedTime(orderEntity.getAutoFinishedTime());
        orderResp.setWarehouseNo(orderEntity.getWarehouseNo());
        orderResp.setCombineOrderId(orderEntity.getCombineOrderId());
        orderResp.setOrderType(orderEntity.getOrderType());
        orderResp.setBeginDeliveryTime(orderEntity.getBeginDeliveryTime());
        orderResp.setProfitSharingFinishTime(orderEntity.getProfitSharingFinishTime());
        orderResp.setOrderVersion(orderEntity.getOrderVersion());
        orderResp.setOrderSource(orderEntity.getOrderSource());
        orderResp.setCustomerOrderId(orderEntity.getCustomerOrderId());
        orderResp.setPlanOrderNo(orderEntity.getPlanOrderNo());
        orderResp.setFulfillmentType(orderEntity.getFulfillmentType());
// Not mapped FROM fields:
// fulfillmentNo
        return orderResp;
    }

    public static OrderAggResp convertToOrderAggResp(OrderEntity orderEntity) {
        if (orderEntity == null) {
            return null;
        }
        OrderAggResp orderAggResp = new OrderAggResp();
        orderAggResp.setId(orderEntity.getId());
        orderAggResp.setTenantId(orderEntity.getTenantId());
        orderAggResp.setStoreId(orderEntity.getStoreId());
        orderAggResp.setAccountId(orderEntity.getAccountId());
        orderAggResp.setSupplierTenantId(orderEntity.getSupplierTenantId());
        orderAggResp.setOrderNo(orderEntity.getOrderNo());
        orderAggResp.setWarehouseType(orderEntity.getWarehouseType());
        orderAggResp.setPayablePrice(orderEntity.getPayablePrice());
        orderAggResp.setDeliveryFee(orderEntity.getDeliveryFee());
        orderAggResp.setTotalPrice(orderEntity.getTotalPrice());
        orderAggResp.setStatus(orderEntity.getStatus());
        orderAggResp.setPayType(orderEntity.getPayType());
        orderAggResp.setOnlinePayChannel(orderEntity.getOnlinePayChannel());
        orderAggResp.setPayTime(orderEntity.getPayTime());
        orderAggResp.setDeliveryTime(orderEntity.getDeliveryTime());
        orderAggResp.setFinishedTime(orderEntity.getFinishedTime());
        orderAggResp.setCreateTime(orderEntity.getCreateTime());
        orderAggResp.setUpdateTime(orderEntity.getUpdateTime());
        orderAggResp.setRemark(orderEntity.getRemark());
        orderAggResp.setApplyEndTime(orderEntity.getApplyEndTime());
        orderAggResp.setAutoFinishedTime(orderEntity.getAutoFinishedTime());
        orderAggResp.setWarehouseNo(orderEntity.getWarehouseNo());
        orderAggResp.setCombineOrderId(orderEntity.getCombineOrderId());
        orderAggResp.setOrderType(orderEntity.getOrderType());
        orderAggResp.setBeginDeliveryTime(orderEntity.getBeginDeliveryTime());
        orderAggResp.setProfitSharingFinishTime(orderEntity.getProfitSharingFinishTime());
        orderAggResp.setOrderVersion(orderEntity.getOrderVersion());
        orderAggResp.setOrderSource(orderEntity.getOrderSource());
        orderAggResp.setCustomerOrderId(orderEntity.getCustomerOrderId());
        orderAggResp.setPlanOrderNo(orderEntity.getPlanOrderNo());
        orderAggResp.setFulfillmentType(orderEntity.getFulfillmentType());
// Not mapped TO fields:
// storeName
// orderItemAndSnapshotRespList
// orderAddressResp
// Not mapped FROM fields:
// fulfillmentNo
        return orderAggResp;
    }

    public static List<OrderResp> convertToOrderRespList(List<OrderEntity> orderEntities) {

        if (orderEntities == null) {
            return Collections.emptyList();
        }
        List<OrderResp> orderRespList = new ArrayList<>();
        for (OrderEntity orderEntity : orderEntities) {
            orderRespList.add(convertToOrderResp(orderEntity));
        }
        return orderRespList;
    }


    public static PageInfo<OrderResp> converterToPageInfo(PageInfo<OrderEntity> orderEntityPageInfo) {


        if (orderEntityPageInfo == null) {
            return null;
        }
        PageInfo<OrderResp> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(orderEntityPageInfo.getPageNum());
        pageInfo.setPageSize(orderEntityPageInfo.getPageSize());
        pageInfo.setSize(orderEntityPageInfo.getSize());
        pageInfo.setStartRow(orderEntityPageInfo.getStartRow());
        pageInfo.setEndRow(orderEntityPageInfo.getEndRow());
        pageInfo.setPages(orderEntityPageInfo.getPages());
        pageInfo.setPrePage(orderEntityPageInfo.getPrePage());
        pageInfo.setNextPage(orderEntityPageInfo.getNextPage());
        pageInfo.setIsFirstPage(orderEntityPageInfo.isIsFirstPage());
        pageInfo.setIsLastPage(orderEntityPageInfo.isIsLastPage());
        pageInfo.setHasPreviousPage(orderEntityPageInfo.isHasPreviousPage());
        pageInfo.setHasNextPage(orderEntityPageInfo.isHasNextPage());
        pageInfo.setNavigatePages(orderEntityPageInfo.getNavigatePages());
        pageInfo.setNavigatepageNums(orderEntityPageInfo.getNavigatepageNums());
        pageInfo.setNavigateFirstPage(orderEntityPageInfo.getNavigateFirstPage());
        pageInfo.setNavigateLastPage(orderEntityPageInfo.getNavigateLastPage());
        pageInfo.setTotal(orderEntityPageInfo.getTotal());
        pageInfo.setList(convertToOrderRespList(orderEntityPageInfo.getList()));
        return pageInfo;
    }


    public static OrderOutResp convertToOrderOutResp(OrderEntity orderEntity) {


        if (orderEntity == null) {
            return null;
        }
        OrderOutResp orderOutResp = new OrderOutResp();
        orderOutResp.setTenantId(orderEntity.getTenantId());
        orderOutResp.setStoreId(orderEntity.getStoreId());
        orderOutResp.setSupplierTenantId(orderEntity.getSupplierTenantId());
        orderOutResp.setWarehouseType(orderEntity.getWarehouseType());
        orderOutResp.setStatus(orderEntity.getStatus());
        orderOutResp.setPayablePrice(orderEntity.getPayablePrice());
        orderOutResp.setDeliveryFee(orderEntity.getDeliveryFee());
        orderOutResp.setTotalPrice(orderEntity.getTotalPrice());
        orderOutResp.setOrderNo(orderEntity.getOrderNo());
        orderOutResp.setAccountId(orderEntity.getAccountId());
        orderOutResp.setDeliveryTime(orderEntity.getDeliveryTime());
        orderOutResp.setPayType(orderEntity.getPayType());
        orderOutResp.setOnlinePayChannel(orderEntity.getOnlinePayChannel());
        orderOutResp.setPayTime(orderEntity.getPayTime());
        orderOutResp.setFinishedTime(orderEntity.getFinishedTime());
        orderOutResp.setRemark(orderEntity.getRemark());
        orderOutResp.setApplyEndTime(orderEntity.getApplyEndTime());
        orderOutResp.setAutoFinishedTime(orderEntity.getAutoFinishedTime());
        orderOutResp.setOrderType(orderEntity.getOrderType());
        orderOutResp.setCombineOrderId(orderEntity.getCombineOrderId());
        orderOutResp.setOrderId(orderEntity.getId());
// Not mapped TO fields:
// orderId
// supplierName
// orderTime
// accountName
// expireTime
// totalAmount
// orderAddressResp
// orderItemOutDTOS
// afterSaleOrderNo
// storeName
// saleAfterFlag
// warehouseNo
// combineItemId
// combineMarketId
// Not mapped FROM fields:
// id
// createTime
// updateTime
// warehouseNo
// beginDeliveryTime
// profitSharingFinishTime
// orderVersion
// orderSource
// customerOrderId
// fulfillmentNo
        return orderOutResp;
    }

    public static OrderEntity convertToEntity(OrderDTO orderDTO) {

        if (orderDTO == null) {
            return null;
        }
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setId(orderDTO.getId());
        orderEntity.setTenantId(orderDTO.getTenantId());
        orderEntity.setStoreId(orderDTO.getStoreId());
        orderEntity.setAccountId(orderDTO.getAccountId());
        orderEntity.setSupplierTenantId(orderDTO.getSupplierTenantId());
        orderEntity.setOrderNo(orderDTO.getOrderNo());
        orderEntity.setWarehouseType(orderDTO.getWarehouseType());
        orderEntity.setPayablePrice(orderDTO.getPayablePrice());
        orderEntity.setDeliveryFee(orderDTO.getDeliveryFee());
        orderEntity.setTotalPrice(orderDTO.getTotalPrice());
        orderEntity.setStatus(orderDTO.getStatus());
        orderEntity.setPayType(orderDTO.getPayType());
        orderEntity.setOnlinePayChannel(orderDTO.getOnlinePayChannel());
        orderEntity.setPayTime(orderDTO.getPayTime());
        orderEntity.setDeliveryTime(orderDTO.getDeliveryTime());
        orderEntity.setFinishedTime(orderDTO.getFinishedTime());
        orderEntity.setCreateTime(orderDTO.getCreateTime());
        orderEntity.setUpdateTime(orderDTO.getUpdateTime());
        orderEntity.setRemark(orderDTO.getRemark());
        orderEntity.setApplyEndTime(orderDTO.getApplyEndTime());
        orderEntity.setAutoFinishedTime(orderDTO.getAutoFinishedTime());
        orderEntity.setWarehouseNo(orderDTO.getWarehouseNo());
        orderEntity.setCombineOrderId(orderDTO.getCombineOrderId());
        orderEntity.setOrderType(orderDTO.getOrderType());
        orderEntity.setBeginDeliveryTime(orderDTO.getBeginDeliveryTime());
        orderEntity.setProfitSharingFinishTime(orderDTO.getProfitSharingFinishTime());
        orderEntity.setOrderVersion(orderDTO.getOrderVersion());
        orderEntity.setOrderSource(orderDTO.getOrderSource());
        orderEntity.setCustomerOrderId(orderDTO.getCustomerOrderId());
        orderEntity.setPlanOrderNo(orderDTO.getPlanOrderNo());
        orderEntity.setFulfillmentType(orderDTO.getFulfillmentType());
// Not mapped TO fields:
// fulfillmentNo
        return orderEntity;
    }

    public static OrderSelfLiftingFinishParam convertToFinishParam(OrderSelfLiftingFinishReq finishReq) {

        if (finishReq == null) {
            return null;
        }
        OrderSelfLiftingFinishParam orderSelfLiftingFinishParam = new OrderSelfLiftingFinishParam();
        orderSelfLiftingFinishParam.setOrderId(finishReq.getOrderId());
        return orderSelfLiftingFinishParam;
    }

    public static Order convertToPO(OrderEntity orderEntity) {

        if (orderEntity == null) {
            return null;
        }
        Order order = new Order();
        order.setId(orderEntity.getId());
        order.setTenantId(orderEntity.getTenantId());
        order.setStoreId(orderEntity.getStoreId());
        order.setAccountId(orderEntity.getAccountId());
        order.setSupplierTenantId(orderEntity.getSupplierTenantId());
        order.setOrderNo(orderEntity.getOrderNo());
        order.setWarehouseType(orderEntity.getWarehouseType());
        order.setPayablePrice(orderEntity.getPayablePrice());
        order.setDeliveryFee(orderEntity.getDeliveryFee());
        order.setTotalPrice(orderEntity.getTotalPrice());
        order.setStatus(orderEntity.getStatus());
        order.setPayType(orderEntity.getPayType());
        order.setOnlinePayChannel(orderEntity.getOnlinePayChannel());
        order.setPayTime(orderEntity.getPayTime());
        order.setDeliveryTime(orderEntity.getDeliveryTime());
        order.setFinishedTime(orderEntity.getFinishedTime());
        order.setCreateTime(orderEntity.getCreateTime());
        order.setUpdateTime(orderEntity.getUpdateTime());
        order.setRemark(orderEntity.getRemark());
        order.setApplyEndTime(orderEntity.getApplyEndTime());
        order.setAutoFinishedTime(orderEntity.getAutoFinishedTime());
        order.setWarehouseNo(orderEntity.getWarehouseNo());
        order.setCombineOrderId(orderEntity.getCombineOrderId());
        order.setOrderType(orderEntity.getOrderType());
        order.setBeginDeliveryTime(orderEntity.getBeginDeliveryTime());
        order.setProfitSharingFinishTime(orderEntity.getProfitSharingFinishTime());
        order.setOrderVersion(orderEntity.getOrderVersion());
        order.setOrderSource(orderEntity.getOrderSource());
        order.setCustomerOrderId(orderEntity.getCustomerOrderId());
        order.setFulfillmentNo(orderEntity.getFulfillmentNo());
        order.setPlanOrderNo(orderEntity.getPlanOrderNo());
        order.setFulfillmentType(orderEntity.getFulfillmentType());
        return order;
    }

    public static List<OrderDetail> convertToDetailList(List<OrderDetailEntity> orderDetailEntities) {

        if (orderDetailEntities == null) {
            return Collections.emptyList();
        }
        List<OrderDetail> orderDetailList = new ArrayList<>();
        for (OrderDetailEntity orderDetailEntity : orderDetailEntities) {
            orderDetailList.add(toOrderDetail(orderDetailEntity));
        }
        return orderDetailList;
    }

    public static OrderDetail toOrderDetail(OrderDetailEntity orderDetailEntity) {

        if (orderDetailEntity == null) {
            return null;
        }
        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setOrderId(orderDetailEntity.getOrderId());
        orderDetail.setOrderNo(orderDetailEntity.getOrderNo());
        orderDetail.setStoreId(orderDetailEntity.getStoreId());
        orderDetail.setCreateTime(orderDetailEntity.getCreateTime());
        orderDetail.setPayTime(orderDetailEntity.getPayTime());
        orderDetail.setPayType(orderDetailEntity.getPayType());
        orderDetail.setDeliveryTime(orderDetailEntity.getDeliveryTime());
        orderDetail.setFinishedTime(orderDetailEntity.getFinishedTime());
        orderDetail.setTitle(orderDetailEntity.getTitle());
        orderDetail.setItemId(orderDetailEntity.getItemId());
        orderDetail.setSkuId(orderDetailEntity.getSkuId());
        orderDetail.setSpecification(orderDetailEntity.getSpecification());
        orderDetail.setAmount(orderDetailEntity.getAmount());
        orderDetail.setSkuPrice(orderDetailEntity.getSkuPrice());
        orderDetail.setSkuTotalPrice(orderDetailEntity.getSkuTotalPrice());
        orderDetail.setDeliveryFee(orderDetailEntity.getDeliveryFee());
        orderDetail.setOrderPrice(orderDetailEntity.getOrderPrice());
        orderDetail.setOrderItemId(orderDetailEntity.getOrderItemId());
        orderDetail.setSupplyTenantId(orderDetailEntity.getSupplyTenantId());
        orderDetail.setSupplySkuId(orderDetailEntity.getSupplySkuId());
        orderDetail.setMainPicture(orderDetailEntity.getMainPicture());
        orderDetail.setSupplyPrice(orderDetailEntity.getSupplyPrice());
        orderDetail.setWarehouseType(orderDetailEntity.getWarehouseType());
        orderDetail.setPricingType(orderDetailEntity.getPricingType());
        orderDetail.setPricingNumber(orderDetailEntity.getPricingNumber());
        orderDetail.setGoodsType(orderDetailEntity.getGoodsType());
        orderDetail.setWeight(orderDetailEntity.getWeight());
        return orderDetail;
    }
}
