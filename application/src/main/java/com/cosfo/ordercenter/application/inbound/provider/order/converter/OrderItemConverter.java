package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemCommandParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderItemConverter {
    private OrderItemConverter() {
    }



    public static OrderItemUpdateParam convertToParam(OrderItemUpdateReq itemUpdateReq) {

        if (itemUpdateReq == null) {
            return null;
        }
        OrderItemUpdateParam orderItemUpdateParam = new OrderItemUpdateParam();
        orderItemUpdateParam.setOrderItemId(itemUpdateReq.getOrderItemId());
        orderItemUpdateParam.setAfterSaleExpiryTime(itemUpdateReq.getAfterSaleExpiryTime());
        return orderItemUpdateParam;
    }

    public static OrderItemStatusUpdateParam convertToStatusUpdateParam(OrderItemStatusUpdateReq statusUpdateReq) {
        if (statusUpdateReq == null) {
            return null;
        }
        OrderItemStatusUpdateParam orderItemStatusUpdateParam = new OrderItemStatusUpdateParam();
        orderItemStatusUpdateParam.setOrderItemId(statusUpdateReq.getOrderItemId());
        orderItemStatusUpdateParam.setStatus(statusUpdateReq.getStatus());
        return orderItemStatusUpdateParam;
    }

    public static OrderItemResp convertToResp(OrderItemEntity orderItemEntity) {

        if (orderItemEntity == null) {
            return null;
        }
        OrderItemResp orderItemResp = new OrderItemResp();
        orderItemResp.setId(orderItemEntity.getId());
        orderItemResp.setTenantId(orderItemEntity.getTenantId());
        orderItemResp.setOrderId(orderItemEntity.getOrderId());
        orderItemResp.setItemId(orderItemEntity.getItemId());
        orderItemResp.setAmount(orderItemEntity.getAmount());
        orderItemResp.setPayablePrice(orderItemEntity.getPayablePrice());
        orderItemResp.setTotalPrice(orderItemEntity.getTotalPrice());
        orderItemResp.setStoreNo(orderItemEntity.getStoreNo());
        orderItemResp.setStatus(orderItemEntity.getStatus());
        orderItemResp.setCreateTime(orderItemEntity.getCreateTime());
        orderItemResp.setUpdateTime(orderItemEntity.getUpdateTime());
        orderItemResp.setAfterSaleExpiryTime(orderItemEntity.getAfterSaleExpiryTime());
        orderItemResp.setOrderType(orderItemEntity.getOrderType());
        orderItemResp.setDeliveryQuantity(orderItemEntity.getDeliveryQuantity());
        return orderItemResp;
    }

    public static List<OrderItemResp> convertToRespList(List<OrderItemEntity> entityList) {

        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderItemResp> orderItemRespList = new ArrayList<>();
        for (OrderItemEntity orderItemEntity : entityList) {
            orderItemRespList.add(convertToResp(orderItemEntity));
        }
        return orderItemRespList;
    }


    public static List<OrderItem> convertToItemList(List<OrderItemCommandParam> commandParams) {

        if (commandParams == null) {
            return Collections.emptyList();
        }
        List<OrderItem> orderItemList = new ArrayList<>();
        for (OrderItemCommandParam orderItemCommandParam : commandParams) {
            orderItemList.add(toOrderItem(orderItemCommandParam));
        }
        return orderItemList;
    }

    public static OrderItemAndSnapshotResp convertToItemAndSnapshotResp(OrderItemEntity orderItemEntity, OrderItemSnapshotEntity snapshotEntity) {

        if (orderItemEntity == null) {
            return null;
        }
        OrderItemAndSnapshotResp orderItemAndSnapshotResp = new OrderItemAndSnapshotResp();
        orderItemAndSnapshotResp.setTenantId(orderItemEntity.getTenantId());
        orderItemAndSnapshotResp.setOrderId(orderItemEntity.getOrderId());
        orderItemAndSnapshotResp.setItemId(orderItemEntity.getItemId());
        orderItemAndSnapshotResp.setAmount(orderItemEntity.getAmount());
        orderItemAndSnapshotResp.setPayablePrice(orderItemEntity.getPayablePrice());
        orderItemAndSnapshotResp.setTotalPrice(orderItemEntity.getTotalPrice());
        orderItemAndSnapshotResp.setStoreNo(orderItemEntity.getStoreNo());
        orderItemAndSnapshotResp.setStatus(orderItemEntity.getStatus());
        orderItemAndSnapshotResp.setCreateTime(orderItemEntity.getCreateTime());
        orderItemAndSnapshotResp.setUpdateTime(orderItemEntity.getUpdateTime());
        orderItemAndSnapshotResp.setAfterSaleExpiryTime(orderItemEntity.getAfterSaleExpiryTime());
        orderItemAndSnapshotResp.setOrderType(orderItemEntity.getOrderType());
        orderItemAndSnapshotResp.setDeliveryQuantity(orderItemEntity.getDeliveryQuantity());
        orderItemAndSnapshotResp.setOrderItemId(orderItemEntity.getId());
// Not mapped TO fields:
// orderItemId
// afterSaleRule
// supplierTenantId
// title
// mainPicture
// specification
// warehouseType
// deliveryType
// goodsType
// specificationUnit
// afterSaleUnit
// supplierName
// supplierSkuId
// supplyPrice
// maxAfterSaleAmount
// skuId
// Not mapped FROM fields:
// id
        if (snapshotEntity != null) {
            orderItemAndSnapshotResp.setSupplierTenantId(snapshotEntity.getSupplierTenantId());
            orderItemAndSnapshotResp.setSupplierSkuId(snapshotEntity.getSupplierSkuId());
            orderItemAndSnapshotResp.setSupplierName(snapshotEntity.getSupplierName());
            orderItemAndSnapshotResp.setTitle(snapshotEntity.getTitle());
            orderItemAndSnapshotResp.setMainPicture(snapshotEntity.getMainPicture());
            orderItemAndSnapshotResp.setSpecification(snapshotEntity.getSpecification());
            if (snapshotEntity.getSpecification() != null && snapshotEntity.getSpecification().contains("_")) {
                orderItemAndSnapshotResp.setSpecification(snapshotEntity.getSpecification().substring(snapshotEntity.getSpecification().indexOf("_") + 1));
            }
            orderItemAndSnapshotResp.setSpecificationUnit(snapshotEntity.getSpecificationUnit());
            orderItemAndSnapshotResp.setWarehouseType(snapshotEntity.getWarehouseType());
            orderItemAndSnapshotResp.setGoodsType(snapshotEntity.getGoodsType());
            orderItemAndSnapshotResp.setDeliveryType(snapshotEntity.getDeliveryType());
            orderItemAndSnapshotResp.setAfterSaleUnit(snapshotEntity.getAfterSaleUnit());
            orderItemAndSnapshotResp.setAfterSaleRule(snapshotEntity.getAfterSaleRule());
            orderItemAndSnapshotResp.setSupplyPrice(snapshotEntity.getSupplyPrice());
            orderItemAndSnapshotResp.setSkuId(snapshotEntity.getSkuId());
            orderItemAndSnapshotResp.setMaxAfterSaleAmount(snapshotEntity.getMaxAfterSaleAmount());
            orderItemAndSnapshotResp.setSkuCode(snapshotEntity.getSkuCode());
            orderItemAndSnapshotResp.setCustomSkuCode(snapshotEntity.getCustomSkuCode());
            orderItemAndSnapshotResp.setItemCode(snapshotEntity.getItemCode());
            orderItemAndSnapshotResp.setPresaleSwitch(snapshotEntity.getPresaleSwitch());
            orderItemAndSnapshotResp.setWeight(snapshotEntity.getWeight());

        }
        return orderItemAndSnapshotResp;
    }

    public static List<OrderItemAndSnapshotResp> convertToItemAndSnapshotRespList(List<OrderItemEntity> orderItemEntities, Map<Long, OrderItemSnapshotEntity> snapshotEntityMap) {

            if (orderItemEntities == null) {
                return Collections.emptyList();
            }
            List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList = new ArrayList<>();
            for (OrderItemEntity orderItemEntity : orderItemEntities) {
                OrderItemSnapshotEntity snapshotEntity = snapshotEntityMap.get(orderItemEntity.getId());
                orderItemAndSnapshotRespList.add(convertToItemAndSnapshotResp(orderItemEntity, snapshotEntity));
            }
            return orderItemAndSnapshotRespList;
    }

    public static List<OrderItemAndSnapshotResp> convertToItemAndSnapshotRespList(List<OrderItemWithSnapshotEntity> itemWithSnapshotEntities) {

        if (itemWithSnapshotEntities == null) {
            return Collections.emptyList();
        }
        List<OrderItemAndSnapshotResp> orderItemAndSnapshotRespList = new ArrayList<>();
        for (OrderItemWithSnapshotEntity orderItemWithSnapshotEntity : itemWithSnapshotEntities) {
            orderItemAndSnapshotRespList.add(toOrderItemAndSnapshotResp(orderItemWithSnapshotEntity));
        }
        return orderItemAndSnapshotRespList;
    }

    public static OrderItemAndSnapshotResp toOrderItemAndSnapshotResp(OrderItemWithSnapshotEntity orderItemWithSnapshotEntity) {
        if (orderItemWithSnapshotEntity == null) {
            return null;
        }
        OrderItemAndSnapshotResp orderItemAndSnapshotResp = new OrderItemAndSnapshotResp();
        orderItemAndSnapshotResp.setOrderItemId(orderItemWithSnapshotEntity.getOrderItemId());
        orderItemAndSnapshotResp.setTenantId(orderItemWithSnapshotEntity.getTenantId());
        orderItemAndSnapshotResp.setOrderId(orderItemWithSnapshotEntity.getOrderId());
        orderItemAndSnapshotResp.setItemId(orderItemWithSnapshotEntity.getItemId());
        orderItemAndSnapshotResp.setItemCode(orderItemWithSnapshotEntity.getItemCode());
        orderItemAndSnapshotResp.setAmount(orderItemWithSnapshotEntity.getAmount());
        orderItemAndSnapshotResp.setPayablePrice(orderItemWithSnapshotEntity.getPayablePrice());
        orderItemAndSnapshotResp.setTotalPrice(orderItemWithSnapshotEntity.getTotalPrice());
        orderItemAndSnapshotResp.setStoreNo(orderItemWithSnapshotEntity.getStoreNo());
        orderItemAndSnapshotResp.setStatus(orderItemWithSnapshotEntity.getStatus());
        orderItemAndSnapshotResp.setCreateTime(orderItemWithSnapshotEntity.getCreateTime());
        orderItemAndSnapshotResp.setUpdateTime(orderItemWithSnapshotEntity.getUpdateTime());
        orderItemAndSnapshotResp.setAfterSaleExpiryTime(orderItemWithSnapshotEntity.getAfterSaleExpiryTime());
        orderItemAndSnapshotResp.setAfterSaleRule(orderItemWithSnapshotEntity.getAfterSaleRule());
        orderItemAndSnapshotResp.setOrderType(orderItemWithSnapshotEntity.getOrderType());
        orderItemAndSnapshotResp.setDeliveryQuantity(orderItemWithSnapshotEntity.getDeliveryQuantity());
        orderItemAndSnapshotResp.setSupplierTenantId(orderItemWithSnapshotEntity.getSupplierTenantId());
        orderItemAndSnapshotResp.setTitle(orderItemWithSnapshotEntity.getTitle());
        orderItemAndSnapshotResp.setMainPicture(orderItemWithSnapshotEntity.getMainPicture());
        orderItemAndSnapshotResp.setSpecification(orderItemWithSnapshotEntity.getSpecification());
        orderItemAndSnapshotResp.setWarehouseType(orderItemWithSnapshotEntity.getWarehouseType());
        orderItemAndSnapshotResp.setDeliveryType(orderItemWithSnapshotEntity.getDeliveryType());
        orderItemAndSnapshotResp.setGoodsType(orderItemWithSnapshotEntity.getGoodsType());
        orderItemAndSnapshotResp.setSpecificationUnit(orderItemWithSnapshotEntity.getSpecificationUnit());
        orderItemAndSnapshotResp.setAfterSaleUnit(orderItemWithSnapshotEntity.getAfterSaleUnit());
        orderItemAndSnapshotResp.setSupplierName(orderItemWithSnapshotEntity.getSupplierName());
        orderItemAndSnapshotResp.setSupplierSkuId(orderItemWithSnapshotEntity.getSupplierSkuId());
        orderItemAndSnapshotResp.setSupplyPrice(orderItemWithSnapshotEntity.getSupplyPrice());
        orderItemAndSnapshotResp.setMaxAfterSaleAmount(orderItemWithSnapshotEntity.getMaxAfterSaleAmount());
        orderItemAndSnapshotResp.setSkuId(orderItemWithSnapshotEntity.getSkuId());
        orderItemAndSnapshotResp.setSkuCode(orderItemWithSnapshotEntity.getSkuCode());
        orderItemAndSnapshotResp.setCustomSkuCode(orderItemWithSnapshotEntity.getCustomSkuCode());
        orderItemAndSnapshotResp.setPresaleSwitch(orderItemWithSnapshotEntity.getPresaleSwitch());
        orderItemAndSnapshotResp.setWeight(orderItemWithSnapshotEntity.getWeight());
        return orderItemAndSnapshotResp;
    }

    public static OrderItem toOrderItem(OrderItemCommandParam orderItemCommandParam) {
        if (orderItemCommandParam == null) {
            return null;
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setId(orderItemCommandParam.getId());
        orderItem.setTenantId(orderItemCommandParam.getTenantId());
        orderItem.setOrderId(orderItemCommandParam.getOrderId());
        orderItem.setItemId(orderItemCommandParam.getItemId());
        orderItem.setAmount(orderItemCommandParam.getAmount());
        orderItem.setPayablePrice(orderItemCommandParam.getPayablePrice());
        orderItem.setTotalPrice(orderItemCommandParam.getTotalPrice());
        orderItem.setStoreNo(orderItemCommandParam.getStoreNo());
        orderItem.setStatus(orderItemCommandParam.getStatus());
        orderItem.setCreateTime(orderItemCommandParam.getCreateTime());
        orderItem.setUpdateTime(orderItemCommandParam.getUpdateTime());
        orderItem.setAfterSaleExpiryTime(orderItemCommandParam.getAfterSaleExpiryTime());
        orderItem.setOrderType(orderItemCommandParam.getOrderType());
        orderItem.setDeliveryQuantity(orderItemCommandParam.getDeliveryQuantity());
        return orderItem;
    }
}
