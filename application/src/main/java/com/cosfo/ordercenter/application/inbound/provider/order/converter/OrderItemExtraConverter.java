package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemExtraEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderItemExtraConverter {
    private OrderItemExtraConverter() {
    }

    public static OrderItemExtraQueryParam convertToQueryParam(OrderItemExtraQueryReq queryReq) {


        if (queryReq == null) {
            return null;
        }
        OrderItemExtraQueryParam orderItemExtraQueryParam = new OrderItemExtraQueryParam();
        orderItemExtraQueryParam.setTenantId(queryReq.getTenantId());
        orderItemExtraQueryParam.setOrderId(queryReq.getOrderId());
        orderItemExtraQueryParam.setCustomerOrderItemIdList(queryReq.getCustomerOrderItemIdList());
        return orderItemExtraQueryParam;
    }

    public static List<OrderItemExtraResp> convertToExtraRespList(List<OrderItemExtraEntity> entityList) {

        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderItemExtraResp> orderItemExtraRespList = new ArrayList<>();
        for (OrderItemExtraEntity orderItemExtraEntity : entityList) {
            orderItemExtraRespList.add(toOrderItemExtraResp(orderItemExtraEntity));
        }
        return orderItemExtraRespList;
    }

    public static OrderItemExtraResp toOrderItemExtraResp(OrderItemExtraEntity orderItemExtraEntity) {
        if (orderItemExtraEntity == null) {
            return null;
        }
        OrderItemExtraResp orderItemExtraResp = new OrderItemExtraResp();
        orderItemExtraResp.setId(orderItemExtraEntity.getId());
        orderItemExtraResp.setTenantId(orderItemExtraEntity.getTenantId());
        orderItemExtraResp.setOrderId(orderItemExtraEntity.getOrderId());
        orderItemExtraResp.setOrderItemId(orderItemExtraEntity.getOrderItemId());
        orderItemExtraResp.setCustomerOrderItemId(orderItemExtraEntity.getCustomerOrderItemId());
        orderItemExtraResp.setCustomerSkuCode(orderItemExtraEntity.getCustomerSkuCode());
        orderItemExtraResp.setSkuCode(orderItemExtraEntity.getSkuCode());
        orderItemExtraResp.setCustomerSkuTitle(orderItemExtraEntity.getCustomerSkuTitle());
        orderItemExtraResp.setCustomerSkuSpecification(orderItemExtraEntity.getCustomerSkuSpecification());
        orderItemExtraResp.setCustomerSkuSpecificationUnit(orderItemExtraEntity.getCustomerSkuSpecificationUnit());
        return orderItemExtraResp;
    }
}
