package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.OrderItemCreateReq;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemSnapshotQueryParam;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderItemSnapshotConverter {
    private OrderItemSnapshotConverter() {
    }

    public static OrderItemSnapshotResp convertToSnapshotResp(OrderItemSnapshotEntity snapshotEntity) {

        if (snapshotEntity == null) {
            return null;
        }
        OrderItemSnapshotResp orderItemSnapshotResp = new OrderItemSnapshotResp();
        orderItemSnapshotResp.setId(snapshotEntity.getId());
        orderItemSnapshotResp.setTenantId(snapshotEntity.getTenantId());
        orderItemSnapshotResp.setOrderItemId(snapshotEntity.getOrderItemId());
        orderItemSnapshotResp.setSkuId(snapshotEntity.getSkuId());
        orderItemSnapshotResp.setSupplierTenantId(snapshotEntity.getSupplierTenantId());
        orderItemSnapshotResp.setSupplierSkuId(snapshotEntity.getSupplierSkuId());
        orderItemSnapshotResp.setAreaItemId(snapshotEntity.getAreaItemId());
        orderItemSnapshotResp.setTitle(snapshotEntity.getTitle());
        orderItemSnapshotResp.setMainPicture(snapshotEntity.getMainPicture());
        orderItemSnapshotResp.setSpecificationUnit(snapshotEntity.getSpecificationUnit());
        orderItemSnapshotResp.setSpecification(snapshotEntity.getSpecification());
        orderItemSnapshotResp.setCreateTime(snapshotEntity.getCreateTime());
        orderItemSnapshotResp.setUpdateTime(snapshotEntity.getUpdateTime());
        orderItemSnapshotResp.setSupplyPrice(snapshotEntity.getSupplyPrice());
        orderItemSnapshotResp.setWarehouseType(snapshotEntity.getWarehouseType());
        orderItemSnapshotResp.setDeliveryType(snapshotEntity.getDeliveryType());
        orderItemSnapshotResp.setSupplierName(snapshotEntity.getSupplierName());
        orderItemSnapshotResp.setMaxAfterSaleAmount(snapshotEntity.getMaxAfterSaleAmount());
        orderItemSnapshotResp.setAfterSaleUnit(snapshotEntity.getAfterSaleUnit());
        orderItemSnapshotResp.setPricingType(snapshotEntity.getPricingType());
        orderItemSnapshotResp.setPricingNumber(snapshotEntity.getPricingNumber());
        orderItemSnapshotResp.setAfterSaleRule(snapshotEntity.getAfterSaleRule());
        orderItemSnapshotResp.setGoodsType(snapshotEntity.getGoodsType());
        orderItemSnapshotResp.setItemCode(snapshotEntity.getItemCode());
        orderItemSnapshotResp.setSkuCode(snapshotEntity.getSkuCode());
        orderItemSnapshotResp.setCustomSkuCode(snapshotEntity.getCustomSkuCode());
        orderItemSnapshotResp.setPresaleSwitch(snapshotEntity.getPresaleSwitch());
// Not mapped FROM fields:
// orderId
// buyMultiple
// buyMultipleSwitch
        return orderItemSnapshotResp;
    }


    public static List<OrderItemSnapshotResp> convertToRespList(List<OrderItemSnapshotEntity> entityList) {

        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderItemSnapshotResp> orderItemSnapshotRespList = new ArrayList<>();
        for (OrderItemSnapshotEntity orderItemSnapshotEntity : entityList) {
            orderItemSnapshotRespList.add(convertToSnapshotResp(orderItemSnapshotEntity));
        }
        return orderItemSnapshotRespList;
    }

    public static OrderItemSnapshotQueryParam convertToParam(OrderItemSnapshotQueryReq queryReq) {

        if (queryReq == null) {
            return null;
        }
        OrderItemSnapshotQueryParam orderItemSnapshotQueryParam = new OrderItemSnapshotQueryParam();
        orderItemSnapshotQueryParam.setSupplierIds(queryReq.getSupplierIds());
        orderItemSnapshotQueryParam.setTenantId(queryReq.getTenantId());
        orderItemSnapshotQueryParam.setOrderId(queryReq.getOrderId());
        return orderItemSnapshotQueryParam;
    }

    public static OrderItemSnapshotAddParam convertToAddParam(OrderItemCreateReq orderItemCreateReq) {

        if (orderItemCreateReq == null) {
            return null;
        }
        OrderItemSnapshotAddParam orderItemSnapshotAddParam = new OrderItemSnapshotAddParam();
        orderItemSnapshotAddParam.setTenantId(orderItemCreateReq.getTenantId());
        orderItemSnapshotAddParam.setSkuId(orderItemCreateReq.getSkuId());
        orderItemSnapshotAddParam.setSupplierTenantId(orderItemCreateReq.getSupplierTenantId());
        orderItemSnapshotAddParam.setSupplierSkuId(orderItemCreateReq.getSupplierSkuId());
        orderItemSnapshotAddParam.setAreaItemId(orderItemCreateReq.getAreaItemId());
        orderItemSnapshotAddParam.setTitle(orderItemCreateReq.getTitle());
        orderItemSnapshotAddParam.setMainPicture(orderItemCreateReq.getMainPicture());
        orderItemSnapshotAddParam.setSpecificationUnit(orderItemCreateReq.getSpecificationUnit());
        orderItemSnapshotAddParam.setSpecification(orderItemCreateReq.getSpecification());
        orderItemSnapshotAddParam.setSupplyPrice(orderItemCreateReq.getSupplyPrice());
        orderItemSnapshotAddParam.setWarehouseType(orderItemCreateReq.getWarehouseType());
        orderItemSnapshotAddParam.setDeliveryType(orderItemCreateReq.getDeliveryType());
        orderItemSnapshotAddParam.setSupplierName(orderItemCreateReq.getSupplierName());
        orderItemSnapshotAddParam.setMaxAfterSaleAmount(orderItemCreateReq.getMaxAfterSaleAmount());
        orderItemSnapshotAddParam.setAfterSaleUnit(orderItemCreateReq.getAfterSaleUnit());
        orderItemSnapshotAddParam.setPricingType(orderItemCreateReq.getPricingType());
        orderItemSnapshotAddParam.setPricingNumber(orderItemCreateReq.getPricingNumber());
        orderItemSnapshotAddParam.setGoodsType(orderItemCreateReq.getGoodsType());
        orderItemSnapshotAddParam.setOrderId(orderItemCreateReq.getOrderId());
        orderItemSnapshotAddParam.setItemCode(orderItemCreateReq.getItemCode());
        orderItemSnapshotAddParam.setBuyMultiple(orderItemCreateReq.getBuyMultiple());
        orderItemSnapshotAddParam.setBuyMultipleSwitch(orderItemCreateReq.getBuyMultipleSwitch());
        orderItemSnapshotAddParam.setSkuCode(orderItemCreateReq.getSkuCode());
        orderItemSnapshotAddParam.setCustomSkuCode(orderItemCreateReq.getCustomSkuCode());
        orderItemSnapshotAddParam.setPresaleSwitch(orderItemCreateReq.getPresaleSwitch());
        orderItemSnapshotAddParam.setWeight(orderItemCreateReq.getWeight());
// Not mapped TO fields:
// id
// orderItemId
// createTime
// updateTime
// afterSaleRule
// Not mapped FROM fields:
// itemId
// amount
// price
// totalPrice
// isEnableApplyAfterSale
// needSendAmount
// orderAfterSaleRule
// afterSaleExpiryTime
// supplySku
// afterSaleFlag
// supplierId
// itemType
// orderType
// combineItemId
// orderCombineItemCreateReq
// itemSaleMode
// customerOrderItemId
// customerSkuCode
// customerSkuTitle
// customerSkuSpecification
// customerSkuSpecificationUnit
        return orderItemSnapshotAddParam;
    }
}
