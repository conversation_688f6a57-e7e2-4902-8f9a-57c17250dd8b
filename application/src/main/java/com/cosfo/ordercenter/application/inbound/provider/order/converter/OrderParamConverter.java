package com.cosfo.ordercenter.application.inbound.provider.order.converter;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.OrderAuditReq;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.param.query.*;

/**
 * <AUTHOR>
 */
public class OrderParamConverter {
    private OrderParamConverter() {
    }

    public static OrderQueryParam convertToOrderQueryParam(OrderQueryReq orderQueryReq) {

        if (orderQueryReq == null) {
            return null;
        }
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        orderQueryParam.setTenantId(orderQueryReq.getTenantId());
        orderQueryParam.setOrderId(orderQueryReq.getOrderId());
        orderQueryParam.setStatus(orderQueryReq.getStatus());
        orderQueryParam.setStoreType(orderQueryReq.getStoreType());
        orderQueryParam.setStoreName(orderQueryReq.getStoreName());
        orderQueryParam.setOrderNo(orderQueryReq.getOrderNo());
        orderQueryParam.setWarehouseType(orderQueryReq.getWarehouseType());
        orderQueryParam.setPhone(orderQueryReq.getPhone());
        orderQueryParam.setStartTime(orderQueryReq.getStartTime());
        orderQueryParam.setEndTime(orderQueryReq.getEndTime());
        orderQueryParam.setCreateStartTime(orderQueryReq.getCreateStartTime());
        orderQueryParam.setCreateEndTime(orderQueryReq.getCreateEndTime());
        orderQueryParam.setStoreIds(orderQueryReq.getStoreIds());
        orderQueryParam.setAccountIds(orderQueryReq.getAccountIds());
        orderQueryParam.setSupplierTenantId(orderQueryReq.getSupplierTenantId());
        orderQueryParam.setSupplierTenantIds(orderQueryReq.getSupplierTenantIds());
        orderQueryParam.setTenantIds(orderQueryReq.getTenantIds());
        orderQueryParam.setStatusList(orderQueryReq.getStatusList());
        orderQueryParam.setPayType(orderQueryReq.getPayType());
        orderQueryParam.setItemIds(orderQueryReq.getItemIds());
        orderQueryParam.setSkuId(orderQueryReq.getSkuId());
        orderQueryParam.setWarehouseNo(orderQueryReq.getWarehouseNo());
        orderQueryParam.setOrderItemIds(orderQueryReq.getOrderItemIds());
        orderQueryParam.setMaxId(orderQueryReq.getMaxId());
        orderQueryParam.setBatchSize(orderQueryReq.getBatchSize());
        orderQueryParam.setOrderNos(orderQueryReq.getOrderNos());
        orderQueryParam.setDeliveryTime(orderQueryReq.getDeliveryTime());
        orderQueryParam.setDeliveryStartTime(orderQueryReq.getDeliveryStartTime());
        orderQueryParam.setDeliveryEndTime(orderQueryReq.getDeliveryEndTime());
        orderQueryParam.setNeOrderId(orderQueryReq.getNeOrderId());
        orderQueryParam.setCustomerOrderIds(orderQueryReq.getCustomerOrderIds());
        orderQueryParam.setOrderSource(orderQueryReq.getOrderSource());
        orderQueryParam.setPlanOrderNo(orderQueryReq.getPlanOrderNo());
        orderQueryParam.setOrderType(orderQueryReq.getOrderType());
        orderQueryParam.setSortOrderIdAsc(orderQueryReq.getSortOrderIdAsc());
        orderQueryParam.setTimeQueryType(orderQueryReq.getTimeQueryType());
        orderQueryParam.setQueryStartTime(orderQueryReq.getQueryStartTime());
        orderQueryParam.setQueryEndTime(orderQueryReq.getQueryEndTime());
        orderQueryParam.setFulfillmentType(orderQueryReq.getFulfillmentType());
        orderQueryParam.setPageNum(orderQueryReq.getPageNum());
        orderQueryParam.setPageSize(orderQueryReq.getPageSize());
        return orderQueryParam;
    }

    public static OrderOmsQueryParam convertToOrderOmsQueryParam(OrderOmsQueryReq orderQueryReq) {

        if (orderQueryReq == null) {
            return null;
        }
        OrderOmsQueryParam orderOmsQueryParam = new OrderOmsQueryParam();
        orderOmsQueryParam.setTenantId(orderQueryReq.getTenantId());
        orderOmsQueryParam.setOrderId(orderQueryReq.getOrderId());
        orderOmsQueryParam.setStatus(orderQueryReq.getStatus());
        orderOmsQueryParam.setStoreType(orderQueryReq.getStoreType());
        orderOmsQueryParam.setStoreName(orderQueryReq.getStoreName());
        orderOmsQueryParam.setOrderNo(orderQueryReq.getOrderNo());
        orderOmsQueryParam.setWarehouseType(orderQueryReq.getWarehouseType());
        orderOmsQueryParam.setPhone(orderQueryReq.getPhone());
        orderOmsQueryParam.setStartTime(orderQueryReq.getStartTime());
        orderOmsQueryParam.setEndTime(orderQueryReq.getEndTime());
        orderOmsQueryParam.setCreateStartTime(orderQueryReq.getCreateStartTime());
        orderOmsQueryParam.setCreateEndTime(orderQueryReq.getCreateEndTime());
        orderOmsQueryParam.setStoreIds(orderQueryReq.getStoreIds());
        orderOmsQueryParam.setAccountIds(orderQueryReq.getAccountIds());
        orderOmsQueryParam.setSupplierTenantId(orderQueryReq.getSupplierTenantId());
        orderOmsQueryParam.setSupplierTenantIds(orderQueryReq.getSupplierTenantIds());
        orderOmsQueryParam.setTenantIds(orderQueryReq.getTenantIds());
        orderOmsQueryParam.setStatusList(orderQueryReq.getStatusList());
        orderOmsQueryParam.setPayType(orderQueryReq.getPayType());
        orderOmsQueryParam.setItemIds(orderQueryReq.getItemIds());
        orderOmsQueryParam.setWarehouseNo(orderQueryReq.getWarehouseNo());
        orderOmsQueryParam.setOrderItemIds(orderQueryReq.getOrderItemIds());
        orderOmsQueryParam.setMaxId(orderQueryReq.getMaxId());
        orderOmsQueryParam.setBatchSize(orderQueryReq.getBatchSize());
        orderOmsQueryParam.setCustomerOrderId(orderQueryReq.getCustomerOrderId());
        orderOmsQueryParam.setPageNum(orderQueryReq.getPageNum());
        orderOmsQueryParam.setPageSize(orderQueryReq.getPageSize());
        return orderOmsQueryParam;
    }

    public static OrderDeliveryQueryParam converterToDeliveryQueryParam(OrderNeedDeliveryReq needDeliveryReq) {

        if (needDeliveryReq == null) {
            return null;
        }
        OrderDeliveryQueryParam orderDeliveryQueryParam = new OrderDeliveryQueryParam();
        orderDeliveryQueryParam.setWarehouseNoList(needDeliveryReq.getWarehouseNoList());
        orderDeliveryQueryParam.setTenantId(needDeliveryReq.getTenantId());
        orderDeliveryQueryParam.setPayTime(needDeliveryReq.getPayTime());
        orderDeliveryQueryParam.setCreateTime(needDeliveryReq.getCreateTime());
        return orderDeliveryQueryParam;
    }

    public static OrderCountQueryParam converterToCountQueryParam(OrderCountReq orderCountReq) {
        if (orderCountReq == null) {
            return null;
        }
        OrderCountQueryParam orderCountQueryParam = new OrderCountQueryParam();
        orderCountQueryParam.setStatusList(orderCountReq.getStatusList());
        orderCountQueryParam.setTenantId(orderCountReq.getTenantId());
        orderCountQueryParam.setStoreId(orderCountReq.getStoreId());
        return orderCountQueryParam;
    }

    public static ItemSaleQuantityQueryParam converterToSalQuantityQueryParam(ItemSaleQuantityReq itemSaleQuantityReq) {


        if (itemSaleQuantityReq == null) {
            return null;
        }
        ItemSaleQuantityQueryParam itemSaleQuantityQueryParam = new ItemSaleQuantityQueryParam();
        itemSaleQuantityQueryParam.setTenantId(itemSaleQuantityReq.getTenantId());
        itemSaleQuantityQueryParam.setMerchantStoreId(itemSaleQuantityReq.getMerchantStoreId());
        itemSaleQuantityQueryParam.setItemIds(itemSaleQuantityReq.getItemIds());
        itemSaleQuantityQueryParam.setStartDay(itemSaleQuantityReq.getStartDay());
        itemSaleQuantityQueryParam.setEndDay(itemSaleQuantityReq.getEndDay());
        return itemSaleQuantityQueryParam;
    }

    public static SupplierOrderTotalQueryParam convertToTotalQueryParam(SupplierOrderTotalReq supplierOrderTotalReq) {

        if (supplierOrderTotalReq == null) {
            return null;
        }
        SupplierOrderTotalQueryParam supplierOrderTotalQueryParam = new SupplierOrderTotalQueryParam();
        supplierOrderTotalQueryParam.setTenantId(supplierOrderTotalReq.getTenantId());
        supplierOrderTotalQueryParam.setSupplierIds(supplierOrderTotalReq.getSupplierIds());
        supplierOrderTotalQueryParam.setStatusList(supplierOrderTotalReq.getStatusList());
        supplierOrderTotalQueryParam.setWarehouseType(supplierOrderTotalReq.getWarehouseType());
        return supplierOrderTotalQueryParam;
    }

    public static OrderCommandParam converterToOrderCommandParam(OrderUpdateReq orderUpdateReq) {

        if (orderUpdateReq == null) {
            return null;
        }
        OrderCommandParam orderCommandParam = new OrderCommandParam();
        orderCommandParam.setId(orderUpdateReq.getId());
        orderCommandParam.setTenantId(orderUpdateReq.getTenantId());
        orderCommandParam.setStoreId(orderUpdateReq.getStoreId());
        orderCommandParam.setAccountId(orderUpdateReq.getAccountId());
        orderCommandParam.setSupplierTenantId(orderUpdateReq.getSupplierTenantId());
        orderCommandParam.setOrderNo(orderUpdateReq.getOrderNo());
        orderCommandParam.setWarehouseType(orderUpdateReq.getWarehouseType());
        orderCommandParam.setPayablePrice(orderUpdateReq.getPayablePrice());
        orderCommandParam.setDeliveryFee(orderUpdateReq.getDeliveryFee());
        orderCommandParam.setTotalPrice(orderUpdateReq.getTotalPrice());
        orderCommandParam.setStatus(orderUpdateReq.getStatus());
        orderCommandParam.setPayType(orderUpdateReq.getPayType());
        orderCommandParam.setOnlinePayChannel(orderUpdateReq.getOnlinePayChannel());
        orderCommandParam.setPayTime(orderUpdateReq.getPayTime());
        orderCommandParam.setDeliveryTime(orderUpdateReq.getDeliveryTime());
        orderCommandParam.setFinishedTime(orderUpdateReq.getFinishedTime());
        orderCommandParam.setCreateTime(orderUpdateReq.getCreateTime());
        orderCommandParam.setUpdateTime(orderUpdateReq.getUpdateTime());
        orderCommandParam.setRemark(orderUpdateReq.getRemark());
        orderCommandParam.setApplyEndTime(orderUpdateReq.getApplyEndTime());
        orderCommandParam.setAutoFinishedTime(orderUpdateReq.getAutoFinishedTime());
        orderCommandParam.setWarehouseNo(orderUpdateReq.getWarehouseNo());
        orderCommandParam.setCombineOrderId(orderUpdateReq.getCombineOrderId());
        orderCommandParam.setOrderType(orderUpdateReq.getOrderType());
        orderCommandParam.setBeginDeliveryTime(orderUpdateReq.getBeginDeliveryTime());
        orderCommandParam.setProfitSharingFinishTime(orderUpdateReq.getProfitSharingFinishTime());
        orderCommandParam.setOrderVersion(orderUpdateReq.getOrderVersion());
        orderCommandParam.setOrderSource(orderUpdateReq.getOrderSource());
        orderCommandParam.setCustomerOrderId(orderUpdateReq.getCustomerOrderId());
// Not mapped TO fields:
// fulfillmentNo
        return orderCommandParam;
    }

    public static OrderStatusUpdateParam convertToOrderStatusUpdateParam(OrderStatusUpdateReq orderStatusUpdateReq) {

        if (orderStatusUpdateReq == null) {
            return null;
        }
        OrderStatusUpdateParam orderStatusUpdateParam = new OrderStatusUpdateParam();
        orderStatusUpdateParam.setStatus(orderStatusUpdateReq.getStatus());
        orderStatusUpdateParam.setOriginStatus(orderStatusUpdateReq.getOriginStatus());
        orderStatusUpdateParam.setTenantId(orderStatusUpdateReq.getTenantId());
        orderStatusUpdateParam.setOrderId(orderStatusUpdateReq.getOrderId());
        orderStatusUpdateParam.setOrderNo(orderStatusUpdateReq.getOrderNo());
        orderStatusUpdateParam.setDeliveryTime(orderStatusUpdateReq.getDeliveryTime());
        return orderStatusUpdateParam;
    }

    public static OrderStatusBatchUpdateParam convertToOrderStatusBatchUpdateParam(OrderStatusBatchUpdateReq statusBatchUpdateReq) {

        if (statusBatchUpdateReq == null) {
            return null;
        }
        OrderStatusBatchUpdateParam orderStatusBatchUpdateParam = new OrderStatusBatchUpdateParam();
        orderStatusBatchUpdateParam.setOrderNos(statusBatchUpdateReq.getOrderNos());
        orderStatusBatchUpdateParam.setOrderIds(statusBatchUpdateReq.getOrderIds());
        orderStatusBatchUpdateParam.setTenantId(statusBatchUpdateReq.getTenantId());
        orderStatusBatchUpdateParam.setUpdateStatus(statusBatchUpdateReq.getUpdateStatus());
        orderStatusBatchUpdateParam.setOriginStatus(statusBatchUpdateReq.getOriginStatus());
        orderStatusBatchUpdateParam.setOriginStatusList(statusBatchUpdateReq.getOriginStatusList());
        return orderStatusBatchUpdateParam;
    }

    public static OrderSelfLiftParam convertToOrderSelfLiftParam(OrderSelfLiftReq orderSelfLiftReq) {

        if (orderSelfLiftReq == null) {
            return null;
        }
        OrderSelfLiftParam orderSelfLiftParam = new OrderSelfLiftParam();
        orderSelfLiftParam.setOrderId(orderSelfLiftReq.getOrderId());
        return orderSelfLiftParam;
    }

    public static OrderAuditCommandParam convertToOrderAuditCommandParam(OrderAuditReq orderAuditReq) {

        if (orderAuditReq == null) {
            return null;
        }
        OrderAuditCommandParam orderAuditCommandParam = new OrderAuditCommandParam();
        orderAuditCommandParam.setOrderId(orderAuditReq.getOrderId());
        orderAuditCommandParam.setOrderNo(orderAuditReq.getOrderNo());
        orderAuditCommandParam.setAccountId(orderAuditReq.getAccountId());
        orderAuditCommandParam.setTenantId(orderAuditReq.getTenantId());
        return orderAuditCommandParam;
    }
}
