package com.cosfo.ordercenter.application.service.aftersale;


import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface NotifyBizService {

    /**
     * 发送售后异常通知
     */
    void sendAfterSaleNotifyMessage(OrderAfterSaleEntity orderAfterSale);


    /**
     * 更新配送时间短信提醒
     * @param sourceOrderList
     * @param deliveryDate
     */
    void updateOrderDeliveryTimeSmsNotify(List<OrderEntity> sourceOrderList, LocalDate deliveryDate);
}
