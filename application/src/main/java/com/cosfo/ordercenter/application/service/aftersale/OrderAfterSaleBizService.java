package com.cosfo.ordercenter.application.service.aftersale;

import com.cosfo.ordercenter.application.service.delivery.dto.OrderAfterSaleServerProviderAuditDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleEnableResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAuditCommandParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleProcessFinishParam;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleBizService {

    /**
     * 校验是否需要退运费
     * @param orderAfterSaleList
     * @return
     */
    boolean validationIsNeedRefundDeliveryFee(List<OrderAfterSaleEntity> orderAfterSaleList);

    /**
     * 售后信息校验
     * 检验数量和金额
     *
     * @param afterSaleEntity
     * @param orderEntity
     * @return
     */
    void orderAfterSaleDataCheck(OrderAfterSaleEntity afterSaleEntity, OrderEntity orderEntity);

    /**
     * 单个订单售后信息批量校验
     *
     * @param afterSaleDTOList
     * @param order
     */
    void orderAfterSaleDataCheckForOneOrder(List<OrderAfterSaleEntity> afterSaleDTOList, OrderEntity order, List<OrderItemEntity> itemList);

    /**
     * 审核拒绝，更新售后单状态
     * @param req
     * @param orderAfterSale
     * @return
     */
    Boolean reviewReject(OrderAfterSaleAuditCommandParam req, OrderAfterSaleEntity orderAfterSale);

    /**
     * 缺货创建售后单
     * @param reqs
     * @param serviceType
     */
    void createAfterSaleOrderIfNeed(List<OrderAfterSaleProcessFinishParam> reqs, Integer serviceType);


    /**
     * 创建关单售后单
     * @param needCloseOrderList
     */
    void createAfterSaleOrderForClose(List<OrderEntity> needCloseOrderList);

    /**
     * 自动审核关单售后单
     * @param needCloseOrder
     */
    void autoReviewSubmissionsForClose(List<OrderEntity> needCloseOrder);

    /**
     * 校验售后申请参数，并填充售后单信息
     * @param orderAfterSaleDTOS
     * @param order
     */
    void checkDataAndBuildDto(List<OrderAfterSaleEntity> orderAfterSaleDTOS, OrderEntity order);

    /**
     * 查询可申请售后信息
     * @param orderAfterSaleEnableApplyParam
     * @return
     */
    Map<Long, OrderAfterSaleEnableResp> queryEnableApplyByApplyParam(OrderAfterSaleEnableApplyParam orderAfterSaleEnableApplyParam);

    /**
     * 检查是否需要服务商确认
     * @param orderAfterSaleServerProviderAuditDTO
     * @return
     */
    boolean checkNeedServiceProviderAudit(OrderAfterSaleServerProviderAuditDTO orderAfterSaleServerProviderAuditDTO);

}
