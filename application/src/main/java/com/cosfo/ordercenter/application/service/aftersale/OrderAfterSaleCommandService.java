package com.cosfo.ordercenter.application.service.aftersale;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleCommandService {

    Long createAfterDeliveryAfterSale(OrderAfterSaleEntity orderAfterSaleDTO);


    /**
     * 批量新增售后单
     * @param orderAfterSaleDTOS
     * @return
     */
    List<Long> createPreDeliveryAfterSale(List<OrderAfterSaleEntity> orderAfterSaleDTOS);



    /**
     * 取消售后单
     * @param orderAfterSaleId
     * @return
     */
    Boolean cancel(Long orderAfterSaleId);

    /**
     * 更新售后单状态
     * @return
     */
    Boolean updateStatus(OrderAfterSaleStatusUpdateReq req);

    /**
     * 更新售后单
     * TODO: 后续需要根据业务操作拆分
     * @param orderAfterSaleUpdateReq
     * @return
     */
    Boolean updateById(OrderAfterSaleUpdateReq orderAfterSaleUpdateReq);

    /**
     * 售后处理完毕
     * @param reqs
     * @return
     */
    Boolean processFinish(List<OrderAfterSaleProcessFinishReq> reqs);

    /**
     * 售后审核
     * @param auditReq
     * @return
     */
    Boolean reviewSubmissions(OrderAfterSaleAuditReq auditReq);

    /**
     * 服务商审核确认
     * @param auditReq
     * @return
     */
    Boolean serviceProviderReviewSubmissions(OrderAfterSaleAuditReq auditReq);

    /**
     * 售后超时订单自动完成
     * @return
     */
    Boolean autoFinished();


    /**
     * 城配仓切仓，更新售后单的城配仓号
     *
     * @param req
     * @return
     */
    Boolean updateAfterSaleStoreNo(OrderAfterSaleUpdateStoreNoReq req);

    /**
     * 开放平台批量新增配送后售后单
     * @param orderAfterSaleBatchReq
     * @return
     */
    List<Long> batchCreateAfterSaleForOpen(OrderAfterSaleBatchReq orderAfterSaleBatchReq);

    /**
     * 修改售后数量
     * @param modifyQuantityReq
     * @return
     */
    Boolean modifyQuantity(OrderAfterSaleModifyQuantityReq modifyQuantityReq);

    /**
     * 回收失败确认退款
     * @param recycleFailRefundReq
     * @return
     */
    Boolean recycleFailRefund(OrderAfterSaleRecycleFailRefundReq recycleFailRefundReq);
}
