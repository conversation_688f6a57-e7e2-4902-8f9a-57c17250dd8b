package com.cosfo.ordercenter.application.service.aftersale;

import com.cosfo.ordercenter.client.req.OrderAfterSaleCalRefundPriceReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleOutQueryDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.QueryResentOrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleOutResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.resp.aftersale.QueryResentOrderAfterSaleResp;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.DubboResponse;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleQueryService {

    List<OrderAfterSaleOutResp> queryOrderAfterSaleInfo(OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO);

    PageInfo<OrderAfterSaleWithOrderResp> queryPage(OrderAfterSalePageQueryReq req);

    /**
     * 根据售后单no查询
     *
     * @param orderAfterSaleNos
     * @return
     */
    List<OrderAfterSaleResp> queryByNos(List<String> orderAfterSaleNos);

    /**
     * 根据售后单id查询
     *
     * @param orderAfterSaleIds
     * @return
     */
   List<OrderAfterSaleResp> queryByIds(List<Long> orderAfterSaleIds);


    BigDecimal calculateRefundPrice(OrderAfterSaleCalRefundPriceReq req);

    /**
     * tms查询三方仓补发售后单信息
     * @param req
     * @return
     */
   List<QueryResentOrderAfterSaleResp> queryResentOrderAfterSaleForTms(QueryResentOrderAfterSaleReq req);
}
