package com.cosfo.ordercenter.application.service.aftersale.executor;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.application.service.aftersale.NotifyBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.stock.StockService;
import com.cosfo.ordercenter.common.constants.OrderAfterSaleConstant;
import com.cosfo.ordercenter.common.enums.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAuditCommandParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleProcessFinishParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleServiceProviderAuditParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.aftersale.service.OrderAfterSaleCommandDomainService;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.facade.FulfillmentOrderQueryFacade;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.req.ValidateCancelAfterSaleOrderReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExchangeAfterSaleExecutor implements OrderAfterSaleExecutor {

    @Resource
    private StockService stockService;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private NotifyBizService notifyBizService;

    @Resource
    private OrderAfterSaleCommandDomainService orderAfterSaleCommandRepository;

    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderQueryRepository orderQueryRepository;

    @Resource
    private FulfillmentOrderQueryFacade fulfillmentOrderQueryFacade;


    @Override
    public List<OrderAfterSaleServiceTypeEnum> serviceType() {
        return Lists.newArrayList(OrderAfterSaleServiceTypeEnum.EXCHANGE);
    }

    @Override
    public Long createOrderAfterSale(OrderAfterSaleEntity afterSaleEntity) {
        afterSaleEntity.setServiceType(serviceType().get(0).getValue());
        afterSaleEntity.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        log.info("创建换货售后单, 转换后对象={}", afterSaleEntity);
        return orderAfterSaleCommandRepository.save(afterSaleEntity);
    }

    @Override
    public List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleEntity> afterSaleEntities) {
        if (CollectionUtils.isEmpty(afterSaleEntities)) {
            return Collections.emptyList();
        }
        // 设置类型和状态
        afterSaleEntities.forEach(afterSaleEntity -> {
            afterSaleEntity.setServiceType(serviceType().get(0).getValue());
            afterSaleEntity.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        });
        log.info("批量创建换货售后单, result={}", JSON.toJSONString(afterSaleEntities));
        return orderAfterSaleCommandRepository.batchAdd(afterSaleEntities);
    }

    @Override
    public Boolean reviewSuccess(OrderAfterSaleAuditCommandParam req) {
        OrderAfterSaleEntity afterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(req.getAfterSaleOrderNo());

        // 只有三方仓订单支持换货
        if (!WarehouseTypeEnum.THREE_PARTIES.getCode().equals(afterSale.getWarehouseType())) {
            throw new BizException("售后单不支持换货");
        }

        OrderEntity order = orderQueryRepository.queryById(afterSale.getOrderId());
        if (order == null) {
            throw new BizException("售后对应订单不存在");
        }

        // 是否线下支付订单
        boolean isOfflinePayOrder = com.cosfo.ordercenter.client.common.PayTypeEnum.OFFLINE_PAY.getCode().equals(order.getPayType());

        // 换货需要出库，先占库存
        // 仓库库存是否充足校验，补发单冻结库存，调RPC接口，成功更新三方处理中，失败抛异常

        if (isOfflinePayOrder || !req.isNeedServiceProviderAudit()) {
            try {
                // TODO xwk 冻结库存
                stockService.lockStockForAfterSale(afterSale, req.getAmount());
            } catch (Exception e) {
                log.error("冻结库存失败，orderAfterSale={}", afterSale, e);
                throw new BizException("库存不足,请和仓库确认库存足够后再试");
            }
        }

        // 如果不需要确认，占用库存成功，变更售后单状态为【三方处理中】9，否则【待确认】12
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(afterSale.getId());
        update.setHandleTime(LocalDateTime.now());
        update.setHandleRemark(req.getHandleRemark());
        update.setRecycleTime(req.getRecycleTime());
        Integer status = OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
        if(isOfflinePayOrder){
            // 线下支付，换货 boss一次审核
            update.setStatus(status);
        }else{
            status = req.isNeedServiceProviderAudit() ? OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue() : OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
            update.setStatus(status);
        }
        update.setAmount(req.getAmount());
        update.setOperatorName(req.getOperatorName());
        update.setResponsibilityType(StringUtils.isNotBlank(req.getResponsibilityType()) ? NumberUtils.toInt(req.getResponsibilityType()) : null);
        orderAfterSaleCommandRepository.updateById(update);
        log.info("变更售后单:{}状态为[{}]", afterSale.getAfterSaleOrderNo(), OrderAfterSaleStatusEnum.getStatusDesc(status));

        // ofc履约中心会监听三方处理中的状态，去生成回收单和补发单，后续换货成功，mq消息通知
        // 消息1：mq回告履约单生成成功，包含城配仓编号和配送日期，saas保存信息
        // 消息2：mq消息回告配送完成，更新状态为已完成4. cosf-mall OfcAfterSaleNewListener

        return true;
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditCommandParam req, OrderAfterSaleEntity orderAfterSale) {
        return orderAfterSaleBizService.reviewReject(req, orderAfterSale);
    }

    @Override
    public boolean cancel(OrderAfterSaleEntity orderAfterSale) {
        OrderEntity order = orderQueryRepository.queryById(orderAfterSale.getOrderId());

        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            Integer serviceType = orderAfterSale.getServiceType();
            if (OrderAfterSaleServiceTypeEnum.verifyInteraction(serviceType)) {
                ValidateCancelAfterSaleOrderReq req = new ValidateCancelAfterSaleOrderReq();
                req.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
                req.setSource(OfcOrderSourceEnum.SAAS_AFTER_SALE);
                if(StringUtils.isNotBlank(order.getCustomerOrderId())){
                    req.setStoreId(order.getStoreId());
                }
                boolean flag = fulfillmentOrderQueryFacade.validateCancelAfterSaleOrder(req);
                if (!flag) {
                    throw new BizException("不好意思，售后单不能取消");
                }
                stockService.releaseStock(orderAfterSale.getAfterSaleOrderNo(), orderAfterSale.getOrderItemId(), SaleStockChangeTypeEnum.MANUAL_CLOSED, orderAfterSale.getAmount(), null, orderAfterSale.getStoreId());
            }

        }
        // 变更状态
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
        log.info("变更售后单为取消状态, afterSaleId={}", orderAfterSale.getId());
        return orderAfterSaleCommandRepository.updateById(update);
    }

    @Override
    public boolean finish(List<OrderAfterSaleProcessFinishParam> reqs) {
        OrderAfterSaleProcessFinishParam req = reqs.get(0);
        String orderAfterSaleNo = req.getOrderAfterSaleNo();
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(orderAfterSaleNo);
        if (reqs.size() != 2) {
            log.error("售后单号:{}换货数据格式有误", orderAfterSaleNo);
            return false;
        }

        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
        StringBuilder recycleDetail = getRecycleDetail(reqs);
        update.setRecycleDetails(recycleDetail.toString());
        // 获取回收详细数据
        Optional<OrderAfterSaleProcessFinishParam> recycleReqOptional = reqs.stream().filter(dto ->
                Objects.nonNull(dto.getItemFinishType()) && DeliveryTypeEnum.RECYCLE.getType().equals(dto.getItemFinishType())).findFirst();
        if (recycleReqOptional.isPresent()) {
            update.setRecyclePicture(recycleReqOptional.get().getRecyclePicture());
            update.setRecycleQuantityDetail(recycleReqOptional.get().getRecycleQuantityDetail());
            update.setRecycleDetails(recycleReqOptional.get().getRecycleDetailMessage());
        }

        update.setFinishedTime(LocalDateTime.now());
        orderAfterSaleCommandRepository.updateById(update);
        log.info("售后单号:{}换货完毕，变更状态为已完成,update:{}", orderAfterSaleNo, JSON.toJSONString(update));

        boolean allNormalFlag = reqs.stream().allMatch(el -> Objects.equals(el.getState(), DeliveryStateEnum.NORMAL.getState()));
        // 发送钉钉消息提示异常售后订单
        if (!allNormalFlag) {
            orderAfterSale.setRecycleDetails(recycleDetail.toString());
            notifyBizService.sendAfterSaleNotifyMessage(orderAfterSale);
        }

        orderAfterSaleBizService.createAfterSaleOrderIfNeed(reqs, orderAfterSale.getServiceType());
        return allNormalFlag;
    }

    private static StringBuilder getRecycleDetail(List<OrderAfterSaleProcessFinishParam> reqs) {
        StringBuilder recycleDetail = new StringBuilder();
        for (OrderAfterSaleProcessFinishParam finishReq : reqs) {
            boolean isNormal = Objects.equals(finishReq.getState(), DeliveryStateEnum.NORMAL.getState());
            boolean isDelivery = Objects.equals(finishReq.getDeliveryType(), DeliveryTypeEnum.DELIVERY.getType());
            String recycleDetails = isDelivery ? OrderAfterSaleConstant.NORMAL_RECYCLE_TEMPLATE : OrderAfterSaleConstant.NORMAL_RECYCLE_TEMPLATE;

            if (isDelivery && !isNormal) {
                recycleDetails = OrderAfterSaleConstant.ABNORMAL_DELIVERY_TEMPLATE + "应配送" + finishReq.getShouldCount() + "缺货" + finishReq.getShortCount() + " ";
            } else if (!isNormal) {
                recycleDetails = OrderAfterSaleConstant.ABNORMAL_RECYCLE_TEMPLATE + finishReq.getRemark() + " ";
            }
            recycleDetail.append(recycleDetails);
        }
        return recycleDetail;
    }

    @Override
    public void serviceProviderPassSubmissions(OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        try {
            stockService.lockStockForAfterSale(orderAfterSale, orderAfterSale.getAmount());
        } catch (Exception e) {
            log.error("换货-冻结库存失败，orderAfterSale={}", orderAfterSale, e);
            throw new BizException("库存不足,请和仓库确认库存足够后再试");
        }
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setServiceProviderAuditTime(LocalDateTime.now());
        orderAfterSaleCommandRepository.updateById(update);

        log.info("变更售后单:{}状态为[三方处理中-9]", orderAfterSale.getAfterSaleOrderNo());
    }
}
