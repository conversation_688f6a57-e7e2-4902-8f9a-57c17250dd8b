package com.cosfo.ordercenter.application.service.aftersale.executor;




import com.cosfo.ordercenter.common.enums.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAuditCommandParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleProcessFinishParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleServiceProviderAuditParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleExecutor {

    List<OrderAfterSaleServiceTypeEnum> serviceType();

    /**
     * 创建售后单
     *
     * @param afterSaleEntity
     * @return
     */
    Long createOrderAfterSale(OrderAfterSaleEntity afterSaleEntity);

    /**
     * 批量创建售后单
     *
     * @param afterSaleEntities
     * @return
     */
    List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleEntity> afterSaleEntities);

    /**
     * 审核通过
     *
     * @param req
     * @return
     */
    Boolean reviewSuccess(OrderAfterSaleAuditCommandParam req);

    /**
     * 审核拒绝
     *
     * @param req
     * @return
     */
    Boolean reviewReject(OrderAfterSaleAuditCommandParam req, OrderAfterSaleEntity orderAfterSale);

    /**
     * 售后单取消
     *
     * @param orderAfterSale
     * @return
     */
    boolean cancel(OrderAfterSaleEntity orderAfterSale);

    /**
     * 售后单完成
     *
     * @return
     */
    boolean finish(List<OrderAfterSaleProcessFinishParam> reqs);

    /**
     * 服务商通过
     * @param orderAfterSaleAuditBO
     */
    void serviceProviderPassSubmissions(OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO);
}
