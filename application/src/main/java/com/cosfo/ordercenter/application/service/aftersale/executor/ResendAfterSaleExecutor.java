package com.cosfo.ordercenter.application.service.aftersale.executor;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.StockRecordType;
import com.cosfo.ordercenter.application.service.aftersale.NotifyBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.stock.StockService;
import com.cosfo.ordercenter.common.constants.OrderAfterSaleConstant;
import com.cosfo.ordercenter.common.enums.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAuditCommandParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleProcessFinishParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleServiceProviderAuditParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.aftersale.service.OrderAfterSaleCommandDomainService;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.facade.*;
import com.cosfo.ordercenter.facade.dto.ProductsMappingDTO;
import com.cosfo.ordercenter.facade.input.ProductMappingQueryInput;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.req.FulfillmentDeliveryInfoReq;
import net.summerfarm.ofc.client.req.InsertAfterSaleLogisticsReq;
import net.summerfarm.ofc.client.req.ValidateCancelAfterSaleOrderReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResendAfterSaleExecutor implements OrderAfterSaleExecutor {

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private StockService stockBizService;
    @Resource
    private NotifyBizService notifyBizService;

    @Resource
    private OrderAfterSaleCommandDomainService afterSaleCommandDomainService;
    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;

    @Resource
    private FulfillmentOrderOperateFacade fulfillmentOrderOperateFacade;
    @Resource
    private FulfillmentOrderQueryFacade fulfillmentOrderQueryFacade;
    @Resource
    private SaleInventoryCenterCommandFacade saleInventoryCenterCommandFacade;
    @Resource
    private StockCommandFacade stockCommandFacade;
    @Resource
    private ProductsMappingQueryFacade productsMappingQueryFacade;
    @Resource
    private StoreQueryFacade storeService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public List<OrderAfterSaleServiceTypeEnum> serviceType() {
        return Lists.newArrayList(OrderAfterSaleServiceTypeEnum.RESEND);
    }

    @Override
    public Long createOrderAfterSale(OrderAfterSaleEntity orderAfterSaleDTO) {
        // 生成售后单
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleDTO;
        orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        orderAfterSale.setServiceType(serviceType().get(0).getValue());
        log.info("创建配送售售后单,orderAfterSale={}", orderAfterSale);
        return afterSaleCommandDomainService.batchAdd(Lists.newArrayList(orderAfterSale)).get(0);
    }

    @Override
    public List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleEntity> orderAfterSaleList) {

        orderAfterSaleList.forEach(orderAfterSale -> {
            orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
            orderAfterSale.setServiceType(serviceType().get(0).getValue());
        });
        log.info("批量创建售后单, orderAfterSaleList={}", JSON.toJSONString(orderAfterSaleList));
        return afterSaleCommandDomainService.batchAdd(orderAfterSaleList);
    }

    @Override
    public Boolean reviewSuccess(OrderAfterSaleAuditCommandParam req) {
        OrderAfterSaleEntity afterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(req.getAfterSaleOrderNo());
        // 补发售后只有【配送后售后】能发起
        if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSale.getAfterSaleType())) {
            throw new BizException("配送前售后不支持补发");
        }

        OrderEntity order = orderQueryRepository.queryById(afterSale.getOrderId());
        if (order == null) {
            throw new BizException("售后对应订单不存在");
        }

        // 是否线下支付订单
        boolean isOfflinePayOrder = com.cosfo.ordercenter.client.common.PayTypeEnum.OFFLINE_PAY.getCode().equals(order.getPayType());

        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(afterSale.getWarehouseType())) {
            InsertAfterSaleLogisticsReq logisticsReq = new InsertAfterSaleLogisticsReq();
            logisticsReq.setAfterSaleOrderNo(req.getAfterSaleOrderNo());
            logisticsReq.setOperator(req.getOperatorName());
            FulfillmentDeliveryInfoReq deliveryInfoReq = new FulfillmentDeliveryInfoReq();
            deliveryInfoReq.setType(req.getDeliveryType());
            deliveryInfoReq.setLogisticsCompany(req.getDeliveryCompany());
            deliveryInfoReq.setLogisticsNo(req.getDeliveryNo());
            deliveryInfoReq.setRemark(req.getRemark());
            logisticsReq.setDeliveryInfo(deliveryInfoReq);
            // rpc调用创建售后补发配送清单
            fulfillmentOrderOperateFacade.insertAfterSaleLogistics(logisticsReq);
            // 变更状态
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
            update.setHandleRemark(req.getHandleRemark());
            update.setOperatorName(req.getOperatorName());
            update.setHandleTime(LocalDateTime.now());
            // 预计补发时间，无仓补发售后没有，只用创建补发物流
            update.setRecycleTime(req.getRecycleTime());
            update.setAmount(req.getAmount());
            afterSaleCommandDomainService.updateById(update);
            log.info("售后单补发成功, 变更状态为4-AUDITED_SUCCESS,afterSaleId={}", afterSale);

            OrderItemEntity orderItem = orderItemQueryRepository.queryById(afterSale.getOrderItemId());

            // 无仓扣库存
            try {
                stockCommandFacade.increaseSelfStock(order.getTenantId(), StockRecordType.AFTER_SALE, orderItem.getItemId(), -req.getAmount(), afterSale.getAfterSaleOrderNo());
//                lockStockForNonWarehouse(afterSale.getTenantId(), order.getItemId(), orderAfterSaleAuditDTO.getAmount(), afterSale.getAfterSaleOrderNo());
            } catch (Exception e) {
                log.error("无仓补发扣库存错误，afterSaleNo=" + afterSale.getAfterSaleOrderNo(), e);
                throw new BizException("库存不足，请确认库存足够后再试");
            }

        } else if (WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(afterSale.getWarehouseType())) {
            // 仓库库存是否充足校验，补发单冻结库存，调RPC接口，成功更新三方处理中，失败抛异常
            try {
                OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = convertToOrderOccupyBySpecifyWarehouseReqDTO(afterSale.getTenantId(), Long.valueOf(order.getWarehouseNo()), order.getOrderNo(), SaleStockChangeTypeEnum.SAAS_AFTER_SALE, afterSale, req.getAmount());
                // 冻结指定仓库库存
                saleInventoryCenterCommandFacade.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
            } catch (Exception e) {
                //捕获异常，前面没有sql修改操作，无需sql回滚
                throw new BizException("库存不足,请和仓库确认库存足够后再试");
            }

            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue());
            update.setHandleRemark(req.getHandleRemark());
            update.setOperatorName(req.getOperatorName());
            update.setHandleTime(LocalDateTime.now());
            update.setRecycleTime(req.getRecycleTime());
            update.setAmount(req.getAmount());
            log.info("自营仓订单审核成功,更新状态为三方处理中-{},afterSaleId={}", update.getStatus(), afterSale.getId());
            return afterSaleCommandDomainService.updateById(update);

            // ofc履约中心会监听三方处理中的状态，去生成出库单，后续补发出库成功，mq消息通知
        } else if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(afterSale.getWarehouseType())) {

            // 仓库库存是否充足校验，补发单冻结库存，调RPC接口，成功更新三方处理中，失败抛异常
            if (isOfflinePayOrder || !req.isNeedServiceProviderAudit()) {
                try {
                    // 冻结库存
                    stockBizService.lockStockForAfterSale(afterSale, req.getAmount());
                } catch (Exception e) {
                    log.error("冻结库存失败，orderAfterSale={}", afterSale, e);
                    throw new BizException("库存不足,请和仓库确认库存足够后再试");
                }
            }

            // 不需要服务商确认 占用库存成功，变更售后单状态为【三方处理中】9，否则【待确认】12
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setHandleTime(LocalDateTime.now());
            update.setHandleRemark(req.getHandleRemark());
            update.setRecycleTime(req.getRecycleTime());
            Integer status = OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
            if(isOfflinePayOrder){
                // 线下支付，补发 boss一次审核
                update.setStatus(status);
            }else{
                status = req.isNeedServiceProviderAudit() ? OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue() : OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
                update.setStatus(status);
            }
            update.setAmount(req.getAmount());
            update.setOperatorName(req.getOperatorName());
            update.setResponsibilityType(StringUtils.isNotBlank(req.getResponsibilityType()) ? NumberUtils.toInt(req.getResponsibilityType()) : null);
            afterSaleCommandDomainService.updateById(update);
            log.info("变更售后单:{}状态为[{}]", afterSale.getAfterSaleOrderNo(), OrderAfterSaleStatusEnum.getStatusDesc(status));

            return true;
            // ofc履约中心会监听三方处理中的状态，去生成出库单，后续补发出库成功，mq消息通知
            // 消息1：mq回告履约单生成成功，包含城配仓编号和配送日期，saas保存信息
            // 消息2：mq消息回告配送完成，更新状态为已完成4. cosf-mall OfcAfterSaleNewListener

        }
        return true;
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditCommandParam req, OrderAfterSaleEntity orderAfterSale) {
        return orderAfterSaleBizService.reviewReject(req, orderAfterSale);
    }

    @Override
    public boolean cancel(OrderAfterSaleEntity orderAfterSale) {
        OrderEntity order = orderQueryRepository.queryById(orderAfterSale.getOrderId());

        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            Integer serviceType = orderAfterSale.getServiceType();
            if (OrderAfterSaleServiceTypeEnum.verifyInteraction(serviceType)) {
                ValidateCancelAfterSaleOrderReq req = new ValidateCancelAfterSaleOrderReq();
                req.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
                req.setSource(OfcOrderSourceEnum.SAAS_AFTER_SALE);
                if(StringUtils.isNotBlank(order.getCustomerOrderId())){
                    req.setStoreId(order.getStoreId());
                }
                boolean flag = fulfillmentOrderQueryFacade.validateCancelAfterSaleOrder(req);
                if (!flag) {
                    throw new BizException("不好意思，售后单不能取消");
                }
//                Long warehouseNo = order.getWarehouseNo() == null ? null : Long.valueOf(order.getWarehouseNo());
                stockBizService.releaseStock(orderAfterSale.getAfterSaleOrderNo(), orderAfterSale.getOrderItemId(), SaleStockChangeTypeEnum.MANUAL_CLOSED, orderAfterSale.getAmount(), null, orderAfterSale.getStoreId());
            }

        }
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
        log.info("售后单[{}]取消售后", orderAfterSale.getId());
        return afterSaleCommandDomainService.updateById(update);
    }

    @Override
    public boolean finish(List<OrderAfterSaleProcessFinishParam> reqs) {
        // 补发
        // 编程事务 + 售后单状态幂等
        try {
            OrderAfterSaleEntity afterSale = transactionTemplate.execute(transactionStatus -> {
                OrderAfterSaleProcessFinishParam req = reqs.get(0);
                String orderAfterSaleNo = req.getOrderAfterSaleNo();
                OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(orderAfterSaleNo);

                // 补发没有给备注 需要自己写
                String recycleDetails = Objects.equals(req.getState(), DeliveryStateEnum.NORMAL.getState()) ? OrderAfterSaleConstant.NORMAL_DELIVERY_TEMPLATE : OrderAfterSaleConstant.ABNORMAL_DELIVERY_TEMPLATE + "应配送" + req.getShouldCount() + "缺货" + req.getShortCount();

                OrderAfterSaleEntity update = new OrderAfterSaleEntity();
                update.setId(orderAfterSale.getId());
                update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
                update.setFinishedTime(LocalDateTime.now());
                update.setRecycleDetails(recycleDetails);
                boolean result = afterSaleCommandDomainService.updateById(update);
                log.info("售后订单:{}补发完成，状态变更为已完成", orderAfterSaleNo);

                if (!result) {
                    throw new BizException("售后单更新失败");
                }

                orderAfterSale.setRecycleDetails(recycleDetails);

                orderAfterSaleBizService.createAfterSaleOrderIfNeed(reqs, orderAfterSale.getServiceType());
                return orderAfterSale;
            });
            // 发送钉钉消息提示异常售后订单
            if (Objects.equals(reqs.get(0).getState(), DeliveryStateEnum.ABNORMAL.getState())) {
                notifyBizService.sendAfterSaleNotifyMessage(afterSale);
            }
        } catch (Exception ex) {
            log.error("补发售后单完成发生异常,req={}", JSON.toJSONString(reqs), ex);
        }
        return true;
    }


    private OrderOccupyBySpecifyWarehouseReqDTO convertToOrderOccupyBySpecifyWarehouseReqDTO(Long tenantId, Long warehouseNo, String orderNo, SaleStockChangeTypeEnum saleStockChangeTypeEnum, OrderAfterSaleEntity afterSale, Integer qty) {
        OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO = new OrderOccupyBySpecifyWarehouseReqDTO();
        orderOccupyBySpecifyWarehouseReqDTO.setTenantId(tenantId);
        orderOccupyBySpecifyWarehouseReqDTO.setWarehouseNo(warehouseNo);
        orderOccupyBySpecifyWarehouseReqDTO.setOrderNo(afterSale.getAfterSaleOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setOrderType(saleStockChangeTypeEnum.getTypeName());
        orderOccupyBySpecifyWarehouseReqDTO.setOperatorNo(afterSale.getAfterSaleOrderNo());
        orderOccupyBySpecifyWarehouseReqDTO.setIdempotentNo(afterSale.getAfterSaleOrderNo());
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = new LinkedList<>();
        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
        OrderItemSnapshotEntity orderItemSnapshot = orderItemSnapshotQueryRepository.queryByOrderItemId(afterSale.getOrderItemId());
        ProductMappingQueryInput queryReq = new ProductMappingQueryInput();
        queryReq.setSkuIds(Collections.singletonList(orderItemSnapshot.getSkuId()));
        List<ProductsMappingDTO> skuMappings = productsMappingQueryFacade.selectMappingList(queryReq);
        if (CollectionUtils.isEmpty(skuMappings) || StringUtils.isEmpty(skuMappings.get(0).getSku())) {
            throw new BizException("找不到SkuCode");
        }
        orderOccupySkuDetailReqDTO.setSkuCode(skuMappings.get(0).getSku());
        orderOccupySkuDetailReqDTO.setOccupyQuantity(qty);
        orderOccupySkuDetailReqDTOS.add(orderOccupySkuDetailReqDTO);
        orderOccupyBySpecifyWarehouseReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);
        orderOccupyBySpecifyWarehouseReqDTO.setOperatorName(storeService.queryStoreName(afterSale.getStoreId()));
        return orderOccupyBySpecifyWarehouseReqDTO;
    }

    @Override
    public void serviceProviderPassSubmissions(OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        try {
            stockBizService.lockStockForAfterSale(orderAfterSale, orderAfterSale.getAmount());
        } catch (Exception e) {
            log.error("补发-冻结库存失败，orderAfterSale={}", orderAfterSale, e);
            throw new BizException("库存不足,请和仓库确认库存足够后再试");
        }
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setServiceProviderAuditTime(LocalDateTime.now());
        afterSaleCommandDomainService.updateById(update);

        log.info("变更售后单:{}状态为[三方处理中-9]", orderAfterSale.getAfterSaleOrderNo());
    }
}
