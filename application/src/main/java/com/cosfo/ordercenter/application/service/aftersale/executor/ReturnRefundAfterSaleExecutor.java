package com.cosfo.ordercenter.application.service.aftersale.executor;

import com.cosfo.ordercenter.application.service.aftersale.NotifyBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.common.enums.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAuditCommandParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleProcessFinishParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleServiceProviderAuditParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.aftersale.service.OrderAfterSaleCommandDomainService;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.cosfo.ordercenter.common.enums.OrderAfterSaleServiceTypeEnum.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReturnRefundAfterSaleExecutor implements OrderAfterSaleExecutor {

    @Resource
    private OrderQueryRepository orderQueryRepository;

    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderAfterSaleCommandDomainService afterSaleCommandDomainService;

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private NotifyBizService notifyBizService;

    private final static Map<Integer, OrderAfterSaleServiceTypeEnum> serviceTypeMap = new HashMap<>();

    @PostConstruct
    void initServiceMap() {
        serviceTypeMap.put(1, RETURN_REFUND);
        serviceTypeMap.put(2, RETURN_REFUND_ENTER_BILL);
        serviceTypeMap.put(3, RETURN_REFUND_BALANCE);
        serviceTypeMap.put(4, RETURN_REFUND);
        serviceTypeMap.put(5, RETURN_REFUND);
        serviceTypeMap.put(6, RETURN_REFUND);
    }

    @Override
    public List<OrderAfterSaleServiceTypeEnum> serviceType() {
        return Lists.newArrayList(RETURN_REFUND, RETURN_REFUND_ENTER_BILL, RETURN_REFUND_BALANCE);
    }

    @Override
    public Long createOrderAfterSale(OrderAfterSaleEntity orderAfterSaleDTO) {
        // 生成售后单
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleDTO;
        orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        // 确定入账类型
        OrderEntity order = orderQueryRepository.queryById(orderAfterSale.getOrderId());
        Integer payType = order.getPayType();
        orderAfterSale.setServiceType(serviceTypeMap.get(payType).getValue());
        log.info("退货退款创建售后单, afterSale={}", orderAfterSale);
        return afterSaleCommandDomainService.batchAdd(Lists.newArrayList(orderAfterSale)).get(0);
    }

    @Override
    public List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleEntity> orderAfterSaleList) {
        throw new BizException("配送后售暂不支持批量处理");
//        boolean openApiReq = orderAfterSaleList.stream().allMatch(orderAfterSaleDTO -> Constant.OPEN_API_REQ_SOURCE.equals(orderAfterSaleDTO.getReqSource()));
//        if (!openApiReq) {
//            throw new BizException("配送后售后暂不支持批量处理");
//        }
//
//        orderAfterSaleList.forEach(orderAfterSaleDTO -> {
//            String afterSaleOrderNo = Constant.createOrderNo(Constant.NORMAL_ORDER_AFTER_SALE_CODE);
//            orderAfterSaleDTO.setAfterSaleOrderNo(afterSaleOrderNo);
//        });
//
//        Long orderId = orderAfterSaleList.get(0).getOrderId();
//        Order order = orderDao.getById(orderId);
//        Integer payType = order.getPayType();
//
//        List<OrderAfterSale> addOrderAfterSaleList = orderAfterSaleList.stream().map(orderAfterSaleDTO -> {
//            OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(orderAfterSaleDTO);
//            orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
//            orderAfterSale.setServiceType(serviceTypeMap.get(payType).getValue());
//            return orderAfterSale;
//        }).collect(Collectors.toList());
//        log.info("退货退款创建售后单, addOrderAfterSaleList={}", addOrderAfterSaleList);
//        return orderAfterSaleDao.batchAdd(addOrderAfterSaleList);
    }

    @Override
    public Boolean reviewSuccess(OrderAfterSaleAuditCommandParam req) {
        OrderAfterSaleEntity afterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(req.getAfterSaleOrderNo());
        OrderEntity order = orderQueryRepository.queryById(afterSale.getOrderId());

        // 配送仓类型
        Integer warehouseType = afterSale.getWarehouseType();
        // 售后类型
        Integer afterSaleType = afterSale.getAfterSaleType();
        // 售后状态
        Integer afterSaleStatus = afterSale.getStatus();

        // 退货退款售后只有【配送后售后】能发起
        if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSaleType)) {
            throw new BizException("配送前售后不支持退货退款");
        }

        // 三方仓审核处理
        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {

            // 变更售后单状态为【三方处理中】9
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setTotalPrice(req.getTotalPrice());
            update.setAmount(req.getAmount());
            update.setHandleTime(LocalDateTime.now());
            update.setHandleRemark(req.getHandleRemark());
            Integer status;
            if(com.cosfo.ordercenter.client.common.PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ())){
                status =  OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue();
            }else{
                status = req.isNeedServiceProviderAudit() ? OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue() : OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
            }
            update.setStatus(status);
            update.setRecycleTime(req.getRecycleTime());
            update.setResponsibilityType(StringUtils.isNotBlank(req.getResponsibilityType()) ? NumberUtils.toInt(req.getResponsibilityType()) : null);
            update.setRefundReceipt (req.getRefundReceipt());
            afterSaleCommandDomainService.updateById(update);
            log.info("变更售后单:{}状态为[{}]", afterSale.getAfterSaleOrderNo(), OrderAfterSaleStatusEnum.getStatusDesc(status));


            // ofc履约中心会监听三方处理中的状态，去生成回收单，后续回收成功，mq消息通知
            // 消息：mq消息回告配送完成，更新状态为已完成4，并退款操作. cosf-mall OfcAfterSaleNewListener
            return true;
        }

        //----------------- 以下为【无仓】【自营仓】审核处理 ------------------

        // 如果是【待退款】状态，说明是第二次审核
        boolean secondAuditFlag = OrderAfterSaleStatusEnum.WAIT_REFUND.getValue().equals(afterSaleStatus);

        // 退货退款两次审核，第一次审核仅更新状态为【待退货】，二次审核，通过直接退款，二次审核前状态为【待退款】（mq消息监听等待退货单入库成功，状态更新为【待退款】）
        if (!secondAuditFlag) {
            log.info("退货退款售后单第一次审核,afterSaleId={}", afterSale.getId());
            if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType) && req.getReturnAddressId() == null) {
                throw new BizException("退回地址必填");
            }
            // 更新为【待退货】
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setStatus(OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue());
            update.setHandleRemark(req.getHandleRemark());
            update.setHandleTime(LocalDateTime.now());
            update.setRecycleTime(req.getRecycleTime());
            update.setTotalPrice(req.getTotalPrice());
            update.setAmount(req.getAmount());
            update.setReturnAddressId(req.getReturnAddressId());
            update.setReturnWarehouseNo(req.getReturnWarehouseNo());
            update.setRefundReceipt (req.getRefundReceipt());
            afterSaleCommandDomainService.updateById(update);
        } else {
            if (WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(warehouseType)) {
                // 自营仓校验入库数量参数
                if (req.getQuantity() == null || req.getActualQuantity() == null) {
                    throw new BizException("入库数量不能为空");
                }
            }
            // 无论无仓有仓，第二次审核，统一发起退款
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(afterSale.getId());
            update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getValue());
            update.setSecondHandleRemark(req.getHandleRemark());
            update.setApplyQuantity(req.getQuantity());
            update.setAmount(req.getActualQuantity());
            update.setUpdateTime(LocalDateTime.now());
            update.setRefundReceipt (req.getRefundReceipt());
            afterSaleCommandDomainService.updateById(update);
            log.info("退货退款第二次售后，售后单状态变更为退款中[{}]", update.getId());
//            orderAsyncService.payRefund(afterSale.getId(), afterSale.getOrderId(), afterSale.getTenantId(), req.getTotalPrice());
        }

        return true;
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditCommandParam req, OrderAfterSaleEntity orderAfterSale) {
        return orderAfterSaleBizService.reviewReject(req, orderAfterSale);
    }

    @Override
    public boolean cancel(OrderAfterSaleEntity orderAfterSale) {
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
        log.info("售后单[]{}更新为取消", update.getId());
        return afterSaleCommandDomainService.updateById(update);
    }

    @Override
    public boolean finish(List<OrderAfterSaleProcessFinishParam> reqs) {

        OrderAfterSaleProcessFinishParam req = reqs.get(0);
        String orderAfterSaleNo = req.getOrderAfterSaleNo();
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(orderAfterSaleNo);
        // 退货回收
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setRecyclePicture(req.getRecyclePicture());
        update.setRecycleQuantityDetail(req.getRecycleQuantityDetail());
        if (Objects.equals(req.getState(), DeliveryStateEnum.NORMAL.getState())) {
            update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getValue());
            update.setRecycleDetails(Optional.ofNullable(req.getRecycleDetailMessage()).orElse("回收状态:正常"));
            afterSaleCommandDomainService.updateById(update);
            log.info("售后单号：{}回收完毕，开始退款", orderAfterSale.getAfterSaleOrderNo());
            // 成功发起退款
            return true;
        }

        // 回收失败 变更订单状态为待退款
        update.setStatus(OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());
        update.setRecycleDetails(Optional.ofNullable(req.getRecycleDetailMessage()).orElse("回收状态:异常；" + req.getRemark()));
        afterSaleCommandDomainService.updateById(update);
        log.info("回收失败,售后单[{}]变更为待退款[{}]", update.getId(), update.getStatus());

        // 发送钉钉消息提示异常售后订单
        orderAfterSale.setRecycleDetails(update.getRecycleDetails());
        notifyBizService.sendAfterSaleNotifyMessage(orderAfterSale);
        return false;
    }

    @Override
    public void serviceProviderPassSubmissions(OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(orderAfterSale.getId());
        update.setTotalPrice(orderAfterSaleAuditBO.getTotalPrice());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue());
        update.setServiceProviderAuditTime(LocalDateTime.now());
        afterSaleCommandDomainService.updateById(update);
        log.info("变更售后单:{}状态为[三方处理中-9]", orderAfterSale.getAfterSaleOrderNo());
    }
}
