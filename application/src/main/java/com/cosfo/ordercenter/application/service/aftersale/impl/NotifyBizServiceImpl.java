package com.cosfo.ordercenter.application.service.aftersale.impl;


import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.manage.client.sms.SmsSenderProvider;
import com.cosfo.manage.client.sms.req.SmsReq;
import com.cosfo.oms.client.provider.system.SystemParameterQueryProvider;
import com.cosfo.oms.client.resp.SystemParameterResultResp;
import com.cosfo.ordercenter.application.service.aftersale.NotifyBizService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.common.util.DingTalkRobotUtil;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreAccountQueryProvider;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class NotifyBizServiceImpl implements NotifyBizService {

    @DubboReference
    private SystemParameterQueryProvider systemParameterQueryProvider;
    @DubboReference
    private TenantQueryProvider tenantQueryProvider;
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @DubboReference
    private SmsSenderProvider smsSenderProvider;
    @DubboReference
    private MerchantStoreAccountQueryProvider merchantStoreAccountQueryProvider;


    @Async(value = "notifyPool")
    @Override
    public void sendAfterSaleNotifyMessage(OrderAfterSaleEntity orderAfterSale) {
        try {
            SystemParameterResultResp afterSaleRobotUrl = RpcResultUtil.handle(systemParameterQueryProvider.selectByKey("after_sale_robot_url"));
            // 消息体信息查询

            List<TenantResultResp> tenantResp = RpcResultUtil.handle(tenantQueryProvider.getTenantsByIds(Lists.newArrayList(orderAfterSale.getTenantId())));
            TenantResultResp tenantResultResp = tenantResp.get(0);
            Long orderId = orderAfterSale.getOrderId();
            Order order = orderDao.getById(orderId);
            OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderAfterSale.getOrderItemId());

            StringBuffer title = new StringBuffer();
            title.append("您有一条SaaS售后单出现异常，请关注");
            StringBuffer text = new StringBuffer();
            text.append("> 商城名称: " + tenantResultResp.getTenantName() + "\n\n");
            text.append("> 订单号: " + order.getOrderNo() + " \n\n");
            text.append("> 售后单号: " + orderAfterSale.getAfterSaleOrderNo() + " \n\n");
            text.append("> 售后类型: " + OrderAfterSaleTypeEnum.getDesc(orderAfterSale.getAfterSaleType()) + " \n\n");
            text.append("> 售后方式: " + OrderAfterSaleServiceTypeEnum.getDesc(orderAfterSale.getServiceType()) + " \n\n");
            text.append("> 售后商品: " + orderItemSnapshot.getTitle() + " \n\n");
            text.append("> 商品规格: " + orderItemSnapshot.getSpecification() + " \n\n");
            text.append("> " + orderAfterSale.getRecycleDetails() + " \n\n");
            text.append("您有一条SaaS售后单出现异常，请及时进行售后处理下");
            HashMap<String, String> msgMap = new HashMap<>(2);
            msgMap.put("title", title.toString());
            msgMap.put("text", text.toString());
            DingTalkRobotUtil.sendMarkDownMsg(afterSaleRobotUrl.getParamValue(), () -> msgMap, null);
        } catch (Exception exception) {
            log.warn("钉钉通知发送失败,", exception);
        }
    }


    @Async
    public void updateOrderDeliveryTimeSmsNotify(List<OrderEntity> sourceOrderList, LocalDate deliveryDate) {
        if (CollectionUtils.isEmpty(sourceOrderList)) {
            return;
        }

        List<Long> accountIds = sourceOrderList.stream().map(OrderEntity::getAccountId).distinct().collect(Collectors.toList());
        List<MerchantStoreAccountResultResp> storeAccountResultResps = RpcResponseUtil.handler(merchantStoreAccountQueryProvider.getMerchantStoreAccountByIds(accountIds));
        if (CollectionUtils.isEmpty(storeAccountResultResps)) {
            return;
        }

        Map<Long, String> accountId2PhoneMap = storeAccountResultResps.stream().collect(Collectors.toMap(MerchantStoreAccountResultResp::getId, MerchantStoreAccountResultResp::getPhone, (v1, v2) -> v1));

        String targetDeliveryDate = deliveryDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        for (OrderEntity order : sourceOrderList) {
            if (order.getDeliveryTime() == null) {
                continue;
            }

            String sourceDeliveryDate = order.getDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 配送日期无变化，跳过
            if (sourceDeliveryDate.equals(targetDeliveryDate)) {
                continue;
            }

            String phone = accountId2PhoneMap.get(order.getAccountId());
            if (StringUtils.isEmpty(phone)) {
                continue;
            }

            try {
                SmsReq req = new SmsReq();
                req.setPhone(phone);
                req.setArgs(Arrays.asList(order.getOrderNo(), sourceDeliveryDate, targetDeliveryDate));
                req.setSceneId(7L);
                RpcResponseUtil.handler(smsSenderProvider.sendSms(req));
            } catch (Exception e) {
                log.warn("修改配送日期发送短信提醒异常", e);
            }
        }
    }
}
