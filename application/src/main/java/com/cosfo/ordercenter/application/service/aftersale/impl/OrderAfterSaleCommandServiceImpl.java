package com.cosfo.ordercenter.application.service.aftersale.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleCommandService;
import com.cosfo.ordercenter.application.service.aftersale.executor.OrderAfterSaleExecutorContext;
import com.cosfo.ordercenter.application.service.delivery.dto.OrderAfterSaleServerProviderAuditDTO;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.common.constants.Constant;
import com.cosfo.ordercenter.common.constants.NumberConstant;
import com.cosfo.ordercenter.common.enums.RedisKeyEnum;
import com.cosfo.ordercenter.common.exception.OpenApiErrorCode;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.*;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.aftersale.service.OrderAfterSaleCommandDomainService;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.facade.OrderAfterSaleFlagFacade;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleCommandServiceImpl implements OrderAfterSaleCommandService {

    private static HashSet<OrderAfterSaleStatusEnum> AFTER_SALE_PROCESS_STATUS = Sets.newHashSet(INVENTORY_DEALING, REFUNDING, AUDITED_SUCCESS, AUDITED_FAILED, CANCEL, WAIT_REFUND);

    private static HashSet<Integer> AFTER_SALE_FAIL_STATUS = Sets.newHashSet(CANCEL.getValue(), AUDITED_FAILED.getValue());

    // 待审核状态，包括待审核和待确认
    private static HashSet<Integer> UNAUDITED_STATUS = Sets.newHashSet(UNAUDITED.getValue(), WAIT_CONFIRM.getValue());

    @Resource
    private OrderAfterSaleExecutorContext orderAfterSaleExecutorContext;
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;

    @Resource
    private OrderAfterSaleCommandDomainService orderAfterSaleCommandDomainService;


    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;
    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;

    @Resource
    private OrderAfterSaleFlagFacade orderAfterSaleFlagFacade;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedissonClient redissonClient;

    @Override
    public Long createAfterDeliveryAfterSale(OrderAfterSaleEntity orderAfterSaleDTO) {
        String afterSaleOrderNo = Constant.createOrderNo(Constant.NORMAL_ORDER_AFTER_SALE_CODE);
        orderAfterSaleDTO.setAfterSaleOrderNo(afterSaleOrderNo);
        Integer serviceType = orderAfterSaleDTO.getServiceType();

        OrderEntity order = orderQueryRepository.queryById(orderAfterSaleDTO.getOrderId());
        //补充信息
        orderAfterSaleDTO.setTenantId(order.getTenantId());
//        orderAfterSaleDTO.setOrderId(order.getId());
        orderAfterSaleDTO.setStoreId(order.getStoreId());
        orderAfterSaleDTO.setAccountId(order.getAccountId());
        orderAfterSaleDTO.setTotalPrice(orderAfterSaleDTO.getApplyPrice());
        orderAfterSaleDTO.setWarehouseType(order.getWarehouseType());
        orderAfterSaleDTO.setOrderId(ObjectUtil.isNotNull(orderAfterSaleDTO.getOrderId()) ? orderAfterSaleDTO.getOrderId() : order.getId());
        orderAfterSaleDTO.setReason(ObjectUtil.isNotNull(orderAfterSaleDTO.getReason()) ? orderAfterSaleDTO.getReason() : "拍多/拍错/不想要");
        if (!StringUtils.isEmpty(order.getWarehouseNo())) {
            orderAfterSaleDTO.setReturnWarehouseNo(String.valueOf(order.getWarehouseNo()));
        }

        orderAfterSaleDTO.setAutoFinishedTime(LocalDateTime.now().plusDays(order.getAutoFinishedTime()));
        orderAfterSaleBizService.orderAfterSaleDataCheck(orderAfterSaleDTO, order);

        Long afterSaleId;
        try {
            afterSaleId = transactionTemplate.execute(status -> {
                Long orderAfterSaleId = orderAfterSaleExecutorContext.load(serviceType).createOrderAfterSale(orderAfterSaleDTO);

                return orderAfterSaleId;
            });
            if (afterSaleId == null) {
                throw new BizException("创建售后失败");
            }
        } catch (Exception ex) {
            throw new BizException("创建售后失败");
        }
        return afterSaleId;
    }

    @Override
    public List<Long> createPreDeliveryAfterSale(List<OrderAfterSaleEntity> orderAfterSaleDTOS) {
        // 批量仅支持配送前售后
        if (CollectionUtils.isEmpty(orderAfterSaleDTOS)) {
            log.warn("售后申请数据为空");
            return Collections.emptyList();
        }

        // 统计 orderItem 数量
        boolean hasDuplicate = orderAfterSaleDTOS.stream().map(OrderAfterSaleEntity::getOrderItemId).distinct().count() != orderAfterSaleDTOS.size();
        if (hasDuplicate) {
            log.warn("售后申请数据存在重复 orderItemId={}", JSON.toJSON(orderAfterSaleDTOS));
            throw new BizException("售后申请提交失败了，请刷新后重试");
        }

        OrderEntity order = orderQueryRepository.queryById(orderAfterSaleDTOS.get(0).getOrderId());
        if (order == null) {
            log.error("售后单对应订单不存在, afterSaleList={}", JSON.toJSON(orderAfterSaleDTOS));
            throw new BizException("售后单对应订单不存在");
        }
        Integer serviceType = orderAfterSaleDTOS.get(0).getServiceType();
        Integer afterSaleType = orderAfterSaleDTOS.get(0).getAfterSaleType();
        String reason = orderAfterSaleDTOS.get(0).getReason();

        List<Long> result = new ArrayList<>();
        List<String> keys = Lists.newArrayList();
        if (OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType())) {
            // 处理组合包逻辑
            // 组合包配送前售后需要售后全部订单子项，直接取 orderItem 拼装售后信息
            List<OrderEntity> combineOrderList = orderQueryRepository.queryByCombineId(order.getCombineOrderId(), order.getTenantId());
            log.info("组合包订单售后, OrderList={}", combineOrderList);
            for (OrderEntity combineOrder : combineOrderList) {
                //判断配送状态
                if (!OrderStatusEnum.ableApplyNotSendAfterSale(combineOrder.getStatus())) {
                    log.warn("订单id={}不支持批量接口申请售后, 订单当前状态={}", combineOrder.getId(), combineOrder.getStatus());
                    throw new BizException("不好意思，组合包订单存在已配送订单，不能退款");
                }
            }
            List<OrderItemEntity> orderItems = orderItemQueryRepository.batchQueryByOrderIds(combineOrderList.stream().map(OrderEntity::getId).collect(Collectors.toList()));
            Map<Long, List<OrderItemEntity>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemEntity::getOrderId));
            try {
                lockOrder(keys, combineOrderList.stream().map(OrderEntity::getId).collect(Collectors.toList()));
                // 拼装组合包订单售后单, 过滤申请订单
                for (OrderEntity combineOrder : combineOrderList) {
                    List<OrderItemEntity> itemList = orderItemMap.get(combineOrder.getId());
                    // 校验改成批量
                    List<OrderAfterSaleEntity> combineAfterSaleList = itemList.stream().map(orderItem -> OrderAfterSaleConverter.buildOrderAfterSaleDTO(combineOrder, orderItem, afterSaleType, serviceType, reason)).collect(Collectors.toList());
                    orderAfterSaleBizService.orderAfterSaleDataCheckForOneOrder(combineAfterSaleList, combineOrder, itemList);
                    List<Long> combineResult = transactionTemplate.execute(status -> orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(combineAfterSaleList));
                    if (combineResult == null) {
                        throw new BizException("创建售后单失败");
                    }
                    result.addAll(combineResult);

                }
            } catch (ProviderException providerException) {
                throw providerException;
            } catch (Exception ex) {
                throw new BizException("创建售后单失败", ex);
            } finally {
                unlockOrder(keys);
            }
        } else {
            log.info("普通订单配送前售后");
            // 原始售后单先处理
            LocalDateTime autoFinishedTime = LocalDateTime.now().plusDays(order.getAutoFinishedTime());
            orderAfterSaleDTOS.forEach(orderAfterSaleDTO -> {
                orderAfterSaleDTO.setAutoFinishedTime(autoFinishedTime);
            });
            try {
                lockOrder(keys, Lists.newArrayList(order.getId()));
                List<OrderItemEntity> orderItems = orderItemQueryRepository.queryByIds(orderAfterSaleDTOS.stream().map(OrderAfterSaleEntity::getOrderItemId).collect(Collectors.toList()));
                orderAfterSaleBizService.orderAfterSaleDataCheckForOneOrder(orderAfterSaleDTOS, order, orderItems);
                result = transactionTemplate.execute(status -> orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(orderAfterSaleDTOS));
                if (result == null) {
                    throw new BizException("创建售后单失败");
                }
            } catch (ProviderException providerException) {
                throw providerException;
            }catch (Exception ex) {
                throw new BizException("创建售后单失败", ex);
            } finally {
                unlockOrder(keys);
            }


        }
        return result;
    }

    @Override
    public Boolean cancel(Long orderAfterSaleId) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryById(orderAfterSaleId);
        if (ObjectUtil.isNull(orderAfterSale)) {
            log.error("售后单不存在 orderAfterSaleId={}", orderAfterSaleId);
            throw new BizException("售后单不存在");
        }

        OrderAfterSaleStatusEnum status = fromCode(orderAfterSale.getStatus());
        // 退款中 已同意 已拒绝 已取消 待退款 都不支持取消
        if (AFTER_SALE_PROCESS_STATUS.contains(status)) {
            throw new BizException("该售后单已经处理中，不能取消");
        }

        if (UNAUDITED_STATUS.contains(orderAfterSale.getStatus())) {
            OrderAfterSaleEntity update = new OrderAfterSaleEntity();
            update.setId(orderAfterSaleId);
            update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
            return orderAfterSaleCommandDomainService.updateById(update);
        }

        return orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).cancel(orderAfterSale);
    }

    @Override
    public Boolean updateStatus(OrderAfterSaleStatusUpdateReq req) {
        OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateCommandParam = OrderAfterSaleConverter.convertToStatusUpdateCommandParam(req);
        return orderAfterSaleCommandDomainService.updateStatus(orderAfterSaleStatusUpdateCommandParam);
    }

    @Override
    public Boolean updateById(OrderAfterSaleUpdateReq orderAfterSaleUpdateReq) {
        OrderAfterSaleEntity orderAfterSaleEntity = OrderAfterSaleConverter.convertToEntity(orderAfterSaleUpdateReq);
        if (nullObjCheck(orderAfterSaleEntity)) {
            log.warn("售后单更新数据为空, orderAfterSaleId={}", orderAfterSaleEntity, new BizException("售后单更新数据为空"));
            return false;
        }
        return orderAfterSaleCommandDomainService.updateById(orderAfterSaleEntity);
    }

    private boolean nullObjCheck(OrderAfterSaleEntity afterSale) {
        if (afterSale.getTenantId() == null
                && afterSale.getOrderId() == null
                && afterSale.getOrderItemId() == null
                && afterSale.getStoreId() == null
                && afterSale.getAccountId() == null
                && afterSale.getAfterSaleOrderNo() == null
                && afterSale.getAmount() == null
                && afterSale.getAfterSaleType() == null
                && afterSale.getServiceType() == null
                && afterSale.getApplyPrice() == null
                && afterSale.getTotalPrice() == null
                && afterSale.getDeliveryFee() == null
                && afterSale.getReason() == null
                && afterSale.getUserRemark() == null
                && afterSale.getProofPicture() == null
                && afterSale.getStatus() == null
                && afterSale.getHandleRemark() == null
                && afterSale.getOperatorName() == null
                && afterSale.getCreateTime() == null
                && afterSale.getUpdateTime() == null
                && afterSale.getFinishedTime() == null
                && afterSale.getHandleTime() == null
                && afterSale.getRecycleTime() == null
                && afterSale.getRecycleDetails() == null
                && afterSale.getResponsibilityType() == null
                && afterSale.getStoreNo() == null
                && afterSale.getWarehouseType() == null
                && afterSale.getAutoFinishedTime() == null
                && afterSale.getApplyQuantity() == null
                && afterSale.getAdminRemark() == null
                && afterSale.getAdminRemarkTime() == null
                && afterSale.getSecondHandleRemark() == null
                && afterSale.getReturnAddressId() == null
                && afterSale.getReturnWarehouseNo() == null) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean processFinish(List<OrderAfterSaleProcessFinishReq> reqs) {
        if (CollectionUtils.isEmpty(reqs)) {
            return true;
        }
        OrderAfterSaleProcessFinishReq processFinishReq = reqs.get(0);
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(processFinishReq.getOrderAfterSaleNo());
        if (orderAfterSale == null) {
            throw new BizException("售后单不存在");
        }
        List<OrderAfterSaleProcessFinishParam> orderAfterSaleProcessFinishParams = OrderAfterSaleConverter.convertToFinishParam(reqs);
        return orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).finish(orderAfterSaleProcessFinishParams);
    }

    @Override
    public Boolean reviewSubmissions(OrderAfterSaleAuditReq auditReq) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(auditReq.getAfterSaleOrderNo());
        if (orderAfterSale == null) {
            log.error("售后单不存在, req={}", auditReq);
            throw new BizException("售后单不存在");
        }
        List<Integer> needAuditStatusList = Arrays.asList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());
        if (!needAuditStatusList.contains(orderAfterSale.getStatus())) {
            throw new BizException("状态非待审核，请确认后重新发起");
        }

        auditReq.setOrderAfterSaleId(orderAfterSale.getId());

        // 校验审核权限
        checkReviewPermission(auditReq, orderAfterSale);

        if (Objects.isNull(auditReq.getTotalPrice())) {
            throw new BizException("售后金额不能为空，请输入售后金额");
        }
        if (auditReq.getTotalPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("售后金额不能小于0");
        }
        if (auditReq.getTotalPrice().compareTo(orderAfterSale.getApplyPrice()) > 0) {
            throw new BizException("售后金额不可大于申请金额，请确认后重新发起");
        }

        // 确认本次审核是否需要服务商确认
        setNeedServiceProvider(orderAfterSale.getTenantId(),auditReq);

        OrderAfterSaleAuditCommandParam orderAfterSaleAuditCommandParam = OrderAfterSaleConverter.convertToAuditCommandParam(auditReq);

        //分账中目前在客户端判断
        if (Objects.equals(AuditFlagEnum.AUDIT_SUCCESS.getFlag(), auditReq.getAuditStatus())) {
            //检验数量
            verifyPriceAndQuantity(orderAfterSale);
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).reviewSuccess(orderAfterSaleAuditCommandParam);
        } else {
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).reviewReject(orderAfterSaleAuditCommandParam, orderAfterSale);
        }
        return true;
    }

    private void setNeedServiceProvider(Long tenantId, OrderAfterSaleAuditReq orderAfterSaleAuditReq) {
        OrderAfterSaleServerProviderAuditDTO orderAfterSaleServerProviderAuditDTO = OrderAfterSaleServerProviderAuditDTO.builder()
                .tenantId(tenantId)
                .warehouseType(orderAfterSaleAuditReq.getWarehouseType())
                .goodsType(orderAfterSaleAuditReq.getGoodsType())
                .auditStatus(orderAfterSaleAuditReq.getAuditStatus())
                .responsibilityType(Optional.ofNullable(orderAfterSaleAuditReq.getResponsibilityType()).map(Integer::valueOf).orElse(null)).build();
        boolean needServiceProviderAudit = orderAfterSaleBizService.checkNeedServiceProviderAudit(orderAfterSaleServerProviderAuditDTO);
        orderAfterSaleAuditReq.setNeedServiceProviderAudit(needServiceProviderAudit);
    }

    /**
     * 校验品牌方自己审核代仓的权限
     * @param orderAfterSale
     */
    private void checkReviewPermission(OrderAfterSaleAuditReq orderAfterSaleAuditReq, OrderAfterSaleEntity orderAfterSale) {
            // 请求来源
        SystemSourceEnum systemSourceEnum = SystemSourceEnum.getByCode(orderAfterSaleAuditReq.getSystemSource());
        OrderItemSnapshotEntity orderItemSnapshot = orderItemSnapshotQueryRepository.queryByOrderItemId(orderAfterSale.getOrderItemId());
        Integer goodsType = orderItemSnapshot.getGoodsType();
        orderAfterSaleAuditReq.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleAuditReq.setGoodsType(orderItemSnapshot.getGoodsType());
        switch (systemSourceEnum) {
            case MANAGE:
                // 校验品牌方的审核权限
                checkBrandReviewPermission(orderAfterSale, goodsType);
                break;
            case OMS:
                // 校验oms的审核权限
                checkServiceProviderReviewPermission(orderAfterSale, goodsType);
                break;
            default:
                throw new BizException("不支持的请求来源");
        }
    }

    /**
     * 品牌方审核权限校验
     * @param orderAfterSale
     * @param goodsType
     */
    private void checkBrandReviewPermission(OrderAfterSaleEntity orderAfterSale, Integer goodsType) {
        OrderEntity order = orderQueryRepository.queryById(orderAfterSale.getOrderId());

        if(!com.cosfo.ordercenter.client.common.PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ())) {
            boolean agentWarehouseFlag = Objects.equals (orderAfterSale.getWarehouseType (), WarehouseTypeEnum.THREE_PARTIES.getCode ()) && Objects.equals (goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode ());
            OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq (orderAfterSale.getTenantId ());
            boolean agentAfterSaleSelfReviewFlag = orderAfterSaleFlagFacade.needSelfReviewFlag (req);
            if (agentWarehouseFlag && !agentAfterSaleSelfReviewFlag) {
                // 代仓售后且品牌方没开启自审配置
                throw new BizException ("该售后订单您暂无审核权限，请联系代仓服务商进行审核");
            }
            boolean quotationFlag = Objects.equals (orderAfterSale.getWarehouseType (), WarehouseTypeEnum.THREE_PARTIES.getCode ()) && Objects.equals (goodsType, GoodsTypeEnum.QUOTATION_TYPE.getCode ());
            if (quotationFlag) {
                throw new BizException ("该售后订单您暂无审核权限");
            }
        }
    }

    /**
     * 服务商权限校验
     * @param orderAfterSale
     * @param goodsType
     */
    private void checkServiceProviderReviewPermission(OrderAfterSaleEntity orderAfterSale, Integer goodsType) {
        OrderEntity order = orderQueryRepository.queryById(orderAfterSale.getOrderId());
        if(!com.cosfo.ordercenter.client.common.PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ())) {
            // 非三方仓不支持审核
            if (!Objects.equals (orderAfterSale.getWarehouseType (), WarehouseTypeEnum.THREE_PARTIES.getCode ())) {
                throw new BizException ("该售后订单您暂无审核权限");
            }
            OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq (orderAfterSale.getTenantId ());
            boolean agentAfterSaleSelfReviewFlag = orderAfterSaleFlagFacade.needSelfReviewFlag (req);
            // 代仓售后且品牌方开启自审配置
            if (Objects.equals (goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode ()) && agentAfterSaleSelfReviewFlag) {
                throw new BizException ("该售后订单您暂无审核权限，品牌方开启了代仓审核权限");
            }
        }else{
            boolean isResendOrExchange = OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(orderAfterSale.getServiceType()) || OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue().equals(orderAfterSale.getServiceType());
            // 非补发、换货，且状态1-"待审核" 需要先品牌方审核
            if(!isResendOrExchange && !OrderAfterSaleStatusEnum.WAIT_CONFIRM.equals (orderAfterSale.getStatus ())){
                throw new BizException ("该售后单审核方为品牌方，请品牌方审核通过后重试");
            }
        }
    }


    private void verifyPriceAndQuantity(OrderAfterSaleEntity orderAfterSale) {
        OrderAfterSaleEnableApplyReq afterSaleEnableApplyReq = new OrderAfterSaleEnableApplyReq();
        afterSaleEnableApplyReq.setOrderId(orderAfterSale.getOrderId());
        afterSaleEnableApplyReq.setTenantId(orderAfterSale.getTenantId());
        afterSaleEnableApplyReq.setOrderItemId(orderAfterSale.getOrderItemId());
        Map<Long, OrderAfterSaleEnableDTO> enableApplyMap = RpcResultUtil.handle(orderAfterSaleQueryService.queryEnableApply(afterSaleEnableApplyReq));
        OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableApplyMap.get(orderAfterSale.getOrderItemId());
        if (orderAfterSaleEnableDTO.getEnableApplyQuantity() < 0) {
            throw new BizException("可售后件数不足");
        }
        if (orderAfterSaleEnableDTO.getEnableApplyPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("可售后金额不足,最大可售后金额为" + orderAfterSaleEnableDTO.getEnableApplyPrice() + "元");
        }
    }

    @Override
    public Boolean serviceProviderReviewSubmissions(OrderAfterSaleAuditReq auditReq) {
        // 参数校验
        checkParams(auditReq);

        // 业务对象组装
        OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO = buildOrderAfterSaleAuditBO(auditReq);

        // 状态校验
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        if (!Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())) {
            throw new BizException("售后状态非待确认");
        }

        // 金额校验
        verifyPriceAndQuantity(orderAfterSale);

        if (Objects.equals(orderAfterSaleAuditBO.getAuditStatus(), AuditFlagEnum.AUDIT_SUCCESS.getFlag())) {
            // 审核通过处理
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).serviceProviderPassSubmissions(orderAfterSaleAuditBO);
        } else {
            // 审核拒绝处理
            serviceProviderRejectSubmissions(orderAfterSaleAuditBO);
        }
        return true;
    }

    private void serviceProviderRejectSubmissions(OrderAfterSaleServiceProviderAuditParam orderAfterSaleAuditBO) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        Long id = orderAfterSale.getId();
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(id);
        update.setServiceProviderAuditTime(LocalDateTime.now());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setStatus(AUDITED_FAILED.getValue());
        update.setResponsibilityType(orderAfterSaleAuditBO.getResponsibility());
        orderAfterSaleCommandDomainService.updateById(update);
        log.info("变更售后单:{}状态为[已拒绝-5]", orderAfterSale.getAfterSaleOrderNo());
    }

    private OrderAfterSaleServiceProviderAuditParam buildOrderAfterSaleAuditBO(OrderAfterSaleAuditReq auditReq) {
        OrderAfterSaleEntity orderAfterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(auditReq.getAfterSaleOrderNo());
        return OrderAfterSaleServiceProviderAuditParam.builder()
                .auditStatus(auditReq.getAuditStatus())
                .handleRemark(auditReq.getHandleRemark())
                .totalPrice(auditReq.getTotalPrice())
                .responsibility(Optional.of(auditReq.getResponsibilityType()).map(Integer::valueOf).orElse(null))
                .orderAfterSale(orderAfterSale)
                .build();
    }

    private void checkParams(OrderAfterSaleAuditReq auditReq) {
        if (StringUtils.isEmpty(auditReq.getHandleRemark())) {
            throw new ParamsException("审核说明不能为空");
        }
        if (Objects.isNull(auditReq.getTotalPrice())) {
            throw new ParamsException("售后金额不能为空");
        }
        if (auditReq.getTotalPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ParamsException("售后金额不能小于0");
        }
        if (Objects.isNull(auditReq.getAuditStatus())) {
            throw new ParamsException("审核状态不能为空");
        }
    }

    @Override
    public Boolean autoFinished() {
        return orderAfterSaleCommandDomainService.autoFinish();
    }

    @Override
    public Boolean updateAfterSaleStoreNo(OrderAfterSaleUpdateStoreNoReq req) {
        OrderAfterSaleEntity afterSale = orderAfterSaleQueryRepository.queryByAfterSaleNo(req.getOrderAfterSaleNo());
        if(afterSale == null){
            throw new BizException("售后单不存在");
        }

        return orderAfterSaleCommandDomainService.updateStoreNo(afterSale.getId(), req.getSourceStoreNo(), req.getStoreNo());
    }

    @Override
    public List<Long> batchCreateAfterSaleForOpen(OrderAfterSaleBatchReq orderAfterSaleBatchReq) {
        List<Long> orderIdList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(dto -> dto.getOrderId()).distinct().collect(Collectors.toList());
        List<String> keys = Lists.newArrayList();
        List<Long> afterSaleIdList;
        try {
            long count = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleDTO::getTenantId).distinct().count();
            if (count > NumberConstant.ONE) {
                throw new BizException("仅支持单租户批量售后", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            }
            Long tenantId = orderAfterSaleBatchReq.getApplyAfterSaleList().get(NumberConstant.ZERO).getTenantId();

            // 订单加锁
            lockOrder(keys, orderIdList);
            // 幂等校验
            checkCustomerAfterSaleOrderNo(orderAfterSaleBatchReq, tenantId);
            // 订单校验
            Map<Long, List<OrderAfterSaleEntity>> orderAfterSaleMap = validOrderAndTransferParam(orderAfterSaleBatchReq, orderIdList);
            try {
                // 批量创建售后单
                afterSaleIdList = transactionTemplate.execute(status -> {
                    List<Long> afterSaleIds = Lists.newArrayList();
                    for (Map.Entry<Long, List<OrderAfterSaleEntity>> orderEntry : orderAfterSaleMap.entrySet()) {
                        List<OrderAfterSaleEntity> orderAfterSaleDTOS = orderEntry.getValue();
                        // 退款录入账单、退货退款录入账单支持
                        Integer serviceType = orderAfterSaleDTOS.get(0).getServiceType();
                        List<Long> orderAfterSaleIds = orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(orderAfterSaleDTOS);
                        afterSaleIds.addAll(orderAfterSaleIds);
                    }
                    return afterSaleIds;
                });
                if (CollectionUtils.isEmpty(afterSaleIdList)) {
                    throw new BizException("创建售后单失败");
                }
            } catch (Exception e) {
                log.error("创建售后单失败,orderAfterSaleMap:{}", JSON.toJSONString(orderAfterSaleMap), e);
                throw new BizException("创建售后单失败");
            }
        } finally {
            unlockOrder(keys);
        }
        return afterSaleIdList;
    }

    @Override
    public Boolean modifyQuantity(OrderAfterSaleModifyQuantityReq modifyQuantityReq) {

        OrderAfterSaleModifyQuantityParam modifyQuantityParam = OrderAfterSaleConverter.convertToParam(modifyQuantityReq);

        return orderAfterSaleCommandDomainService.modifyQuantity(modifyQuantityParam);
    }

    @Override
    public Boolean recycleFailRefund(OrderAfterSaleRecycleFailRefundReq recycleFailRefundReq) {
        OrderAfterSaleRecycleFailRefundParam recycleFailRefundParam = OrderAfterSaleConverter.convertToParam(recycleFailRefundReq);
        return orderAfterSaleCommandDomainService.recycleFailRefund(recycleFailRefundParam);
    }

    /**
     * 外部订单号加锁
     *
     * @param keys
     * @param orderIdList
     */
    private void lockOrder(List<String> keys, List<Long> orderIdList) {
        for (Long orderId : orderIdList) {
            String redisKey = RedisKeyEnum.OC00001.join(orderId);
            RLock lock = redissonClient.getLock(redisKey);
            // 未获取到锁，退出
            if (!lock.tryLock()) {
                throw new BizException("部分订单正在发起售后，请稍后重试", OpenApiErrorCode.CREATE_AFTER_CONCURRENCE_VALID_CODE);
            }
            keys.add(redisKey);
        }
    }

    /**
     * 外部订单号解锁
     *
     * @param keys
     */
    private void unlockOrder(List<String> keys) {
        for (String key : keys) {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 请求中存在已处理的非失败单号，强校验拦截
     *
     * @param orderAfterSaleBatchReq
     * @param tenantId
     */
    private void checkCustomerAfterSaleOrderNo(OrderAfterSaleBatchReq orderAfterSaleBatchReq, Long tenantId) {
        List<String> customerAfterSaleOrderNoList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleDTO::getCustomerAfterSaleOrderNo).collect(Collectors.toList());
        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setCustomerAfterSaleOrderNos(customerAfterSaleOrderNoList);
        List<OrderAfterSaleEntity> orderAfterSales = orderAfterSaleQueryRepository.queryListByParam(orderAfterSaleQueryParam);
        // 若售后单处于常规状态，拦截
        Optional<OrderAfterSaleEntity> normalOrderAfterSale = orderAfterSales.stream().filter(orderAfterSale -> !AFTER_SALE_FAIL_STATUS.contains(orderAfterSale.getStatus())).findFirst();
        if (!CollectionUtils.isEmpty(orderAfterSales) && normalOrderAfterSale.isPresent()) {
            List<String> existList = orderAfterSales.stream().map(OrderAfterSaleEntity::getCustomerAfterSaleOrderNo).collect(Collectors.toList());
            log.info("存在外部系统单号已处理,orderAfterSaleQueryParam:{},tenantId:{},existList:{}", JSON.toJSONString(orderAfterSaleQueryParam), tenantId, JSON.toJSONString(existList));
            throw new BizException("存在外部系统单号已处理，请排除相关单号" + normalOrderAfterSale.get().getCustomerAfterSaleOrderNo(), OpenApiErrorCode.AFTER_ORDER_NO_NO_EXIST_CODE);
        }
    }

    private Map<Long, List<OrderAfterSaleEntity>> validOrderAndTransferParam(OrderAfterSaleBatchReq orderAfterSaleBatchReq, List<Long> orderIdList) {
        List<OrderEntity> orders = orderQueryRepository.queryByIds(orderIdList);
        Map<Long, OrderEntity> orderMap = orders.stream().collect(Collectors.toMap(OrderEntity::getId, Function.identity(), (v1, v2) -> v1));

        // 校验当前订单状态是否支持配送后售后
        Optional<OrderEntity> orderOptional = orders.stream().filter(order -> !OrderStatusEnum.ableApplyDeliveredAfterSale(order.getStatus())).findFirst();
        if (orderOptional.isPresent()) {
            log.info("订单当前状态不支持发起配送后售后,订单号={}", orderOptional.get().getCustomerOrderId());
            throw new BizException("订单当前状态不支持发起配送后售后,订单号：" + orderOptional.get().getCustomerOrderId(), OpenApiErrorCode.ORDER_STATUS_UN_SUPPORT_CODE);
        }

        Map<Long, List<OrderAfterSaleEntity>> orderAfterSaleMap = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleConverter::convertToEntity).collect(Collectors.groupingBy(OrderAfterSaleEntity::getOrderId));

        for (Map.Entry<Long, List<OrderAfterSaleEntity>> orderEntry : orderAfterSaleMap.entrySet()) {
            Long orderId = orderEntry.getKey();
            OrderEntity order = orderMap.get(orderId);
            List<OrderAfterSaleEntity> orderAfterSaleDTOS = orderEntry.getValue();
            if (Objects.isNull(order)) {
                log.info("售后单对应订单不存在, orderAfterSaleDTOS={},orderAfterSaleBatchReq={}", JSON.toJSON(orderAfterSaleDTOS), JSON.toJSONString(orderAfterSaleBatchReq));
                throw new BizException("售后单对应订单不存在,售后单：" + StringUtils.join(orderAfterSaleDTOS.stream()
                        .map(OrderAfterSaleEntity::getCustomerAfterSaleOrderNo).collect(Collectors.toList()), ','), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            }

            orderAfterSaleBizService.checkDataAndBuildDto(orderAfterSaleDTOS, order);

//            orderAfterSaleDTOS.forEach(orderAfterSaleDTO -> {
//                LocalDateTime autoFinishedTme = orderAfterSaleBizService.orderAfterSaleDataCheck(orderAfterSaleDTO, order);
//                orderAfterSaleDTO.setAutoFinishedTime(autoFinishedTme);
//            });

        }
        return orderAfterSaleMap;
    }
}
