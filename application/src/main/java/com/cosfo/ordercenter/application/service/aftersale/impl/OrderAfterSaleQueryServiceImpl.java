package com.cosfo.ordercenter.application.service.aftersale.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleCalRefundPriceReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleOutQueryDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.aftersale.*;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSalePageQueryParam;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.facade.ProductsMappingQueryFacade;
import com.cosfo.ordercenter.facade.dto.ProductsMappingDTO;
import com.cosfo.ordercenter.facade.input.ProductMappingQueryInput;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleQueryServiceImpl implements OrderAfterSaleQueryService {

    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;
    @Resource
    private OrderQueryRepository orderQueryRepository;

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private ProductsMappingQueryFacade productsMappingQueryFacade;


    @Override
    public List<OrderAfterSaleOutResp> queryOrderAfterSaleInfo(OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO) {
        Long tenantId = orderAfterSaleOutQueryDTO.getTenantId();
        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setAfterSaleOrderNos(orderAfterSaleOutQueryDTO.getAfterSaleOrderNos());
        // 查询售后单信息
        List<OrderAfterSaleEntity> orderAfterSales = orderAfterSaleQueryRepository.queryListByCondition(orderAfterSaleQueryParam);
        if (CollectionUtils.isEmpty(orderAfterSales)) {
            return Collections.emptyList();
        }

        // 订单项
        List<Long> orderItemIds = orderAfterSales.stream().map(OrderAfterSaleEntity::getOrderItemId).collect(Collectors.toList());
        List<OrderItemEntity> orderItems = orderItemQueryRepository.batchQuery(tenantId, orderItemIds);
        Map<Long, OrderItemEntity> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItemEntity::getId, item -> item));
        Map<Long, OrderItemSnapshotEntity> itemSnapshotMap = orderItemSnapshotQueryRepository.queryOrderItemSnapshot(orderItemIds);
        List<OrderAfterSaleOutResp> orderAfterSaleOutDTOS = orderAfterSales.stream().map(orderAfterSale -> {
            OrderItemEntity orderItem = orderItemMap.get(orderAfterSale.getOrderItemId());
            OrderItemSnapshotEntity orderItemSnapshot = itemSnapshotMap.get(orderAfterSale.getOrderItemId());
            return OrderAfterSaleConverter.buildOrderAfterSaleOutDTO(orderAfterSale, orderItem, orderItemSnapshot);
        }).collect(Collectors.toList());
        return orderAfterSaleOutDTOS;
    }

    @Override
    public PageInfo<OrderAfterSaleWithOrderResp> queryPage(OrderAfterSalePageQueryReq req) {
        OrderAfterSalePageQueryParam orderAfterSalePageQueryParam = OrderAfterSaleConverter.convertToPageQueryParam(req);
        PageInfo<OrderAfterSaleEntity> orderAfterSalePage = orderAfterSaleQueryRepository.queryPage(orderAfterSalePageQueryParam);
        if (orderAfterSalePage == null || CollectionUtils.isEmpty(orderAfterSalePage.getList())) {
            return PageInfo.emptyPageInfo();
        }
        List<Long> orderItemIds = orderAfterSalePage.getList().stream().map(OrderAfterSaleEntity::getOrderItemId).collect(Collectors.toList());
        Map<Long, OrderItemEntity> orderItemMap = getOrderItemMap(orderItemIds);
        Map<Long, OrderItemSnapshotEntity> snapshotMap = getSnapshotMap(orderItemIds);
        Map<Long, OrderEntity> orderMap = getOrderMap(orderItemIds);
        List<OrderAfterSaleWithOrderResp> afterSaleWithOrderDTOList = orderAfterSalePage.getList().stream()
                .map(orderAfterSale -> OrderAfterSaleConverter.handleOrderAfterSaleDetailInfo(orderAfterSale, orderItemMap, snapshotMap, orderMap)).collect(Collectors.toList());
        PageInfo<OrderAfterSaleWithOrderResp> result = new PageInfo<>();
        result.setList(afterSaleWithOrderDTOList);
        result.setPageNum(orderAfterSalePage.getPageNum());
        result.setPageSize(orderAfterSalePage.getPageSize());
        result.setTotal(orderAfterSalePage.getTotal());
        return result;
    }

    @Override
    public List<OrderAfterSaleResp> queryByNos(List<String> orderAfterSaleNos) {
        List<OrderAfterSaleEntity> orderAfterSaleEntities = orderAfterSaleQueryRepository.queryByNos(orderAfterSaleNos);
        List<OrderAfterSaleResp> orderAfterSaleResps = OrderAfterSaleConverter.convertToRespList(orderAfterSaleEntities);
        fillOrderNo(orderAfterSaleResps);
        return orderAfterSaleResps;
    }

    @Override
    public List<OrderAfterSaleResp> queryByIds(List<Long> orderAfterSaleIds) {
        List<OrderAfterSaleEntity> orderAfterSaleEntities = orderAfterSaleQueryRepository.queryByIds(orderAfterSaleIds);
        List<OrderAfterSaleResp> orderAfterSaleResps = OrderAfterSaleConverter.convertToRespList(orderAfterSaleEntities);
        fillOrderNo(orderAfterSaleResps);
        return orderAfterSaleResps;
    }

    @Override
    public BigDecimal calculateRefundPrice(OrderAfterSaleCalRefundPriceReq req) {
        Integer quantity = req.getQuantity();
        OrderItemEntity orderItem = orderItemQueryRepository.queryById(req.getOrderItemId());
        OrderItemSnapshotEntity orderItemSnapshot = orderItemSnapshotQueryRepository.queryByOrderItemId(orderItem.getId());

        BigDecimal applyPrice = NumberUtil.mul(quantity, orderItem.getTotalPrice());
        Integer maxApplyAmount = orderItem.getAmount() * orderItemSnapshot.getMaxAfterSaleAmount();
        BigDecimal refundPrice = NumberUtil.div(applyPrice, maxApplyAmount, 2);
        OrderAfterSaleEnableApplyParam enableApplyReq = new OrderAfterSaleEnableApplyParam();
        enableApplyReq.setTenantId(orderItem.getTenantId());
        enableApplyReq.setOrderItemIds(Lists.newArrayList(orderItem.getId()));
        enableApplyReq.setOrderId(orderItem.getOrderId());

        Map<Long, OrderAfterSaleEnableResp> enableApplyMap = orderAfterSaleBizService.queryEnableApplyByApplyParam(enableApplyReq);
        OrderAfterSaleEnableResp orderAfterSaleEnableDTO = enableApplyMap.get(req.getOrderItemId());
        BigDecimal enableApplyPrice = orderAfterSaleEnableDTO.getEnableApplyPrice();
        return NumberUtil.min(enableApplyPrice, refundPrice);
    }

    @Override
    public List<QueryResentOrderAfterSaleResp> queryResentOrderAfterSaleForTms(QueryResentOrderAfterSaleReq req) {
        List<OrderEntity> orderList = orderQueryRepository.queryByStoreIdAndDeliveryTime(req.getStoreId(), req.getDeliveryDate().atStartOfDay(), WarehouseTypeEnum.THREE_PARTIES.getCode());
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        ProductMappingQueryInput queryReq = new ProductMappingQueryInput();
        queryReq.setSkuList(Collections.singletonList(req.getSku()));
        List<ProductsMappingDTO> handle = productsMappingQueryFacade.selectMappingList(queryReq);
        ProductsMappingDTO productsMappingResp = handle.stream().max(Comparator.comparing(ProductsMappingDTO::getId)).orElse(new ProductsMappingDTO());
        Long agentSkuId = productsMappingResp.getAgentSkuId();

        if (agentSkuId == null) {
            log.warn("skucode={}, 未找到货品记录", req.getSku());
            return Collections.emptyList();
        }

        List<Long> orderIds = orderList.stream().map(OrderEntity::getId).collect(Collectors.toList());

        List<OrderItemSnapshotEntity> orderItemSnapshots = orderItemSnapshotQueryRepository.queryByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderItemSnapshots)) {
            return Collections.emptyList();
        }

        List<Long> orderItemIdList = orderItemSnapshots.stream().filter(e -> agentSkuId.equals(e.getSupplierSkuId())).map(OrderItemSnapshotEntity::getOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIdList)) {
            return Collections.emptyList();
        }

        OrderAfterSaleQueryParam param = new OrderAfterSaleQueryParam();
        param.setOrderItemIds(orderItemIdList);
        List<OrderAfterSaleEntity> orderAfterSaleList = orderAfterSaleQueryRepository.queryListByCondition(param);
        if (CollectionUtils.isEmpty(orderAfterSaleList)) {
            return Collections.emptyList();
        }

        LocalDateTime createStartTime = req.getCreateDate().atStartOfDay();
        LocalDateTime createEndTime = req.getCreateDate().plusDays(1).atStartOfDay();

        List<QueryResentOrderAfterSaleResp> resultList = orderAfterSaleList.stream()
                .filter(e -> (OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(e.getServiceType())
                        && e.getCreateTime().isAfter(createStartTime)
                        && e.getCreateTime().isBefore(createEndTime)))
                .map(e -> {
                    QueryResentOrderAfterSaleResp dto = new QueryResentOrderAfterSaleResp();
                    dto.setAfterSaleOrderNo(e.getAfterSaleOrderNo());
                    dto.setCreateTime(e.getCreateTime());
                    dto.setCreateUser(StringUtils.isBlank(e.getOperatorName()) ? "系统" : e.getOperatorName());
                    return dto;
                })
                .collect(Collectors.toList());
        return resultList;
    }

    private Map<Long, OrderItemEntity> getOrderItemMap(List<Long> orderItemIds) {
        List<OrderItemEntity> orderItems = orderItemQueryRepository.batchQuery(null, orderItemIds);
        Map<Long, OrderItemEntity> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItemEntity::getId, o -> o));
        return orderItemMap;
    }

    private Map<Long, OrderItemSnapshotEntity> getSnapshotMap(List<Long> orderItemIds) {
        List<OrderItemSnapshotEntity> orderItemSnapshots = orderItemSnapshotQueryRepository.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshotEntity> snapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshotEntity::getOrderItemId, o -> o));
        return snapshotMap;
    }

    private Map<Long, OrderEntity> getOrderMap(List<Long> orderItemIds) {
        List<OrderEntity> orders = orderQueryRepository.queryList(OrderQueryParam.builder().orderItemIds(orderItemIds).build());
        Map<Long, OrderEntity> orderMap = orders.stream().collect(Collectors.toMap(OrderEntity::getId, order -> order));
        return orderMap;
    }

    private void fillOrderNo(List<OrderAfterSaleResp> orderAfterSaleDTOS) {
        if(CollectionUtil.isEmpty (orderAfterSaleDTOS)){
            return;
        }
        List<Long> orderItemIds = orderAfterSaleDTOS.stream().map(OrderAfterSaleResp::getOrderItemId).collect(Collectors.toList());
        Map<Long, OrderItemEntity> orderItemMap = getOrderItemMap(orderItemIds);
        Map<Long, OrderEntity> orderMap = getOrderMap(orderItemIds);
        for(OrderAfterSaleResp dto : orderAfterSaleDTOS){
            OrderItemEntity orderItem = orderItemMap.get(dto.getOrderItemId());
            OrderEntity order = orderMap.get(orderItem.getOrderId());
            dto.setOrderNo (order.getOrderNo ());
        }
    }
}
