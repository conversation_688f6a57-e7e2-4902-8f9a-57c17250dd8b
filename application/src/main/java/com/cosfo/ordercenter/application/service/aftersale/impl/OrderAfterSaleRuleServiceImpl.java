package com.cosfo.ordercenter.application.service.aftersale.impl;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleRuleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleRuleService;
import com.cosfo.ordercenter.client.req.OrderAfterSaleRuleCommandReq;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleRuleCommandRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class OrderAfterSaleRuleServiceImpl implements OrderAfterSaleRuleService {

    @Resource
    private OrderAfterSaleRuleCommandRepository orderAfterSaleRuleCommandRepository;

    @Override
    public Long add(OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO) {
        OrderAfterSaleRuleCommandParam orderAfterSaleRuleCommandParam = OrderAfterSaleRuleConverter.convertToCommandParam(orderAfterSaleRuleDTO);
        return orderAfterSaleRuleCommandRepository.addOrUpdate(orderAfterSaleRuleCommandParam);
    }

    @Override
    public Boolean updateRule(OrderAfterSaleRuleCommandReq orderAfterSaleRuleDTO) {
        OrderAfterSaleRuleCommandParam orderAfterSaleRuleCommandParam = OrderAfterSaleRuleConverter.convertToCommandParam(orderAfterSaleRuleDTO);

        return orderAfterSaleRuleCommandRepository.updateRule(orderAfterSaleRuleCommandParam) > 0;
    }

    @Override
    public Boolean deleteRule(Long id) {
        return orderAfterSaleRuleCommandRepository.deleteRule(id) > 0;
    }
}
