package com.cosfo.ordercenter.application.service.delivery;

import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;


import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/11 10:15
 * @Description:
 */
public interface RefundDeliveryFeeService {

    /**
     * 计算退款运费
     *
     * @param orderAfterSaleInputs 售后信息
     * @param orderDTO             订单信息
     * @return
     */
    MerchantDeliveryFeeSnapshotDTO queryRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, Order orderDTO);

    /**
     * 普通计算方式 - 计算应退运费
     * @param orderAfterSaleInputs
     * @param deliveryFee
     * @return
     */
    BigDecimal calculateNormalDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, BigDecimal deliveryFee);
}
