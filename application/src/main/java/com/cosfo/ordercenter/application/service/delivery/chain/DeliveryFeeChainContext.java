package com.cosfo.ordercenter.application.service.delivery.chain;

import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import lombok.Data;

/**
 * 运费计算上下文参数
 *
 * @author: xiaowk
 * @time: 2025/3/3 上午11:39
 */
@Data
public class DeliveryFeeChainContext {

    /**
     * 运费计算请求参数
     */
    private DeliveryTotalReq deliveryTotalReq;

    /**
     * 订单的仓库类型
     */
    private Integer warehouseType;

    private WarehouseTypeEnum warehouseTypeEnum;

}
