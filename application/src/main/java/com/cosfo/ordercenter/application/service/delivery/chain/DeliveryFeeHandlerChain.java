package com.cosfo.ordercenter.application.service.delivery.chain;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.MerchantDeliveryFeeSnapshotConverter;
import com.cosfo.ordercenter.application.service.delivery.chain.handler.AbstractDeliveryFeeHandler;
import com.cosfo.ordercenter.application.service.delivery.chain.handler.GlobalDeliveryFeeHandler;
import com.cosfo.ordercenter.application.service.delivery.chain.handler.WarehouseDeliveryFeeHandler;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import com.cosfo.ordercenter.client.validgroup.QueryRuleGroup;
import com.cosfo.ordercenter.common.util.ValidatorUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 运费计算责任链配置
 *
 * @author: xiaowk
 * @time: 2025/3/3 上午11:31
 */
@Component
@Slf4j
public class DeliveryFeeHandlerChain implements ApplicationContextAware {

    // 定义责任链处理器顺序
    private final List<Class<? extends AbstractDeliveryFeeHandler>> handlerOrderList = Lists.newArrayList(
            GlobalDeliveryFeeHandler.class,
            WarehouseDeliveryFeeHandler.class
    );

    private ApplicationContext applicationContext;
    /**
     * 第一个处理器
     */
    private AbstractDeliveryFeeHandler head;
    private AbstractDeliveryFeeHandler tail;


    @PostConstruct
    public void init() {
        // 根据给定的顺序列表获取处理者实例
        for (Class<? extends AbstractDeliveryFeeHandler> handlerClass : handlerOrderList) {
            AbstractDeliveryFeeHandler handler = applicationContext.getBean(handlerClass);
            addHandler(handler);
        }
    }

    private void addHandler(AbstractDeliveryFeeHandler handler) {
        if (handler == null) {
            return;
        }
        handler.setNextHandler(null);

        if (head == null) {
            head = handler;
            tail = handler;
            return;
        }

        tail.setNextHandler(handler);
        tail = handler;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 责任链开始处理方法, 计算运费
     * @param deliveryTotalReq
     * @return
     */
    public MerchantDeliveryFeeSnapshotResp executeChain(DeliveryTotalReq deliveryTotalReq) {
        if (head == null) {
            throw new BizException("运费计算处理器为空");
        }

        Integer warehouseType = deliveryTotalReq.getOrderInfoDTO().getWarehouseType();
        WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(warehouseType);
        if (Objects.isNull(warehouseTypeEnum)) {
            throw new ParamsException("请输入正确的仓库类型！");
        }
        deliveryTotalReq.getOrderInfoDTO().setWarehouseTypeEnum(warehouseTypeEnum);

        log.info("{},开始计算运费：{}", warehouseTypeEnum.getDesc(), JSON.toJSONString(deliveryTotalReq));

        // 规则中保存的省份没有"省""市"字样
        String storeProvince = deliveryTotalReq.getOrderInfoDTO().getStoreProvince();
        storeProvince = storeProvince.replaceAll("(?<!市)省$|(?<!市)市$", "");
        deliveryTotalReq.getOrderInfoDTO().setStoreProvince(storeProvince);

        DeliveryFeeChainContext chainContext = new DeliveryFeeChainContext();
        chainContext.setDeliveryTotalReq(deliveryTotalReq);
        chainContext.setWarehouseType(warehouseType);
        chainContext.setWarehouseTypeEnum(warehouseTypeEnum);

        MerchantDeliveryFeeSnapshotDTO merchantDeliveryFeeSnapshotDTO = head.calculateMerchantDeliveryFee(chainContext);

        log.info("{},计算运费完成。打印快照：{}", warehouseTypeEnum.getDesc(), JSON.toJSONString(merchantDeliveryFeeSnapshotDTO));

        return MerchantDeliveryFeeSnapshotConverter.convertToSnapshotResp(merchantDeliveryFeeSnapshotDTO);
    }
}
