package com.cosfo.ordercenter.application.service.delivery.chain.handler;

import com.cosfo.ordercenter.application.service.delivery.chain.DeliveryFeeChainContext;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;

/**
 * 责任链抽象处理器
 * @author: xiaowk
 * @time: 2025/3/3 上午11:32
 */
public abstract class AbstractDeliveryFeeHandler {

    /**
     * 下一个处理器
     */
    protected AbstractDeliveryFeeHandler nextHandler;


    public void setNextHandler(AbstractDeliveryFeeHandler nextHandler) {
        this.nextHandler = nextHandler;
    }


    public abstract MerchantDeliveryFeeSnapshotDTO calculateMerchantDeliveryFee(DeliveryFeeChainContext chainContext);

    /**
     * 执行责任链下一个处理器
     * @param chainContext
     * @return
     */
    protected MerchantDeliveryFeeSnapshotDTO executeNextHandler(DeliveryFeeChainContext chainContext){
        if(nextHandler != null){
            return nextHandler.calculateMerchantDeliveryFee(chainContext);
        }
        return null;
    }

}
