package com.cosfo.ordercenter.application.service.delivery.chain.handler;

import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.MerchantDeliveryRuleConverter;
import com.cosfo.ordercenter.application.service.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.application.service.delivery.chain.DeliveryFeeChainContext;
import com.cosfo.ordercenter.application.service.delivery.executor.MerchantDeliveryRuleTypeContext;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 全局包邮运费配置计算运费
 *
 * @author: xiaowk
 * @time: 2025/3/3 上午11:47
 */
@Component
public class GlobalDeliveryFeeHandler extends AbstractDeliveryFeeHandler {

    @Resource
    private DeliveryFeeService deliveryFeeService;
    @Resource
    private MerchantDeliveryRuleTypeContext merchantDeliveryRuleTypeContext;

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateMerchantDeliveryFee(DeliveryFeeChainContext chainContext) {
        DeliveryTotalReq deliveryTotalReq = chainContext.getDeliveryTotalReq();

        // 查询全局包邮例外规则
        List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS = deliveryFeeService.queryAllDeliveryRule(MerchantDeliveryRuleQueryReq.builder()
                .tenantId(deliveryTotalReq.getTenantId())
                .warehouseType(10)
                .defaultType(0)
                .build());

        if (!CollectionUtils.isEmpty(deliveryRuleInfoDTOS)) {
            MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO = merchantDeliveryRuleTypeContext
                    .load(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.GLOBAL_FREE.getCode())
                    .calculateRuleTypeDeliveryFee(MerchantDeliveryRuleConverter.converterToDTO(deliveryTotalReq), deliveryRuleInfoDTOS);

            // 满足全局包邮规则，直接返回运费
            if (deliveryFeeSnapshotDTO != null) {
                return deliveryFeeSnapshotDTO;
            }
        }

        // 未配置全局包邮或不满足全局包邮规则
        return executeNextHandler(chainContext);
    }


}
