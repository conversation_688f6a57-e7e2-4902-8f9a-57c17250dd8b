package com.cosfo.ordercenter.application.service.delivery.chain.handler;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.MerchantDeliveryRuleConverter;
import com.cosfo.ordercenter.application.service.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.application.service.delivery.chain.DeliveryFeeChainContext;
import com.cosfo.ordercenter.application.service.delivery.executor.MerchantDeliveryWarehouseContext;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 按照仓运费配置计算运费（无仓、自营仓、三方仓）
 *
 * @author: xiaowk
 * @time: 2025/3/3 上午11:47
 */
@Component
@Slf4j
public class WarehouseDeliveryFeeHandler extends AbstractDeliveryFeeHandler {

    @Resource
    private MerchantDeliveryWarehouseContext merchantDeliveryWarehouseContext;
    @Resource
    private DeliveryFeeService deliveryFeeService;

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateMerchantDeliveryFee(DeliveryFeeChainContext chainContext) {

        DeliveryTotalReq deliveryTotalReq = chainContext.getDeliveryTotalReq();

        // 查询所有规则
        List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS = deliveryFeeService.queryAllDeliveryRule(MerchantDeliveryRuleQueryReq.builder()
                .tenantId(deliveryTotalReq.getTenantId())
                .warehouseType(chainContext.getWarehouseType())
                .warehouseNo(deliveryTotalReq.getOrderInfoDTO().getWarehouseNo())
                .build());
        if (CollectionUtils.isEmpty(deliveryRuleInfoDTOS)) {
            throw new BizException("未配置运费！");
        }

        log.info("{},开始计算运费-按仓库。所有规则(含未命中)：{}", chainContext.getWarehouseTypeEnum(), JSON.toJSONString(deliveryRuleInfoDTOS));

        // 按仓库，分别计算运费
        MerchantDeliveryFeeSnapshotDTO merchantDeliveryFeeSnapshotDTO = merchantDeliveryWarehouseContext.load(chainContext.getWarehouseType())
                .calculateDeliveryFee(MerchantDeliveryRuleConverter.converterToDTO(chainContext.getDeliveryTotalReq()), deliveryRuleInfoDTOS);
        return merchantDeliveryFeeSnapshotDTO;
    }
}
