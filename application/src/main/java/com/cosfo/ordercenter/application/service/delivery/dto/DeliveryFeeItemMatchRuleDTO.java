package com.cosfo.ordercenter.application.service.delivery.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/11/9 下午12:33
 */
@Data
public class DeliveryFeeItemMatchRuleDTO {

    /**
     * 运费规则id
     */
    private Long ruleId;

    /**
     * 匹配运费规则的商品
     */
    private List<Long> itemIds;

    // 匹配运费规则的商品 总件数
    private Integer totalCount;

    // 匹配运费规则的商品 总金额
    private BigDecimal totalPrice;

    /**
     * 匹配运费规则的商品 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 运费金额
     */
    private BigDecimal deliveryFee;
}
