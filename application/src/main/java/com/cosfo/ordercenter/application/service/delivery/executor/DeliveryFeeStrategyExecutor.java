package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.application.service.delivery.dto.DeliveryFeeResultDTO;
import com.cosfo.ordercenter.application.service.delivery.dto.OrderDeliveryDTO;
import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;


/**
 * @author: monna.chen
 * @Date: 2023/8/22 18:20
 * @Description:
 */
public interface DeliveryFeeStrategyExecutor {

    TenantDeliveryEnum.TypeEnum tenantStrategy();

    /**
     * 计算运费
     *
     * @param orderDTO
     * @param tenantDeliveryFeeRule
     * @return
     */
    DeliveryFeeResultDTO calculateDeliveryFee(OrderDeliveryDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule);

}
