package com.cosfo.ordercenter.application.service.delivery.executor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.ordercenter.application.service.delivery.dto.DeliveryFeeResultDTO;
import com.cosfo.ordercenter.application.service.delivery.dto.OrderDeliveryDTO;
import com.cosfo.ordercenter.application.service.delivery.dto.OrderItemDeliveryDTO;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryItemFeeDTO;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.client.validgroup.FollowTypeGroup;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.common.constants.NumberConstant;
import com.cosfo.ordercenter.common.util.ValidatorUtils;
import com.cosfo.ordercenter.domain.order.param.query.OrderQueryParam;
import com.cosfo.ordercenter.facade.ProductQueryFacade;
import com.cosfo.ordercenter.facade.dto.AllCategoryDTO;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.infrastructure.dao.TenantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 11:07
 * @Description:
 */
@Service
@Slf4j
public class FollowWarehouseExecutor implements MerchantDeliveryRuleTypeExecutor {
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private TenantDeliveryFeeRuleDao tenantDeliveryFeeRuleDao;
    @Resource
    private DeliveryFeeStrategyContext deliveryFeeStrategyContext;
    @Resource
    private ProductQueryFacade productQueryFacade;

    @Override
    public List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType() {
        return Collections.singletonList(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        ValidatorUtils.validateEntity(deliveryDTO, FollowTypeGroup.class);

        return calculateThreeFollowDeliveryFee(deliveryDTO, ruleList.get(0));
    }


    /**
     * 随仓报价计费方式
     *
     * @param deliveryTotalDTO
     * @param ruleInfo
     * @return
     */
    private MerchantDeliveryFeeSnapshotDTO calculateThreeFollowDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, MerchantDeliveryRuleInfoDTO ruleInfo) {
        ValidatorUtils.validateEntity(deliveryTotalDTO, FollowTypeGroup.class);
        OrderDeliveryDTO orderDTO = new OrderDeliveryDTO();
        // 订单信息参数
        orderDTO.setCity(deliveryTotalDTO.getOrderInfoDTO().getStoreCity());
        orderDTO.setArea(deliveryTotalDTO.getOrderInfoDTO().getStoreArea());
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setProvince(deliveryTotalDTO.getOrderInfoDTO().getStoreProvince());
        orderAddress.setCity(deliveryTotalDTO.getOrderInfoDTO().getStoreCity());
        orderAddress.setArea(deliveryTotalDTO.getOrderInfoDTO().getStoreArea());
        orderDTO.setOrderAddress(orderAddress);
        orderDTO.setWarehouseType(deliveryTotalDTO.getOrderInfoDTO().getWarehouseType());
        Optional.ofNullable(deliveryTotalDTO.getOrderInfoDTO().getWarehouseNo()).ifPresent(w -> {
            orderDTO.setWarehouseNo(w.toString());
        });
        List<OrderItemDeliveryDTO> orderItemDTOList = deliveryTotalDTO.getOrderItemInfoDTOList().stream().map(item -> {
            OrderItemDeliveryDTO orderItemDTO = new OrderItemDeliveryDTO();
            orderItemDTO.setSkuId(item.getSkuId());
            orderItemDTO.setSupplySkuId(item.getSupplierSkuId());
            orderItemDTO.setCalcPartDeliveryFee(item.getItemTotalPrice());
            orderItemDTO.setAmount(item.getItemCount());
            orderItemDTO.setGoodsType(item.getGoodsType());
            return orderItemDTO;
        }).collect(Collectors.toList());
        orderDTO.setOrderItemDTOList(orderItemDTOList);
        orderDTO.setTenantId(deliveryTotalDTO.getTenantId());

        // 查询对于品牌方运费定价方式
        TenantDeliveryFeeRule tenantDeliveryFeeRule = tenantDeliveryFeeRuleDao.selectByTenantId(deliveryTotalDTO.getTenantId());
        if (Objects.isNull(tenantDeliveryFeeRule)) {
            throw new BizException("该租户未设置品牌方运费，不可使用基于仓运费报价");
        }

        // boss后台配置运费规则 【随鲜沐商城规则】 判断是否有全鲜沐品订单
        if (TenantDeliveryEnum.TypeEnum.FOLLOW_SUPPLIER.getType().equals(tenantDeliveryFeeRule.getType())) {
            // 全鲜沐品订单
            if (checkSampleDayOrderOnlyXianmuItem(deliveryTotalDTO)) {
                // 当日第二笔订单免费
                log.info("{}计算运费完成,{},最终运费:0",
                        deliveryTotalDTO.getOrderInfoDTO().getWarehouseTypeEnum().getDesc(),
                        DeliveryConstant.DELIVERY_FEE_DAILY);
                return MerchantDeliveryFeeSnapshotDTO.builder()
                        .tenantId(deliveryTotalDTO.getTenantId())
                        .deliveryFee(BigDecimal.ZERO)
                        .orderInfo(deliveryTotalDTO)
                        .ruleList(Collections.singletonList(ruleInfo))
                        .remark(DeliveryConstant.DELIVERY_FEE_DAILY)
                        .build();
            }

        } else {
            // boss后台配置运费规则【免运费】【自定义】
            if (checkSampleDayOrder(deliveryTotalDTO)) {
                // 当日第二笔订单免费
                log.info("{}计算运费完成,{},最终运费:0",
                        deliveryTotalDTO.getOrderInfoDTO().getWarehouseTypeEnum().getDesc(),
                        DeliveryConstant.DELIVERY_FEE_DAILY);
                return MerchantDeliveryFeeSnapshotDTO.builder()
                        .tenantId(deliveryTotalDTO.getTenantId())
                        .deliveryFee(BigDecimal.ZERO)
                        .orderInfo(deliveryTotalDTO)
                        .ruleList(Collections.singletonList(ruleInfo))
                        .remark(DeliveryConstant.DELIVERY_FEE_DAILY)
                        .build();
            }
        }


        // 供应商运费 免运费供应商运费为0
        BigDecimal supplierDeliveryFee = BigDecimal.ZERO;
        List<Long> skuIds = orderItemDTOList.stream().map(OrderItemDeliveryDTO::getSkuId).collect(Collectors.toList());

        // 查询sku的类目信息
        Map<Long, AllCategoryDTO> skuCategoryMap = productQueryFacade.querySkuCategory(skuIds);
        orderItemDTOList.forEach(item -> {
            Optional.ofNullable(skuCategoryMap.get(item.getSkuId())).ifPresent(category -> {
                item.setFirstCategoryId(category.getFirstCategoryId());
            });
        });

        DeliveryFeeResultDTO deliveryFeeResultDTO = deliveryFeeStrategyContext.load(tenantDeliveryFeeRule.getType())
                .calculateDeliveryFee(orderDTO, tenantDeliveryFeeRule);
        supplierDeliveryFee = deliveryFeeResultDTO.getDeliveryFee();
        // 实时加价
        BigDecimal deliveryFee = null;
        if (MerchantDeliveryFeeRuleEnum.MerchatDeliveryFeeRulePriceTypeEnum.REAL_TIME_PRICE.getCode().equals(ruleInfo.getPriceType())) {
            deliveryFee = NumberUtil.add(supplierDeliveryFee, ruleInfo.getRelateNumber());
            // 实时上浮
        } else if (MerchantDeliveryFeeRuleEnum.MerchatDeliveryFeeRulePriceTypeEnum.REAL_TIME_FLOATING.getCode().equals(ruleInfo.getPriceType())) {
            deliveryFee = NumberUtil.div(NumberUtil.mul(supplierDeliveryFee, NumberUtil.add(NumberConstant.HUNDRED, ruleInfo.getRelateNumber())), NumberConstant.HUNDRED, NumberConstant.TWO);
        }

        log.info("{}最终运费计算完成,{},最终运费:{}",
                deliveryTotalDTO.getOrderInfoDTO().getWarehouseTypeEnum().getDesc(),
                DeliveryConstant.DELIVERY_FEE_FOLLOW,
                deliveryFee);

        return MerchantDeliveryFeeSnapshotDTO.builder()
                .tenantId(deliveryTotalDTO.getTenantId())
                .deliveryFee(deliveryFee)
                .orderInfo(deliveryTotalDTO)
                .ruleList(Collections.singletonList(ruleInfo))
                .hitRuleList(Collections.singletonList(DeliveryItemFeeDTO.builder()
                        .ruleId(ruleInfo.getRuleId())
                        .deliveryFee(deliveryFee)
                        .build()))
                .remark(DeliveryConstant.DELIVERY_FEE_FOLLOW)
                .build();
    }

    /**
     * 检查是否有同一天配送日的三方仓订单
     *
     * @return
     */
    private boolean checkSampleDayOrder(DeliveryTotalDTO deliveryDTO) {
        // 查询是否有同一配送日的订单
        OrderQueryParam queryDTO = OrderQueryParam.builder()
                .tenantId(deliveryDTO.getTenantId())
                .storeIds(Collections.singletonList(deliveryDTO.getStoreId()))
                .deliveryTime(deliveryDTO.getDeliveryTime())
                .supplierTenantId(deliveryDTO.getSupplierTenantId())
                .statusList(DeliveryConstant.DAILY_DELIVERY_STATUS)
                .build();
        List<Order> orderList = orderDao.queryList(queryDTO);
        return CollectionUtils.isNotEmpty(orderList);
    }


    /**
     * 检查是否有同一天配送日的三方仓订单 - 全鲜沐商品
     *
     * @return
     */
    private boolean checkSampleDayOrderOnlyXianmuItem(DeliveryTotalDTO deliveryDTO) {
        // 查询是否有同一配送日的订单
        OrderQueryParam queryDTO = OrderQueryParam.builder()
                .tenantId(deliveryDTO.getTenantId())
                .storeIds(Collections.singletonList(deliveryDTO.getStoreId()))
                .deliveryTime(deliveryDTO.getDeliveryTime())
                .supplierTenantId(deliveryDTO.getSupplierTenantId())
                .statusList(DeliveryConstant.DAILY_DELIVERY_STATUS)
                .build();
        List<Order> orderList = orderDao.queryList(queryDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }

        List<OrderItemSnapshot> orderItemSnapshotList = orderItemSnapshotDao.queryByOrderIds(orderList.stream().map(Order::getId).distinct().collect(Collectors.toList()));

        Map<Long, List<OrderItemSnapshot>> orderItemMap = orderItemSnapshotList.stream().collect(Collectors.groupingBy(OrderItemSnapshot::getOrderId));

        for (Map.Entry<Long, List<OrderItemSnapshot>> entry : orderItemMap.entrySet()) {
            List<OrderItemSnapshot> tmpList = entry.getValue();
            boolean allMatchQuotationTypeFlag = tmpList.stream().allMatch(e -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(e.getGoodsType()));
            if (allMatchQuotationTypeFlag) {
                return true;
            }
        }

        return false;
    }


    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, Order orderDTO) {
        return null;
    }
}
