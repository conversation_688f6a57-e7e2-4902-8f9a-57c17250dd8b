package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.application.service.delivery.dto.DeliveryFeeResultDTO;
import com.cosfo.ordercenter.application.service.delivery.dto.OrderDeliveryDTO;
import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@Service
public class FreeDeliveryFeeExecutor implements DeliveryFeeStrategyExecutor {

    @Override
    public TenantDeliveryEnum.TypeEnum tenantStrategy() {
        return TenantDeliveryEnum.TypeEnum.FREE;
    }

    @Override
    public DeliveryFeeResultDTO calculateDeliveryFee(OrderDeliveryDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
        deliveryFeeResultDTO.setDeliveryFee(BigDecimal.ZERO);
        return deliveryFeeResultDTO;
    }

}
