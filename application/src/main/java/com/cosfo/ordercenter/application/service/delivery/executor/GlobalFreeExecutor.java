package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.*;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 全局包邮免运费 每单
 *
 * @author: xiaowk
 * @date: 2025/4/7 上午11:01
 */
@Slf4j
@Service
public class GlobalFreeExecutor implements MerchantDeliveryRuleTypeExecutor {

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;

    @Override
    public List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType() {
        return Collections.singletonList(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.GLOBAL_FREE);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        return calGlobalFreeDeliveryFee(deliveryDTO, ruleList);
    }


    /**
     * 计算全局包邮规则
     *
     * @param deliveryTotalDTO
     * @param deliveryRuleInfoDTOS
     * @return
     */
    private MerchantDeliveryFeeSnapshotDTO calGlobalFreeDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS) {
        DeliveryOrderInfoDTO orderInfo = deliveryTotalDTO.getOrderInfoDTO();

        BigDecimal mulOrderTotalSum = Optional.ofNullable(orderInfo.getMulOrderTotalPrice()).orElse(BigDecimal.ZERO);

        for (MerchantDeliveryRuleInfoDTO deliveryRuleInfoDTO : deliveryRuleInfoDTOS) {
            // 校验是否匹配例外规则
            if (!checkHitRule(orderInfo, deliveryRuleInfoDTO)) {
                continue;
            }

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总金额>门槛值的，即为门槛运费
            boolean hit = stepFeeDescList.stream()
                    .filter(s -> mulOrderTotalSum.compareTo(s.getStepThreshold()) >= 0)
                    .findFirst()
                    .isPresent();

            if (hit) {
                return MerchantDeliveryFeeSnapshotDTO.builder()
                        .tenantId(deliveryTotalDTO.getTenantId())
                        .deliveryFee(BigDecimal.ZERO)
                        .orderInfo(deliveryTotalDTO)
                        .ruleList(deliveryRuleInfoDTOS)
                        .hitRuleList(Collections.singletonList(DeliveryItemFeeDTO.builder().ruleId(deliveryRuleInfoDTO.getRuleId()).deliveryFee(BigDecimal.ZERO).build()))
                        .remark(DeliveryConstant.DELIVERY_FEE_GLOBAL)
                        .build();
            }
        }

        return null;
    }


    private boolean checkHitRule(DeliveryOrderInfoDTO orderInfo, MerchantDeliveryRuleInfoDTO deliveryRule) {
        // 规则中保存的省份没有"省""市"字样
        String storeProvince = orderInfo.getStoreProvince().replaceAll("(?<!市)省$|(?<!市)市$", "");

        // 规则未关联区域
        Set<List<String>> hitAreaList = deliveryRule.getHitAreaList();
        // 部分地方没有区
        List<String> orderArea = Arrays.asList(storeProvince, orderInfo.getStoreCity(), orderInfo.getStoreArea());
        List<String> orderCity = Arrays.asList(storeProvince, orderInfo.getStoreCity());
        if (org.springframework.util.CollectionUtils.isEmpty(hitAreaList) || (!hitAreaList.contains(orderArea) && !hitAreaList.contains(orderCity))) {
            return false;
        }

        return true;
    }


    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, Order orderDTO) {
        // 只有发货前 且 整单售后 才需要计算退运费
        if (!Objects.equals(orderAfterSaleInputs.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
                    .orderId(orderDTO.getId())
                    .deliveryFee(null)
                    .remark("发货后售后不退运费")
                    .build();
        }

        List<OrderAfterSaleEntity> orderAfterSaleEntities = OrderAfterSaleConverter.convertPOToEntityList(orderAfterSaleInputs);
        if (!orderAfterSaleBizService.validationIsNeedRefundDeliveryFee(orderAfterSaleEntities)) {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                    .orderId(orderDTO.getId())
                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
                    .deliveryFee(null)
                    .remark(DeliveryConstant.DELIVERY_SUB_RETURN)
                    .build();
        }

        return null;
    }


}
