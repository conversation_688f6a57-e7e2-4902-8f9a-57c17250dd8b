package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.application.inbound.provider.aftersale.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.*;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.common.enums.OrderStatusEnum;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderQueryParam;
import com.cosfo.ordercenter.facade.StoreQueryFacade;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemDao;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 全局包邮免运费 每天累计订单
 * TODO: 每天累计订单全局包邮-注释代码，后续需要时启用
 *
 * @author: xiaowk
 * @date: 2025/3/3 下午5:34
 */
//@Slf4j
//@Service
//public class GlobalFreePerDailyExecutor implements MerchantDeliveryRuleTypeExecutor {
//    /**
//     * 查询同一下单日的订单状态
//     * status :3, 4, 5, 10,11,12,13
//     */
//    public static final List<Integer> DAILY_ORDER_STATUS = Arrays.asList(OrderStatusEnum.WAIT_DELIVERY.getCode(),
//            OrderStatusEnum.DELIVERING.getCode(),
//            OrderStatusEnum.FINISHED.getCode(),
//            OrderStatusEnum.WAITING_DELIVERY.getCode(),
//            OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(),
//            OrderStatusEnum.OUT_OF_STORAGE.getCode(),
//            OrderStatusEnum.WAIT_AUDIT.getCode()
//    );
//
//
//    @Resource
//    private OrderDao orderDao;
//    @Resource
//    private OrderItemDao orderItemDao;
//    @Resource
//    private OrderAfterSaleDao orderAfterSaleDao;
//    @Resource
//    private StoreQueryFacade storeService;
//    @Resource
//    private OrderAfterSaleBizService orderAfterSaleBizService;
//
//    @Override
//    public List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType() {
//        return Collections.singletonList(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.GLOBAL_FREE_PER_DAILY);
//    }
//
//    @Override
//    public MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
//        return calGlobalFreeDeliveryFee(deliveryDTO, ruleList);
//    }
//
//
//    /**
//     * 计算全局包邮规则
//     *
//     * @param deliveryTotalDTO
//     * @param deliveryRuleInfoDTOS
//     * @return
//     */
//    private MerchantDeliveryFeeSnapshotDTO calGlobalFreeDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS) {
//        DeliveryOrderInfoDTO orderInfo = deliveryTotalDTO.getOrderInfoDTO();
//
//        OrderQueryParam orderQueryParam = OrderQueryParam.builder()
//                .tenantId(deliveryTotalDTO.getTenantId())
//                .storeIds(Collections.singletonList(deliveryTotalDTO.getStoreId()))
//                .createStartTime(LocalDate.now().atStartOfDay())
//                .createEndTime(LocalDate.now().plusDays(1).atStartOfDay())
//                .statusList(DAILY_ORDER_STATUS)
//                .build();
//        List<Order> orderDTOS = orderDao.queryList(orderQueryParam);
//
//        // 计算累计和
//        BigDecimal totalSum = buildHistoryOrderPrice(deliveryTotalDTO.getTenantId(), orderDTOS, null);
//        BigDecimal mulOrderTotalSum = totalSum.add(Optional.ofNullable(orderInfo.getMulOrderTotalPrice()).orElse(BigDecimal.ZERO));
//
//        for (MerchantDeliveryRuleInfoDTO deliveryRuleInfoDTO : deliveryRuleInfoDTOS) {
//            // 校验是否匹配例外规则
//            if (!checkHitRule(orderInfo, deliveryRuleInfoDTO)) {
//                continue;
//            }
//
//            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
//            // 门槛倒序排，最先一个满足总金额>门槛值的，即为门槛运费
//            boolean hit = stepFeeDescList.stream()
//                    .filter(s -> mulOrderTotalSum.compareTo(s.getStepThreshold()) >= 0)
//                    .findFirst()
//                    .isPresent();
//
//            if (hit) {
//                return MerchantDeliveryFeeSnapshotDTO.builder()
//                        .tenantId(deliveryTotalDTO.getTenantId())
//                        .deliveryFee(BigDecimal.ZERO)
//                        .orderInfo(deliveryTotalDTO)
//                        .ruleList(deliveryRuleInfoDTOS)
//                        .hitRuleList(Collections.singletonList(DeliveryItemFeeDTO.builder().ruleId(deliveryRuleInfoDTO.getRuleId()).deliveryFee(BigDecimal.ZERO).build()))
//                        .remark(DeliveryConstant.DELIVERY_FEE_GLOBAL)
//                        .build();
//            }
//        }
//
//        return null;
//    }
//
//
//    private BigDecimal buildHistoryOrderPrice(Long tenantId, List<Order> orderList, Set<Long> excludeOrderIds) {
//        if (CollectionUtils.isEmpty(orderList)) {
//            return BigDecimal.ZERO;
//        }
//
//        orderList = orderList.stream()
//                .filter(e -> CollectionUtils.isEmpty(excludeOrderIds) || !excludeOrderIds.contains(e.getId()))
//                .collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(orderList)) {
//            return BigDecimal.ZERO;
//        }
//
//        List<Long> orderIds = orderList.stream()
//                .map(Order::getId)
//                .collect(Collectors.toList());
//
//
//        // 查询订单项
//        List<OrderItemWithSnapshotEntity> orderItems = orderItemDao.queryOrderItemVOByOrderIds(tenantId, orderIds);
//        Map<Long, OrderItemWithSnapshotEntity> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItemWithSnapshotEntity::getOrderItemId, Function.identity(), (v1, v2) -> v1));
//
//        // 计算订单累计和
//        BigDecimal orderTotalPrice = orderList.stream()
//                .map(order -> order.getPayablePrice().subtract(order.getDeliveryFee()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        // 查询售后单
//        List<OrderAfterSale> orderAfterSaleResultDTOS = orderAfterSaleDao.queryList(OrderAfterSaleListQueryParam.builder()
//                .orderIds(orderIds)
//                .afterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType())
//                .serviceType(DeliveryConstant.SERVICE_TYPE)
//                .statusList(Lists.newArrayList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), OrderAfterSaleStatusEnum.REFUNDING.getValue()))
//                .build());
//
//        BigDecimal afterSaleTotalPrice = orderAfterSaleResultDTOS.stream()
//                .map(e ->
//                        Optional.ofNullable(orderItemMap.get(e.getOrderItemId()))
//                                .map(OrderItemWithSnapshotEntity::getPayablePrice)
//                                .orElse(BigDecimal.ZERO)
//                                .multiply(new BigDecimal(String.valueOf(e.getAmount()))))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        // 下单金额-售后金额 = 剩余下单金额
//        return orderTotalPrice.subtract(afterSaleTotalPrice);
//    }
//
//
//    private boolean checkHitRule(DeliveryOrderInfoDTO orderInfo, MerchantDeliveryRuleInfoDTO deliveryRule) {
//        // 规则中保存的省份没有"省""市"字样
//        String storeProvince = orderInfo.getStoreProvince().replaceAll("(?<!市)省$|(?<!市)市$", "");
//
//        // 规则未关联区域
//        Set<List<String>> hitAreaList = deliveryRule.getHitAreaList();
//        // 部分地方没有区
//        List<String> orderArea = Arrays.asList(storeProvince, orderInfo.getStoreCity(), orderInfo.getStoreArea());
//        List<String> orderCity = Arrays.asList(storeProvince, orderInfo.getStoreCity());
//        if (CollectionUtils.isEmpty(hitAreaList) || (!hitAreaList.contains(orderArea) && !hitAreaList.contains(orderCity))) {
//            return false;
//        }
//
//        return true;
//    }
//
//
//    @Override
//    public MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, Order orderDTO) {
//        // 只有发货前 且 整单售后 才需要计算退运费
//        if (!Objects.equals(orderAfterSaleInputs.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
//            return MerchantDeliveryFeeSnapshotDTO.builder()
//                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
//                    .orderId(orderDTO.getId())
//                    .deliveryFee(null)
//                    .remark("发货后售后不退运费")
//                    .build();
//        }
//
//        List<OrderAfterSaleEntity> orderAfterSaleEntities = OrderAfterSaleConverter.convertPOToEntityList(orderAfterSaleInputs);
//        if (!orderAfterSaleBizService.validationIsNeedRefundDeliveryFee(orderAfterSaleEntities)) {
//            return MerchantDeliveryFeeSnapshotDTO.builder()
//                    .orderId(orderDTO.getId())
//                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
//                    .deliveryFee(null)
//                    .remark(DeliveryConstant.DELIVERY_SUB_RETURN)
//                    .build();
//        }
//
//        // 同一下单日的所有订单 = 有效订单
//        List<Order> dailyOrderList = orderDao.queryList(OrderQueryParam.builder()
//                .tenantId(orderAfterSaleInputs.get(0).getTenantId())
//                .storeIds(Collections.singletonList(orderAfterSaleInputs.get(0).getStoreId()))
//                .createStartTime(orderDTO.getCreateTime().toLocalDate().atStartOfDay())
//                .createEndTime(orderDTO.getCreateTime().toLocalDate().plusDays(1).atStartOfDay())
//                .statusList(DAILY_ORDER_STATUS)
//                .build());
//
//
//        // 计算剩余累计和，排除当前订单
//        BigDecimal mulOrderTotalSum = buildHistoryOrderPrice(orderAfterSaleInputs.get(0).getTenantId(), dailyOrderList, Sets.newHashSet(orderDTO.getId()));
//
//        // 构建计算运费信息
//        DeliveryOrderInfoDTO orderInfo = new DeliveryOrderInfoDTO();
//
//        MerchantAddressResultResp merchantAddressDTO = storeService.queryDefaultAddress(orderDTO.getStoreId(), orderDTO.getTenantId());
//        orderInfo.setStoreProvince(merchantAddressDTO.getProvince());
//        orderInfo.setStoreCity(merchantAddressDTO.getCity());
//        orderInfo.setStoreArea(merchantAddressDTO.getArea());
//        orderInfo.setStorePoi(merchantAddressDTO.getPoiNote());
//
//        for (MerchantDeliveryRuleInfoDTO deliveryRuleInfoDTO : deliveryRuleInfoDTOS) {
//            // 校验是否匹配例外规则
//            if (!checkHitRule(orderInfo, deliveryRuleInfoDTO)) {
//                continue;
//            }
//
//            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
//            // 门槛倒序排，最先一个满足总金额>门槛值的，即为门槛运费
//            boolean hit = stepFeeDescList.stream()
//                    .filter(s -> mulOrderTotalSum.compareTo(s.getStepThreshold()) >= 0)
//                    .findFirst()
//                    .isPresent();
//
//            if (hit) {
//                return MerchantDeliveryFeeSnapshotDTO.builder()
//                        .orderId(orderDTO.getId())
//                        .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
//                        .deliveryFee(orderDTO.getDeliveryFee())
//                        .remark("全局包邮售后全退运费")
//                        .build();
//            }
//        }
//
//        return null;
//    }
//
//
//}
