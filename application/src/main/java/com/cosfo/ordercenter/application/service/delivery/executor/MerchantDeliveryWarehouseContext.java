package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchantDeliveryWarehouseContext {

    @Resource
    private List<MerchantDeliveryWarehouseExecutor> executors;

    public MerchantDeliveryWarehouseExecutor load(Integer warehouseType) {
        WarehouseTypeEnum typeEnum = WarehouseTypeEnum.getByCode(warehouseType);
        for (MerchantDeliveryWarehouseExecutor warehouseExecutor : executors) {
            if (warehouseExecutor.warehouseType().contains(typeEnum)) {
                return warehouseExecutor;
            }
        }
        throw new BizException("仓库类型不存在");
    }
}
