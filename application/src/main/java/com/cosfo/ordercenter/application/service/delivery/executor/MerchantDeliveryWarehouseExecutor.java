package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;


import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 18:34
 * @Description:
 */
public interface MerchantDeliveryWarehouseExecutor {

    List<WarehouseTypeEnum> warehouseType();


    /**
     * 根据仓库类型及规则配置，查询应付运费
     *
     * @param deliveryTotalDTO
     * @param ruleList
     * @return
     */
    MerchantDeliveryFeeSnapshotDTO calculateDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, List<MerchantDeliveryRuleInfoDTO> ruleList);

    /**
     * 查询售后时的应退运费
     * @param orderAfterSaleInputs
     * @param orderDTO
     * @return
     */
    MerchantDeliveryFeeSnapshotDTO calculateRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, Order orderDTO);
}
