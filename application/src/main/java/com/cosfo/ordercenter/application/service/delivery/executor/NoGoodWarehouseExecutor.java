package com.cosfo.ordercenter.application.service.delivery.executor;

import com.cosfo.ordercenter.application.service.delivery.RefundDeliveryFeeService;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;

import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 14:57
 * @Description:
 */
@Service
public class NoGoodWarehouseExecutor implements MerchantDeliveryWarehouseExecutor {
    @Resource
    private MerchantDeliveryRuleTypeContext ruleTypeContext;
    @Resource
    private RefundDeliveryFeeService refundDeliveryFeeService;

    @Override
    public List<WarehouseTypeEnum> warehouseType() {
        return Collections.singletonList(WarehouseTypeEnum.PROPRIETARY);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        return ruleTypeContext.load(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.SINGLE.getCode())
            .calculateRuleTypeDeliveryFee(deliveryTotalDTO, ruleList);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, Order orderDTO) {
        return MerchantDeliveryFeeSnapshotDTO.builder()
            .orderId(orderDTO.getId())
            .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
            .deliveryFee(refundDeliveryFeeService.calculateNormalDeliveryFee(orderAfterSaleInputs, orderDTO.getDeliveryFee()))
            .build();
    }
}
