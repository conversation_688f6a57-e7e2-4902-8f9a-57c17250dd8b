package com.cosfo.ordercenter.application.service.delivery.executor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderParamConverter;
import com.cosfo.ordercenter.application.service.delivery.dto.MergeOrderInfoDTO;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.*;
import com.cosfo.ordercenter.client.validgroup.DailyTypeGroup;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.common.util.ValidatorUtils;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.order.entity.OrderDetailEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderQueryParam;
import com.cosfo.ordercenter.facade.StoreQueryFacade;
import com.cosfo.ordercenter.infrastructure.dao.OrderAddressDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemDao;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;
import com.cosfo.ordercenter.infrastructure.model.order.OrderDetail;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 11:41
 * @Description:
 */
@Slf4j
@Service
public class PerDailyExecutor implements MerchantDeliveryRuleTypeExecutor {
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private StoreQueryFacade storeService;
    @Resource
    private MerchantDeliveryRuleTypeContext merchantDeliveryRuleTypeContext;

    @Override
    public List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType() {
        return Collections.singletonList(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.DAILY);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        ValidatorUtils.validateEntity(deliveryDTO, DailyTypeGroup.class);
        LocalDate deliveryTime = deliveryDTO.getDeliveryTime();
        OrderQueryReq queryDTO = OrderQueryReq.builder()
                .tenantId(deliveryDTO.getTenantId())
                .storeIds(Collections.singletonList(deliveryDTO.getStoreId()))
                .deliveryTime(deliveryTime)
                .supplierTenantId(deliveryDTO.getSupplierTenantId())
                .statusList(DeliveryConstant.DAILY_DELIVERY_STATUS)
                .fulfillmentType(deliveryDTO.getOrderInfoDTO().getFulfillmentType())
                .build();
        List<Order> deliveryEffectOrderList = queryEffectiveDeliveryOrders(queryDTO, deliveryDTO.getOrderInfoDTO().getStorePoi());
        return calculateThreeDailyDeliveryFee(deliveryDTO, ruleList, deliveryEffectOrderList);
    }


    /**
     * 过滤出所有在同一地址的订单
     *
     * @param queryDTO
     * @param storePoi
     * @return
     */
    public List<Order> queryEffectiveDeliveryOrders(OrderQueryReq queryDTO, String storePoi) {
        OrderQueryParam orderQueryParam = OrderParamConverter.convertToOrderQueryParam(queryDTO);
        List<Order> orderDTOS = orderDao.queryList(orderQueryParam);
        List<Long> orderIds = orderDTOS.stream()
                .map(Order::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        List<OrderAddress> orderAddressDTOS = orderAddressDao.queryByOrderIds(orderIds, queryDTO.getTenantId());
        Map<Long, String> addressMap = orderAddressDTOS.stream().collect(Collectors.toMap(OrderAddress::getOrderId, OrderAddress::getPoiNote, (k1, k2) -> k1));

        return orderDTOS.stream()
                .filter(i -> {
                    String address = addressMap.get(i.getId());
                    return Objects.nonNull(address) && address.equals(storePoi);
                })
                .collect(Collectors.toList());

    }

    /**
     * 三方仓 - 每日运费
     * 以同一配送日的历史订单总和，计算阶梯运费
     *
     * @param deliveryDTO
     * @param deliveryRuleInfoDTOS
     * @param orderList
     * @return
     */
    private MerchantDeliveryFeeSnapshotDTO calculateThreeDailyDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, List<Order> orderList) {
        // 查询历史订单，组成一个大订单
        List<Long> afterSaleIds = buildHistoryOrderInfo(deliveryDTO, orderList);
        // 计算阶梯运费
        MerchantDeliveryFeeSnapshotDTO feeSnapshotVO = merchantDeliveryRuleTypeContext
                .load(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.SINGLE.getCode())
                .calculateRuleTypeDeliveryFee(deliveryDTO, deliveryRuleInfoDTOS);
        // 补充参与计算的订单
        List<Long> orderIds = orderList.stream()
                .map(Order::getId)
                .collect(Collectors.toList());
        BigDecimal paidDeliveryFee = Optional.ofNullable(deliveryDTO.getRelatedOrderInfoDTO()).orElse(new DeliveryRelatedOrderInfoDTO()).getPaidDeliveryFee();
        paidDeliveryFee = Optional.ofNullable(paidDeliveryFee).orElse(BigDecimal.ZERO);
        feeSnapshotVO.getOrderInfo().setRelatedOrderInfoDTO(DeliveryRelatedOrderInfoDTO.builder()
                .orderIds(orderIds)
                .afterSaleIds(afterSaleIds)
                .paidDeliveryFee(paidDeliveryFee)
                .receivableDeliveryFee(feeSnapshotVO.getDeliveryFee())
                .build());

        // 下单时运费 = 当日应付运费 - 已付运费
        BigDecimal deliveryFee = feeSnapshotVO.getDeliveryFee().subtract(paidDeliveryFee);
        feeSnapshotVO.setDeliveryFee(deliveryFee.compareTo(BigDecimal.ZERO) > 0 ? deliveryFee : BigDecimal.ZERO);
        return feeSnapshotVO;
    }


    /**
     * 构建历史订单的总订单信息
     *
     * @param deliveryDTO
     * @param orderList
     */
    private List<Long> buildHistoryOrderInfo(DeliveryTotalDTO deliveryDTO, List<Order> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }

        List<Long> orderIds = orderList.stream()
                .map(Order::getId)
                .collect(Collectors.toList());
        // 查询订单项
        List<OrderItemWithSnapshotEntity> orderItems = orderItemDao.queryOrderItemVOByOrderIds(deliveryDTO.getTenantId(), orderIds);
        Map<Long, OrderItemWithSnapshotEntity> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItemWithSnapshotEntity::getOrderItemId, Function.identity(), (v1, v2) -> v1));

        // 查询售后单
        List<OrderAfterSale> orderAfterSaleResultDTOS = orderAfterSaleDao.queryList(OrderAfterSaleListQueryParam.builder()
                .orderIds(orderIds)
                .afterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType())
                .serviceType(DeliveryConstant.SERVICE_TYPE)
                .statusList(Collections.singletonList(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()))
                .build());
        List<Long> afterSaleIds = orderAfterSaleResultDTOS.stream().map(OrderAfterSale::getId).collect(Collectors.toList());
        Map<Long, List<OrderAfterSale>> orderAfterSaleMap = orderAfterSaleResultDTOS.stream().collect(Collectors.groupingBy(OrderAfterSale::getOrderItemId));

        // 今日已付运费 = 当日有效订单的运费 - 当日有效订单所关联售后单的已退运费
        BigDecimal paidDeliveryFee = orderList.stream().map(Order::getDeliveryFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        paidDeliveryFee = paidDeliveryFee.subtract(orderAfterSaleResultDTOS.stream()
                .map(OrderAfterSale::getDeliveryFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        deliveryDTO.setRelatedOrderInfoDTO(DeliveryRelatedOrderInfoDTO.builder()
                .paidDeliveryFee(paidDeliveryFee)
                .build());

        // 构建订单
        List<DeliveryOrderItemInfoDTO> orderItemInfoDTOList = deliveryDTO.getOrderItemInfoDTOList();

        // 本次下单的商品总数量/价格
        Map<Long, MergeOrderInfoDTO> deliveryOrderMap = orderItemInfoDTOList.stream().collect(Collectors.groupingBy(DeliveryOrderItemInfoDTO::getItemId,
                Collectors.collectingAndThen(Collectors.toList(), list -> {
                    BigDecimal totalPrice = BigDecimal.ZERO;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    int totalCount = 0;
                    for (DeliveryOrderItemInfoDTO item : list) {
                        totalCount = totalCount + Optional.ofNullable(item.getItemCount()).orElse(0);
                        totalPrice = totalPrice.add(Optional.ofNullable(item.getItemTotalPrice()).orElse(BigDecimal.ZERO));
                        totalWeight = totalWeight.add(Optional.ofNullable(item.getTotalWeight()).orElse(BigDecimal.ZERO));
                    }
                    MergeOrderInfoDTO mergeOrderInfoDTO = new MergeOrderInfoDTO();
                    mergeOrderInfoDTO.setSkuId(list.get(0).getSkuId());
                    mergeOrderInfoDTO.setSupplierSkuId(list.get(0).getSupplierSkuId());
                    mergeOrderInfoDTO.setItemTotalCount(totalCount);
                    mergeOrderInfoDTO.setItemTotalPrice(totalPrice);
                    mergeOrderInfoDTO.setTotalWeight(totalWeight);
                    return mergeOrderInfoDTO;
                })));

        Map<Long, MergeOrderInfoDTO> historyOrderMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemWithSnapshotEntity::getItemId,
                Collectors.collectingAndThen(Collectors.toList(), list -> {
                    BigDecimal totalPrice = BigDecimal.ZERO;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    int totalCount = 0;
                    List<Long> orderItemIds = new ArrayList<>();
                    for (OrderItemWithSnapshotEntity item : list) {
                        totalCount = totalCount + Optional.ofNullable(item.getAmount()).orElse(0);
                        totalPrice = totalPrice.add(Optional.ofNullable(item.getTotalPrice()).orElse(BigDecimal.ZERO));
                        totalWeight = totalWeight.add(Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(String.valueOf(item.getAmount()))));
                        orderItemIds.add(item.getOrderItemId());
                    }
                    MergeOrderInfoDTO mergeOrderInfoDTO = new MergeOrderInfoDTO();
                    mergeOrderInfoDTO.setSkuId(list.get(0).getSkuId());
                    mergeOrderInfoDTO.setSupplierSkuId(list.get(0).getSupplierSkuId());
                    mergeOrderInfoDTO.setItemTotalCount(totalCount);
                    mergeOrderInfoDTO.setItemTotalPrice(totalPrice);
                    mergeOrderInfoDTO.setTotalWeight(totalWeight);
                    mergeOrderInfoDTO.setOrderItemIds(orderItemIds);
                    return mergeOrderInfoDTO;
                })));


        Set<Long> orderItemIds = deliveryOrderMap.keySet();
        Set<Long> historyItemIds = historyOrderMap.keySet();
        Set<Long> totalItemIds = new HashSet<Long>();
        totalItemIds.addAll(orderItemIds);
        totalItemIds.addAll(historyItemIds);

        List<DeliveryOrderItemInfoDTO> orderItemList = new ArrayList<>();
        Integer orderTotalCount = 0;
        BigDecimal orderTotalPrice = BigDecimal.ZERO;
        for (Long orderItemId : totalItemIds) {
            MergeOrderInfoDTO deliveryOrderInfo = Optional.ofNullable(deliveryOrderMap.get(orderItemId)).orElse(new MergeOrderInfoDTO());
            MergeOrderInfoDTO historyOrderInfo = Optional.ofNullable(historyOrderMap.get(orderItemId)).orElse(new MergeOrderInfoDTO());
            // 构造下单数据
            DeliveryOrderItemInfoDTO orderItem = new DeliveryOrderItemInfoDTO();
            orderItem.setItemId(orderItemId);
            orderItem.setSkuId(Optional.ofNullable(deliveryOrderInfo.getSkuId())
                    .orElse(historyOrderInfo.getSkuId()));
            orderItem.setSupplierSkuId(Optional.ofNullable(deliveryOrderInfo.getSupplierSkuId())
                    .orElse(historyOrderInfo.getSupplierSkuId()));
            // 下单数量相加
            orderItem.setItemCount(Optional.ofNullable(deliveryOrderInfo.getItemTotalCount()).orElse(0)
                    + Optional.ofNullable(historyOrderInfo.getItemTotalCount()).orElse(0));
            // 下单金额相加
            orderItem.setItemTotalPrice(Optional.ofNullable(deliveryOrderInfo.getItemTotalPrice()).orElse(BigDecimal.ZERO).add(
                    Optional.ofNullable(historyOrderInfo.getItemTotalPrice()).orElse(BigDecimal.ZERO)));
            // 下单重量相加
            orderItem.setTotalWeight(Optional.ofNullable(deliveryOrderInfo.getTotalWeight()).orElse(BigDecimal.ZERO).add(
                    Optional.ofNullable(historyOrderInfo.getTotalWeight()).orElse(BigDecimal.ZERO)));

            // 减去售后的金额和数量
            List<OrderAfterSale> orderAfterSaleItem = Optional.ofNullable(historyOrderInfo.getOrderItemIds()).orElse(Collections.emptyList()).stream()
                    .map(orderAfterSaleMap::get)
                    .filter(Objects::nonNull)
                    .collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
            if (CollectionUtils.isNotEmpty(orderAfterSaleItem)) {
                orderItem.setItemCount(orderItem.getItemCount() - orderAfterSaleItem.stream()
                        .mapToInt(OrderAfterSale::getAmount)
                        .sum());
                orderItem.setItemTotalPrice(orderItem.getItemTotalPrice()
                        .subtract(orderAfterSaleItem.stream().
                                map(OrderAfterSale::getApplyPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)));

                BigDecimal refundWeight = orderAfterSaleItem.stream().
                        map(e -> {
                            // 订单项 重量快照
                            BigDecimal weightsnapshot = Optional.ofNullable(orderItemMap.get(e.getOrderItemId())).map(OrderItemWithSnapshotEntity::getWeight).orElse(BigDecimal.ZERO);
                            return NumberUtil.mul(e.getAmount(), weightsnapshot);
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                orderItem.setTotalWeight(orderItem.getTotalWeight().subtract(refundWeight));
            }
            orderTotalCount = orderTotalCount + orderItem.getItemCount();
            orderTotalPrice = orderTotalPrice.add(orderItem.getItemTotalPrice());
            orderItemList.add(orderItem);
        }
        deliveryDTO.setOrderItemInfoDTOList(orderItemList);
        deliveryDTO.getOrderInfoDTO().setOrderTotalCount(orderTotalCount);
        deliveryDTO.getOrderInfoDTO().setOrderTotalPrice(orderTotalPrice);

        return afterSaleIds;
    }


    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, Order orderDTO) {
        // 只有发货前 且 整单售后 才需要计算退运费
        if (!Objects.equals(orderAfterSaleInputs.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
                    .orderId(orderDTO.getId())
                    .deliveryFee(null)
                    .remark("发货后售后不退运费")
                    .build();
        }

        // 同一日配送的所有订单 = 有效订单
        LocalDate deliveryDate = orderDTO.getDeliveryTime().toLocalDate();
        List<Order> dailyOrderList = orderDao.queryList(OrderQueryParam.builder()
                .tenantId(orderAfterSaleInputs.get(0).getTenantId())
                .storeIds(Collections.singletonList(orderAfterSaleInputs.get(0).getStoreId()))
                .deliveryTime(deliveryDate)
                .statusList(DeliveryConstant.DELIVERY_EFFECTIVE_STATUS)
                .warehouseType(orderDTO.getWarehouseType())
                .fulfillmentType(orderDTO.getFulfillmentType())
                .build());
        // 关闭订单的场景下，此时订单状态是关单中，所以需要另外查询。状态列表不应包含任何过程状态。
        List<Long> dailyOrderIds = dailyOrderList.stream().map(Order::getId).collect(Collectors.toList());
        if (!dailyOrderIds.contains(orderDTO.getId())) {
            dailyOrderList.addAll(orderDao.queryList(OrderQueryParam.builder()
                    .orderId(orderDTO.getId())
                    .build()));
            dailyOrderIds.add(orderDTO.getId());
        }

        // 不同地址的运费分开计算
        List<OrderAddress> orderAddresses = orderAddressDao.queryByOrderIds(dailyOrderIds, orderDTO.getTenantId());
        OrderAddress currentOrderAddress = orderAddresses.stream()
                .filter(i -> i.getOrderId().equals(orderDTO.getId()))
                .findFirst()
                .orElse(new OrderAddress());
        List<Long> effectiveOrderIds = orderAddresses.stream()
                .filter(i -> i.getPoiNote().equals(currentOrderAddress.getPoiNote()))
                .map(OrderAddress::getOrderId)
                .collect(Collectors.toList());
        List<Order> effectOrderList = dailyOrderList.stream()
                .filter(i -> effectiveOrderIds.contains(i.getId()))
                .collect(Collectors.toList());


        // 有效订单的所有商品信息
        List<OrderDetailEntity> effectiveOrderItemList = orderDao.queryOrderDetail(effectiveOrderIds, orderDTO.getTenantId());
        // 有效订单的所有售后单
        List<OrderAfterSale> effectiveAfterSaleList = orderAfterSaleDao.queryList(OrderAfterSaleListQueryParam.builder()
                .orderIds(effectiveOrderIds)
                .statusList(DeliveryConstant.EFFECTIVE_AFTER_SALE_STATUS)
                .build());

        // 本次售后的订单及相关, 部分退货不退运费
        List<OrderDetailEntity> orderItemList = effectiveOrderItemList.stream()
                .filter(i -> i.getOrderId().equals(orderDTO.getId()))
                .collect(Collectors.toList());
        List<OrderAfterSale> afterSaleList = effectiveAfterSaleList.stream()
                .filter(i -> i.getOrderId().equals(orderDTO.getId()))
                .collect(Collectors.toList());
        int afterSaleCount = orderAfterSaleInputs.stream().mapToInt(OrderAfterSale::getAmount).sum();
        if (CollectionUtils.isNotEmpty(afterSaleList)) {
            afterSaleCount = afterSaleCount + afterSaleList.stream().mapToInt(OrderAfterSale::getAmount).sum();
        }
        int orderCount = orderItemList.stream().mapToInt(OrderDetailEntity::getAmount).sum();
        if (orderCount != afterSaleCount) {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                    .orderId(orderDTO.getId())
                    .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
                    .deliveryFee(null)
                    .remark(DeliveryConstant.DELIVERY_SUB_RETURN)
                    .build();
        }

        List<OrderDetail> orderDetails = OrderConverter.convertToDetailList(effectiveOrderItemList);

        // 构建计算运费信息
        DeliveryTotalDTO deliveryTotalDTO = buildRefundHistoryOrderInfo(effectOrderList, orderDetails, effectiveAfterSaleList, orderAfterSaleInputs);
        deliveryTotalDTO.setTenantId(orderDTO.getTenantId());
        deliveryTotalDTO.setStoreId(orderDTO.getStoreId());
        deliveryTotalDTO.setSupplierTenantId(orderDTO.getSupplierTenantId());
        deliveryTotalDTO.setDeliveryTime(deliveryDate);

        MerchantAddressResultResp merchantAddressDTO = storeService.queryDefaultAddress(deliveryTotalDTO.getStoreId(), deliveryTotalDTO.getTenantId());
        deliveryTotalDTO.getOrderInfoDTO().setStoreProvince(merchantAddressDTO.getProvince());
        deliveryTotalDTO.getOrderInfoDTO().setStoreCity(merchantAddressDTO.getCity());
        deliveryTotalDTO.getOrderInfoDTO().setStoreArea(merchantAddressDTO.getArea());
        deliveryTotalDTO.getOrderInfoDTO().setStorePoi(merchantAddressDTO.getPoiNote());
        deliveryTotalDTO.getOrderInfoDTO().setWarehouseType(orderDTO.getWarehouseType());
        deliveryTotalDTO.getOrderInfoDTO().setFulfillmentType(orderDTO.getFulfillmentType());

        deliveryTotalDTO.getRelatedOrderInfoDTO().setOrderIds(effectiveOrderIds);
        deliveryTotalDTO.getRelatedOrderInfoDTO().setOrderPaidDeliveryFee(orderDTO.getDeliveryFee());

        // 计算运费
        log.info("每日运费-开始计算退款后的应付运费：总订单信息：{}", JSON.toJSONString(deliveryTotalDTO));
        MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO = merchantDeliveryRuleTypeContext.load(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.SINGLE.getCode())
                .calculateRuleTypeDeliveryFee(deliveryTotalDTO, deliveryRuleInfoDTOS);
        deliveryFeeSnapshotDTO.setScene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE);
        deliveryFeeSnapshotDTO.setOrderId(orderDTO.getId());
        DeliveryRelatedOrderInfoDTO relatedOrderInfoDTO = deliveryFeeSnapshotDTO.getOrderInfo().getRelatedOrderInfoDTO();
        relatedOrderInfoDTO.setReceivableDeliveryFee(deliveryFeeSnapshotDTO.getDeliveryFee());
        BigDecimal deliveryFee = relatedOrderInfoDTO.getPaidDeliveryFee().subtract(relatedOrderInfoDTO.getReceivableDeliveryFee());
        // 应退运费一定 >0
        deliveryFee = deliveryFee.compareTo(BigDecimal.ZERO) > 0 ? deliveryFee : BigDecimal.ZERO;
        // 应退运费 不能大于当笔订单的运费
        deliveryFee = deliveryFee.compareTo(orderDTO.getDeliveryFee()) > 0 ? orderDTO.getDeliveryFee() : deliveryFee;
        deliveryFeeSnapshotDTO.setDeliveryFee(deliveryFee);
        return deliveryFeeSnapshotDTO;
    }


    /**
     * 构建当日有效订单,作为一个大订单
     *
     * @param effectOrderList        当日有效订单
     * @param effectiveOrderItemList 有效订单的订单项
     * @param effectiveAfterSaleList 有效订单关联的售后信息
     * @param orderAfterSaleInputs   申请售后信息
     * @return
     */
    private DeliveryTotalDTO buildRefundHistoryOrderInfo(List<Order> effectOrderList,
                                                         List<OrderDetail> effectiveOrderItemList,
                                                         List<OrderAfterSale> effectiveAfterSaleList,
                                                         List<OrderAfterSale> orderAfterSaleInputs) {

        Map<Long, OrderDetail> orderItemMap = effectiveOrderItemList.stream().collect(Collectors.toMap(OrderDetail::getOrderItemId, Function.identity(), (v1, v2) -> v1));


        // 当日全部下单商品去重,按商品汇总总价格/数量
        Map<Long, MergeOrderInfoDTO> effectiveOrderItemMap = effectiveOrderItemList.stream().collect(Collectors.groupingBy(OrderDetail::getItemId,
                Collectors.collectingAndThen(Collectors.toList(), list -> {
                    BigDecimal totalPrice = BigDecimal.ZERO;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    int totalCount = 0;
                    List<Long> orderItemIds = new ArrayList<>();
                    for (OrderDetail item : list) {
                        totalCount = totalCount + Optional.ofNullable(item.getAmount()).orElse(0);
                        totalPrice = totalPrice.add(Optional.ofNullable(item.getSkuTotalPrice()).orElse(BigDecimal.ZERO));
                        totalWeight = totalWeight.add(Optional.ofNullable(item.getWeight()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(String.valueOf(item.getAmount()))));
                        orderItemIds.add(item.getOrderItemId());
                    }
                    MergeOrderInfoDTO mergeOrderInfoDTO = new MergeOrderInfoDTO();
                    mergeOrderInfoDTO.setSkuId(list.get(0).getSkuId());
                    mergeOrderInfoDTO.setSupplierSkuId(list.get(0).getSupplySkuId());
                    mergeOrderInfoDTO.setOrderItemIds(orderItemIds);
                    mergeOrderInfoDTO.setItemTotalCount(totalCount);
                    mergeOrderInfoDTO.setItemTotalPrice(totalPrice);
                    mergeOrderInfoDTO.setTotalWeight(totalWeight);
                    return mergeOrderInfoDTO;
                })));

        // 售后信息合并
        List<OrderAfterSale> orderAfterSaleList = new ArrayList<>();
        orderAfterSaleList.addAll(effectiveAfterSaleList);
        orderAfterSaleList.addAll(orderAfterSaleInputs);
        Map<Long, List<OrderAfterSale>> itemAfterSaleMap = orderAfterSaleList.stream().collect(Collectors.groupingBy(OrderAfterSale::getOrderItemId));

        for (Long itemId : effectiveOrderItemMap.keySet()) {
            MergeOrderInfoDTO mergeOrderInfoDTO = effectiveOrderItemMap.get(itemId);
            List<OrderAfterSale> orderAfterSaleItem = mergeOrderInfoDTO.getOrderItemIds().stream()
                    .map(itemAfterSaleMap::get)
                    .filter(Objects::nonNull)
                    .collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalWeight = BigDecimal.ZERO;
            int totalCount = 0;
            for (OrderAfterSale orderAfterSale : orderAfterSaleItem) {
                totalCount = totalCount + orderAfterSale.getAmount();
                totalPrice = totalPrice.add(orderAfterSale.getApplyPrice());
                // 订单项 重量快照
                BigDecimal weightsnapshot = Optional.ofNullable(orderItemMap.get(orderAfterSale.getOrderItemId())).map(OrderDetail::getWeight).orElse(BigDecimal.ZERO);
                totalWeight = totalWeight.add(NumberUtil.mul(orderAfterSale.getAmount(), weightsnapshot));
            }
            mergeOrderInfoDTO.setItemTotalCount(mergeOrderInfoDTO.getItemTotalCount() - totalCount);
            mergeOrderInfoDTO.setItemTotalPrice(mergeOrderInfoDTO.getItemTotalPrice().subtract(totalPrice));
            mergeOrderInfoDTO.setTotalWeight(mergeOrderInfoDTO.getTotalWeight().subtract(totalWeight));
        }


        // 构建大订单的订单项
        BigDecimal orderTotalPrice = BigDecimal.ZERO;
        int orderTotalCount = 0;
        List<DeliveryOrderItemInfoDTO> deliveryOrderItemList = new ArrayList<>();

        for (Long itemId : effectiveOrderItemMap.keySet()) {
            MergeOrderInfoDTO mergeOrderInfoDTO = effectiveOrderItemMap.get(itemId);

            // 构造下单数据
            DeliveryOrderItemInfoDTO orderItem = new DeliveryOrderItemInfoDTO();
            orderItem.setItemId(itemId);
            orderItem.setSkuId(mergeOrderInfoDTO.getSkuId());
            orderItem.setSupplierSkuId(mergeOrderInfoDTO.getSupplierSkuId());
            // 减去售后的金额和数量
            orderItem.setItemCount(mergeOrderInfoDTO.getItemTotalCount());
            // 当天这个商品已全部退款
            if (0 == orderItem.getItemCount()) {
                continue;
            }
            orderItem.setItemTotalPrice(mergeOrderInfoDTO.getItemTotalPrice());
            orderItem.setTotalWeight(mergeOrderInfoDTO.getTotalWeight());

            orderTotalPrice = orderTotalPrice.add(orderItem.getItemTotalPrice());
            orderTotalCount = orderTotalCount + orderItem.getItemCount();

            deliveryOrderItemList.add(orderItem);

        }


        // 今日已付运费 = 当日有效订单的运费 - 当日有效订单所关联售后单的已退运费
        BigDecimal paidDeliveryFee = effectOrderList.stream().map(Order::getDeliveryFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        paidDeliveryFee = paidDeliveryFee.subtract(effectiveAfterSaleList.stream()
                .map(OrderAfterSale::getDeliveryFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        return DeliveryTotalDTO.builder()
                .orderInfoDTO(DeliveryOrderInfoDTO.builder()
                        .orderTotalCount(orderTotalCount)
                        .orderTotalPrice(orderTotalPrice)
                        .build())
                .orderItemInfoDTOList(deliveryOrderItemList)
                .relatedOrderInfoDTO(DeliveryRelatedOrderInfoDTO.builder()
                        .afterSaleIds(effectiveAfterSaleList.stream().map(OrderAfterSale::getId).collect(Collectors.toList()))
                        .paidDeliveryFee(paidDeliveryFee)
                        .build())
                .build();
    }
}
