package com.cosfo.ordercenter.application.service.delivery.impl;

import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.MerchantDeliveryRuleConverter;
import com.cosfo.ordercenter.application.service.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryStepFeeDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.domain.delivery.param.MerchantDeliveryRuleQueryParam;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryStepFeeParam;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryWarehouseParam;
import com.cosfo.ordercenter.infrastructure.converter.delivery.MerchantDeliveryStepFeeConverter;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryRuleWarehouseRelationDao;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryStepFeeDao;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryFeeRule;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryStepFee;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 11:43
 * @Description:
 */
@Service
public class DeliveryFeeServiceImpl implements DeliveryFeeService {
    @Resource
    private MerchantDeliveryFeeRuleDao merchantDeliveryFeeRuleDao;
    @Resource
    private MerchantDeliveryStepFeeDao merchantDeliveryStepFeeDao;
    @Resource
    private MerchantDeliveryRuleWarehouseRelationDao merchantDeliveryRuleWarehouseRelationDao;

    @Override
    public List<MerchantDeliveryRuleInfoDTO> queryAllDeliveryRule(MerchantDeliveryRuleQueryReq queryReq) {
        // 规则主表
        MerchantDeliveryRuleQueryParam merchantDeliveryRuleQueryParam = MerchantDeliveryRuleConverter.converterQueryParam(queryReq);
        List<MerchantDeliveryFeeRule> deliveryFeeRules = merchantDeliveryFeeRuleDao.listByParam(merchantDeliveryRuleQueryParam);

        if (CollectionUtils.isEmpty(deliveryFeeRules)) {
            return Collections.emptyList();
        }

        // 三方仓时，每日运费可有多条，随仓报价只能一条
        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(queryReq.getWarehouseType())) {
            chekThreeRuleValid(deliveryFeeRules);
        }

        List<Long> ruleIds = deliveryFeeRules.stream().map(MerchantDeliveryFeeRule::getId).collect(Collectors.toList());
        // 阶梯运费
        List<MerchantDeliveryStepFee> deliveryStepDescFees = merchantDeliveryStepFeeDao.listStepDesc(QueryDeliveryStepFeeParam.builder()
                .tenantId(queryReq.getTenantId())
                .ruleIds(ruleIds)
                .build());
        // 三方仓且随仓报价时，没有阶梯运费
        if (!MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE.getCode().equals(deliveryFeeRules.get(0).getRuleType())
                && CollectionUtils.isEmpty(deliveryStepDescFees)) {
            return Collections.emptyList();
        }
        Map<Long, List<MerchantDeliveryStepFee>> stepFeeDescMap = deliveryStepDescFees.stream().collect(Collectors.groupingBy(MerchantDeliveryStepFee::getRuleId));


        Set<Long> warehouseRuleIds = new HashSet<>();
        if (WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(queryReq.getWarehouseType())) {
            // 命中仓库
            warehouseRuleIds = merchantDeliveryRuleWarehouseRelationDao.listRuleIds(QueryDeliveryWarehouseParam.builder()
                    .tenantId(queryReq.getTenantId())
                    .ruleIds(ruleIds)
                    .warehouseNoList(Collections.singletonList(queryReq.getWarehouseNo()))
                    .build());
        }

        Set<Long> finalWarehouseRuleIds = warehouseRuleIds;
        return deliveryFeeRules.stream()
                .map(rule -> {
                    return buildDeliveryDTO(queryReq.getWarehouseNo(),
                            stepFeeDescMap.get(rule.getId()),
                            finalWarehouseRuleIds,
                            rule);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建运费对象
     *
     * @param warehouseNo           当前订单的仓库号
     * @param stepFeeDescList       当前规则的阶梯
     * @param finalWarehouseRuleIds 规则命中的仓库
     * @param rule                  当前规则
     * @return
     */
    private MerchantDeliveryRuleInfoDTO buildDeliveryDTO(Long warehouseNo,
                                                         List<MerchantDeliveryStepFee> stepFeeDescList,
                                                         Set<Long> finalWarehouseRuleIds,
                                                         MerchantDeliveryFeeRule rule) {
        MerchantDeliveryRuleInfoDTO ruleInfoDTO = MerchantDeliveryRuleConverter.converterToDTO(rule);
        // 阶梯运费
        Optional.ofNullable(stepFeeDescList).ifPresent(stepFees -> {
            ruleInfoDTO.setStepFeeDescList(stepFees.stream()
                    .filter(s -> rule.getFreeDeliveryType().equals(s.getFeeRule()))
                    .map(s -> DeliveryStepFeeDTO.builder()
                            .stepThreshold(s.getStepThreshold())
                            .deliveryFee(s.getDeliveryFee())
                            .ruleId(s.getRuleId())
                            .feeRule(s.getFeeRule())
                            .calType(s.getCalType())
                            .deliveryfeeCalRule(MerchantDeliveryStepFeeConverter.toDeliveryFeeCalRuleDTO(s.getDeliveryfeeCalRule()))
                            .build())
                    .collect(Collectors.toList()));
        });
        if (CollectionUtils.isEmpty(ruleInfoDTO.getStepFeeDescList())) {
            throw new BizException("未配置阶梯运费！");
        }

        // 命中仓库
        if (finalWarehouseRuleIds.contains(rule.getId())) {
            ruleInfoDTO.setWarehouseNo(warehouseNo);
        }

        return ruleInfoDTO;
    }

    /**
     * 校验三方仓规则是否配置正确
     *
     * @param deliveryFeeRules
     */
    private void chekThreeRuleValid(List<MerchantDeliveryFeeRule> deliveryFeeRules) {
        MerchantDeliveryFeeRule deliveryFeeRule = deliveryFeeRules.stream()
                .filter(i -> MerchantDeliveryFeeRuleEnum.MerchantAddressDefaultTypeEnum.DEFAULT.getCode().equals(i.getDefaultType()))
                .findFirst()
                .orElse(new MerchantDeliveryFeeRule());
        if (Objects.isNull(deliveryFeeRule.getId())) {
            throw new BizException("三方仓的运费规则配置错误，没有默认规则！");
        }
        if (MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.FOLLOW_WAREHOUSE.getCode().equals(deliveryFeeRule.getRuleType())
                && 1 != deliveryFeeRules.size()) {
            throw new BizException("三方仓的运费规则配置错误，有多条规则！");
        }
    }
}
