package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.resp.order.OrderResp;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CombineOrderQueryService {


    /**
     * 查询组合包订单
     * @param combineItemId
     * @param tenantId
     * @return
     */
    List<OrderResp> queryByCombineId(Long combineItemId, Long tenantId);

    /**
     * 查询组合包订单
     * @param combineItemId
     * @param tenantId
     * @return
     */
    List<OrderResp> queryByCombineIds(Collection<Long> combineItemId, Long tenantId);
}
