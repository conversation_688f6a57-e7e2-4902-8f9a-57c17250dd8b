package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAddressQueryService {


    /**
     * 查询订单地址
     *
     * @param orderId
     * @param tenantId
     * @return
     */
    OrderAddressResp queryByOrderId(Long tenantId, Long orderId);

    /**
     * 批量查询订单地址
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderAddressResp> queryByOrderIds(Long tenantId, List<Long> orderIds);
}
