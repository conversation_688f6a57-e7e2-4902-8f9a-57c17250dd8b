package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.*;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.domain.order.param.command.OrderCommandParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
public interface OrderCommandService {

    /**
     * 根据订单id更新
     *
     * @param orderCommandParam
     * @return
     */
    Boolean updateById(OrderCommandParam orderCommandParam);

    /**
     * 根据订单id更新支付方式
     * @param orderCommandParam
     * @return
     */
    Boolean updatePayType(OrderCommandParam orderCommandParam);

    /**
     * 根据订单id更新订单状态
     * todo 了关锁
     *
     * @param orderStatusUpdateReq
     * @return
     */
    Boolean updateStatus(OrderStatusUpdateReq orderStatusUpdateReq);

    /**
     * 批量更新订单状态
     * todo 更新状态
     *
     * @param orderStatusBatchUpdateReq
     * @return
     */
    Integer batchUpdateStatus(OrderStatusBatchUpdateReq orderStatusBatchUpdateReq);


    /**
     * 订单自提
     * <p>更新订单状态，更新订单明细售后截止时间</p>
     *
     * @param orderSelfLiftReq
     * @return
     */
    Boolean selfLifting(OrderSelfLiftReq orderSelfLiftReq);

    /**
     * 更新订单分账完成时间
     *
     * @param req
     * @return
     */
    Boolean batchUpdateProfitSharingFinishTime(ProfitSharingFinishTimeReq req);


    /**
     * 关闭订单
     *
     * @param req
     * @return
     */
    Boolean close(OrderCloseReq req);

    /**
     * 审核通过订单
     * @param req
     * @return
     */
    Boolean auditSuccess(OrderAuditReq req);

    /**
     * 创建订单
     *
     * @param req
     * @return
     */
    Long create(OrderCreateReq req);

    /**
     * 取消订单
     *
     * @param req
     * @return
     */
    Boolean cancel(OrderCancelReq req);

    /**
     * 库存锁定成功
     *
     * @param req
     * @return
     */
    Boolean lockStockSuccess(LockStockSuccessReq req);

    /**
     * 确认收货
     *
     * @return
     */
    Boolean confirm(OrderConfirmReq req);


    /**
     * 履约单创建成功
     *
     * @param req
     * @return
     */
    Boolean fulfillmentOrderCreate(OrderFulfillmentOrderCreateReq req);


    /**
     * 订单完成
     *
     * @return
     */
    Boolean finish();

    /**
     * 支付成功
     *
     * @return
     */
    Boolean paySuccess(OrderPaySuccessReq req);


    /**
     * 订单配送时间更新，由于各种因素导致配送时间变更同步SaaS使用
     *
     * @param req
     * @return
     */
    Integer updateOrderDeliveryTime(OrderUpdateDelivertDateReq req);

    /**
     * 预售订单设置配送日期，同时更新订单状态为10-待出库 由3-已支付
     * @param req
     * @return
     */
    Boolean setDeliveryDatePresaleOrder(@Valid OrderPresaleSetDeliveryDateReq req);

    /**
     * 自提完成
     *
     * @param req
     * @return
     */
    Boolean selfLiftingFinish(OrderSelfLiftingFinishReq req);


    /**
     * 城配仓切仓，更新订单的城配仓号
     *
     * @param req
     * @return
     */
    Boolean updateOrderStoreNo(OrderUpdateStoreNoReq req);

    /**
     * 更新订单金额
     *
     * @param req
     * @return
     */
    OrderResp refreshOrderAmount(RefreshOrderAmountReq req);
}
