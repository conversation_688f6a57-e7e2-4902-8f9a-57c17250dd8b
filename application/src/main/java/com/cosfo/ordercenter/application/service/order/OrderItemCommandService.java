package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;

/**
 * <AUTHOR>
 */
public interface OrderItemCommandService {

    /**
     * 更新订单明细售后过期时间
     *
     * @param dto
     * @return
     */
    Boolean updateAfterSaleExpiryTime(OrderItemUpdateReq dto);

    /**
     * 更新订单明细配送数量
     * <p>当配送数量=待配送数量时，更新订单状态</p>
     * @param dto
     * @return
     */
    Boolean updateDeliveryQuantity(OrderItemUpdateDeliveryQuantityReq dto);

    /**
     * 更新订单项退款信息
     * @param req
     * @return
     */
    Boolean updateStatus(OrderItemStatusUpdateReq req);
}
