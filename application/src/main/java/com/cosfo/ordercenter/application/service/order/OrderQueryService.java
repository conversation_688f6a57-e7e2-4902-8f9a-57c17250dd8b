package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.req.OrderAggQueryReq;
import com.cosfo.ordercenter.client.req.OrderOutQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderAggResp;
import com.cosfo.ordercenter.client.resp.order.OrderOutResp;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderQueryService {

    List<OrderOutResp> queryOrderInfo(OrderOutQueryReq orderOutQueryReq);

    List<OrderAggResp> queryOrderAggByNos(OrderAggQueryReq orderAggQueryReq);
}
