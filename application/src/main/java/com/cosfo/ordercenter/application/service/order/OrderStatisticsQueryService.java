package com.cosfo.ordercenter.application.service.order;

import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.*;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import net.xianmu.common.result.DubboResponse;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderStatisticsQueryService {

    OrderSummaryResp queryOrderSummary(OrderSummaryReq orderSummaryReq);


    /**
     * 查询待配送数量
     * @param tenantId
     * @return
     */
    Integer getWaitDeliveryQuantity(Long tenantId);

    /**
     * 查询待审核数量
     * @param tenantId
     * @return
     */
    Integer getWaitAuditQuantity(Long tenantId);


    /**
     * 查询sku维度销量
     * @param orderSkuSaleReq
     * @return
     */
    List<OrderSkuQuantityResp> querySkuSaleQuantity(OrderSkuSaleReq orderSkuSaleReq);

    /**
     * 查询sku，仓库维度销量
     * @param orderSkuSaleReq
     * @return
     */
    List<OrderSkuQuantityResp> querySkuSaleWithStoreNoQuantity(OrderSkuSaleReq orderSkuSaleReq);


    /**
     * 查询sku、城市维度销量
     * @param orderSkuSaleReq
     * @return
     */
    List<OrderSkuQuantityResp> querySkuSaleWithCityQuantity(OrderSkuSaleReq orderSkuSaleReq);


    /**
     * 查询订单明细列表
     * 包含orderItem和快照信息
     * @param orderDetailReq
     * @return
     */
    List<OrderDetailResp> queryOrderDetail(OrderDetailReq orderDetailReq);

    /**
     * 获取sku销售信息
     * @param orderIds
     * @param tenantId
     * @return
     */
    OrderItemSaleResp querySkuSaleQuantity(List<Long> orderIds, Long tenantId);


    /**
     * 按条件统计订单数量
     * @param orderCountReq
     * @return
     */
    Integer countOrderQuantity(OrderCountReq orderCountReq);


    /**
     * 统计时间范围内门店 item 购买数量
     * @param itemSaleQuantityReq
     * @return
     */
    Map<Long, Integer> countItemSaleQuantity(ItemSaleQuantityReq itemSaleQuantityReq);

    /**
     * 查询供应商订单统计信息
     * @param supplierOrderTotalReq
     * @return
     */
    List<SupplierOrderTotalResp> querySupplierOrderSummary(SupplierOrderTotalReq supplierOrderTotalReq);
}
