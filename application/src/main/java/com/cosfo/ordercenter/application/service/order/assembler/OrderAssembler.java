package com.cosfo.ordercenter.application.service.order.assembler;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderAddressConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemOutResp;
import com.cosfo.ordercenter.client.resp.order.OrderOutResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.order.entity.*;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderAssembler {
    private OrderAssembler() {
    }

    public static List<OrderSkuQuantityResp> assembleOrderSkuQuantityResp(List<OrderSkuQuantityEntity> entityList) {

        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderSkuQuantityResp> orderSkuQuantityRespList = new ArrayList<>();
        for (OrderSkuQuantityEntity orderSkuQuantityEntity : entityList) {
            orderSkuQuantityRespList.add(toOrderSkuQuantityResp(orderSkuQuantityEntity));
        }
        return orderSkuQuantityRespList;
    }

    public static OrderSkuQuantityResp toOrderSkuQuantityResp(OrderSkuQuantityEntity orderSkuQuantityEntity) {
        if (orderSkuQuantityEntity == null) {
            return null;
        }
        OrderSkuQuantityResp orderSkuQuantityResp = new OrderSkuQuantityResp();
        orderSkuQuantityResp.setSkuId(orderSkuQuantityEntity.getSkuId());
        orderSkuQuantityResp.setSaleQuantity(orderSkuQuantityEntity.getSaleQuantity());
        orderSkuQuantityResp.setWarehouseId(orderSkuQuantityEntity.getWarehouseId());
        orderSkuQuantityResp.setCity(orderSkuQuantityEntity.getCity());
        return orderSkuQuantityResp;
    }

    public static List<OrderDetailResp> assembleOrderDetailResp(List<OrderDetailEntity> entityList) {


        if (entityList == null) {
            return Collections.emptyList();
        }
        List<OrderDetailResp> orderDetailRespList = new ArrayList<>();
        for (OrderDetailEntity orderDetailEntity : entityList) {
            orderDetailRespList.add(toOrderDetailResp(orderDetailEntity));
        }
        return orderDetailRespList;
    }

    public static OrderDetailResp toOrderDetailResp(OrderDetailEntity orderDetailEntity) {
        if (orderDetailEntity == null) {
            return null;
        }
        OrderDetailResp orderDetailResp = new OrderDetailResp();
        orderDetailResp.setOrderId(orderDetailEntity.getOrderId());
        orderDetailResp.setOrderNo(orderDetailEntity.getOrderNo());
        orderDetailResp.setStoreId(orderDetailEntity.getStoreId());
        orderDetailResp.setCreateTime(orderDetailEntity.getCreateTime());
        orderDetailResp.setPayTime(orderDetailEntity.getPayTime());
        orderDetailResp.setPayType(orderDetailEntity.getPayType());
        orderDetailResp.setDeliveryTime(orderDetailEntity.getDeliveryTime());
        orderDetailResp.setFinishedTime(orderDetailEntity.getFinishedTime());
        orderDetailResp.setTitle(orderDetailEntity.getTitle());
        orderDetailResp.setItemId(orderDetailEntity.getItemId());
        orderDetailResp.setSkuId(orderDetailEntity.getSkuId());
        orderDetailResp.setSpecification(orderDetailEntity.getSpecification());
        orderDetailResp.setAmount(orderDetailEntity.getAmount());
        orderDetailResp.setSkuPrice(orderDetailEntity.getSkuPrice());
        orderDetailResp.setDeliveryFee(orderDetailEntity.getDeliveryFee());
        orderDetailResp.setOrderPrice(orderDetailEntity.getOrderPrice());
        orderDetailResp.setOrderItemId(orderDetailEntity.getOrderItemId());
        orderDetailResp.setSupplyTenantId(orderDetailEntity.getSupplyTenantId());
        orderDetailResp.setSupplySkuId(orderDetailEntity.getSupplySkuId());
        orderDetailResp.setMainPicture(orderDetailEntity.getMainPicture());
        orderDetailResp.setSupplyPrice(orderDetailEntity.getSupplyPrice());
        orderDetailResp.setWarehouseType(orderDetailEntity.getWarehouseType());
        orderDetailResp.setPricingType(orderDetailEntity.getPricingType());
        orderDetailResp.setPricingNumber(orderDetailEntity.getPricingNumber());
        orderDetailResp.setGoodsType(orderDetailEntity.getGoodsType());
// Not mapped FROM fields:
// skuTotalPrice
        return orderDetailResp;
    }

    public static List<OrderOutResp> assembleOrderOutResp(List<OrderEntity> orders,
                                                          Map<Long, List<OrderItemEntity>> orderItemListMap,
                                                          Map<Long, OrderItemSnapshotEntity> itemSnapshotMap,
                                                          Map<Long, List<OrderAfterSaleEntity>> orderAfterSaleDTOListMap,
                                                          Map<Long, OrderAddressEntity> orderAddressMap) {

        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<OrderOutResp> orderOutDTOS = orders.stream().map(order -> {
            OrderOutResp orderOutDTO = OrderConverter.convertToOrderOutResp(order);
            OrderAddressEntity orderAddress = orderAddressMap.get(order.getId());
            orderOutDTO.setOrderAddressResp(OrderAddressConverter.converterToResp(orderAddress));
            List<OrderItemEntity> orderItems = orderItemListMap.get(order.getId());
            if (CollectionUtils.isEmpty(orderItems)) {
                return orderOutDTO;
            }
            List<OrderItemOutResp> orderItemOutDTOS = orderItems.stream().map(orderItem -> {
                OrderItemOutResp orderItemOutDTO = OrderItemAssembler.toOrderItemOutResp(orderItem, itemSnapshotMap.get(orderItem.getId()));
                Integer needSendAmount = orderItem.getAmount();
                if (orderAfterSaleDTOListMap.containsKey(orderItem.getId())) {
                    // 发货数量 - 配送前售后数量
                    List<OrderAfterSaleEntity> orderAfterSales = orderAfterSaleDTOListMap.get(orderItem.getId());
                    Integer afterSaleAmount = orderAfterSales.stream().filter(orderAfterSale -> OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(orderAfterSale.getAfterSaleType())).map(OrderAfterSaleEntity::getAmount).reduce(Integer::sum).orElse(0);
                    needSendAmount = orderItem.getAmount() - afterSaleAmount;
                }

                orderItemOutDTO.setNeedSendAmount(needSendAmount);
                return orderItemOutDTO;
            }).collect(Collectors.toList());
            orderOutDTO.setOrderItemOutDTOS(orderItemOutDTOS);
            return orderOutDTO;
        }).collect(Collectors.toList());
        return orderOutDTOS;
    }
}
