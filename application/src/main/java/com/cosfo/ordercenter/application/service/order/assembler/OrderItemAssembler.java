package com.cosfo.ordercenter.application.service.order.assembler;

import com.cosfo.ordercenter.client.resp.order.OrderItemOutResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;

/**
 * <AUTHOR>
 */
public class OrderItemAssembler {
    private OrderItemAssembler() {
    }

    public static OrderItemOutResp toOrderItemOutResp(OrderItemEntity orderItemEntity, OrderItemSnapshotEntity orderItemSnapshotEntity) {

        if (orderItemEntity == null) {
            return null;
        }
        OrderItemOutResp orderItemOutResp = new OrderItemOutResp();
        orderItemOutResp.setId(orderItemEntity.getId());
        orderItemOutResp.setOrderId(orderItemEntity.getOrderId());
        orderItemOutResp.setItemId(orderItemEntity.getItemId());
        orderItemOutResp.setAmount(orderItemEntity.getAmount());
        orderItemOutResp.setTotalPrice(orderItemEntity.getTotalPrice());
        orderItemOutResp.setAfterSaleExpiryTime(orderItemEntity.getAfterSaleExpiryTime());
        orderItemOutResp.setOrderType(orderItemEntity.getOrderType());
        orderItemOutResp.setTenantId(orderItemEntity.getTenantId());
// Not mapped TO fields:
// skuId
// supplierSkuId
// areaItemId
// price
// supplyPrice
// supplierTenantId
// supplierName
// title
// mainPicture
// specification
// specificationUnit
// warehouseType
// deliveryType
// isEnableApplyAfterSale
// afterSaleUnit
// maxAfterSaleAmount
// pricingType
// pricingNumber
// needSendAmount
// goodsType
// supplySku
// afterSaleFlag
// supplierId
// itemType
// combineItemId
// itemSaleMode
// Not mapped FROM fields:
// payablePrice
// storeNo
// status
// createTime
// updateTime
// deliveryQuantity

        if (orderItemSnapshotEntity != null) {
            orderItemOutResp.setDeliveryType(orderItemSnapshotEntity.getDeliveryType());
            orderItemOutResp.setSupplierTenantId(orderItemSnapshotEntity.getSupplierTenantId());
            orderItemOutResp.setWarehouseType(orderItemSnapshotEntity.getWarehouseType());
            orderItemOutResp.setSupplierSkuId(orderItemSnapshotEntity.getSupplierSkuId());
            orderItemOutResp.setSupplierName(orderItemSnapshotEntity.getSupplierName());
            orderItemOutResp.setTitle(orderItemSnapshotEntity.getTitle());
            orderItemOutResp.setMainPicture(orderItemSnapshotEntity.getMainPicture());
            orderItemOutResp.setSpecification(orderItemSnapshotEntity.getSpecification());
            orderItemOutResp.setSpecificationUnit(orderItemSnapshotEntity.getSpecificationUnit());
            orderItemOutResp.setGoodsType(orderItemSnapshotEntity.getGoodsType());
            orderItemOutResp.setAfterSaleUnit(orderItemSnapshotEntity.getAfterSaleUnit());
            orderItemOutResp.setSupplyPrice(orderItemSnapshotEntity.getSupplyPrice());
        }
        return orderItemOutResp;
    }

}
