package com.cosfo.ordercenter.application.service.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class RefundDTO implements Serializable {

    /**
     * 原订单编号
     */
    private Long orderId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 售后订单号
     */
    private Long afterSaleId;

    /**
     * 售后金额
     */
    private BigDecimal refundPrice;
}
