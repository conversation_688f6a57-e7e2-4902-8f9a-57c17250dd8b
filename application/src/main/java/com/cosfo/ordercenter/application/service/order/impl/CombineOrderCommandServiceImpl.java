package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.service.order.CombineOrderCommandService;
import com.cosfo.ordercenter.domain.order.service.CombineOrderCommandDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class CombineOrderCommandServiceImpl implements CombineOrderCommandService {

    @Resource
    private CombineOrderCommandDomainService combineOrderCommandDomainService;

    @Override
    public Long add(Long combineItemId, Long tenantId) {
        return combineOrderCommandDomainService.add(combineItemId, tenantId);
    }
}
