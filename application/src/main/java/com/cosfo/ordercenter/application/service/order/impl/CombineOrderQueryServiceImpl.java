package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.application.service.order.CombineOrderQueryService;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CombineOrderQueryServiceImpl implements CombineOrderQueryService {

    @Resource
    private OrderQueryRepository orderQueryRepository;

    @Override
    public List<OrderResp> queryByCombineId(Long combineItemId, Long tenantId) {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByCombineId(combineItemId, tenantId);
        return OrderConverter.convertToOrderRespList(orderEntities);
    }

    @Override
    public List<OrderResp> queryByCombineIds(Collection<Long> combineItemId, Long tenantId) {
        return OrderConverter.convertToOrderRespList(orderQueryRepository.queryByCombineIds(Sets.newHashSet(combineItemId), tenantId));
    }
}
