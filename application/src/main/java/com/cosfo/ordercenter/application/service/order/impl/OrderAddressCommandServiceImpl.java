package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.service.order.OrderAddressCommandService;
import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;
import com.cosfo.ordercenter.domain.order.service.OrderAddressCommandDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class OrderAddressCommandServiceImpl implements OrderAddressCommandService {


    @Resource
    private OrderAddressCommandDomainService orderAddressCommandDomainService;

    @Override
    public Long add(OrderAddressCommandParam addressCommandParam) {
        return orderAddressCommandDomainService.add(addressCommandParam);
    }
}
