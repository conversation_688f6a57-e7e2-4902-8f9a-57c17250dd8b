package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderAddressConverter;
import com.cosfo.ordercenter.application.service.order.OrderAddressQueryService;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressQueryRepository;
import com.google.common.collect.Lists;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OrderAddressQueryServiceImpl implements OrderAddressQueryService {

    @Resource
    private OrderAddressQueryRepository orderAddressQueryRepository;

    @Override
    public OrderAddressResp queryByOrderId(Long tenantId, Long orderId) {
        return OrderAddressConverter.converterToResp(orderAddressQueryRepository.getByOrderId(orderId, tenantId));
    }

    @Override
    public List<OrderAddressResp> queryByOrderIds(Long tenantId, List<Long> orderIds) {
        return OrderAddressConverter.convertToRespList(orderAddressQueryRepository.queryByOrderIds(orderIds, tenantId));
    }
}
