package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderItemConverter;
import com.cosfo.ordercenter.application.service.order.OrderItemCommandService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.service.OrderCommandDomainService;
import com.cosfo.ordercenter.domain.order.service.OrderItemCommandDomainService;
import com.google.common.collect.Lists;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderItemCommandServiceImpl implements OrderItemCommandService {

    public static final List<Integer> NOT_SENT_FINISHED_AFTER_SALE_STATUS_LIST = Lists.newArrayList(OrderAfterSaleStatusEnum.REFUNDING.getValue(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());


    @Resource
    private OrderItemCommandDomainService orderItemCommandDomainService;
    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderCommandDomainService orderCommandDomainService;

    @Override
    public Boolean updateAfterSaleExpiryTime(OrderItemUpdateReq dto) {
        OrderItemUpdateParam orderItemUpdateParam = OrderItemConverter.convertToParam(dto);
        return orderItemCommandDomainService.updateAfterSaleExpiryTime(orderItemUpdateParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDeliveryQuantity(OrderItemUpdateDeliveryQuantityReq dto) {
        if (CollectionUtils.isEmpty(dto.getOrderItemQuantities())) {
            throw new BizException("配送明细不能为空");
        }
        Set<Long> hasRefundOrderItemIdSet = Collections.emptySet();

        OrderAfterSaleListQueryParam req = new OrderAfterSaleListQueryParam();
        req.setStatusList(NOT_SENT_FINISHED_AFTER_SALE_STATUS_LIST);
        req.setOrderIds(Collections.singletonList(dto.getOrderId()));
        req.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
        List<OrderAfterSaleEntity> orderAfterSaleList = orderAfterSaleQueryRepository.queryList(req);
        if (!CollectionUtils.isEmpty(orderAfterSaleList)) {
            hasRefundOrderItemIdSet = orderAfterSaleList.stream().map(OrderAfterSaleEntity::getOrderItemId).collect(Collectors.toSet());
        }

        Set<Long> finalHasRefundOrderItemIdSet = hasRefundOrderItemIdSet;
        // 更新订单明细已配送数量
        for (OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity itemQuantity : dto.getOrderItemQuantities()) {
            orderItemCommandDomainService.updateDeliveryQuantity(itemQuantity.getOrderItemId(), itemQuantity.getQuantity());
        }
        //判断是否配送完成
        List<OrderItemEntity> list = orderItemQueryRepository.queryByOrderId(dto.getOrderId());
        Optional<OrderItemEntity> any = list.stream().filter(orderItem -> !finalHasRefundOrderItemIdSet.contains(orderItem.getId()) && orderItem.getAmount() > orderItem.getDeliveryQuantity())
                .findAny();
        boolean isUnFinish = any.isPresent();
        //更新订单状态
        OrderStatusUpdateParam updateReq = new OrderStatusUpdateParam();
        updateReq.setOrderId(dto.getOrderId());
        updateReq.setStatus(isUnFinish ? OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode() : OrderStatusEnum.DELIVERING.getCode());
        return orderCommandDomainService.updateStatus(updateReq);

    }

    @Override
    public Boolean updateStatus(OrderItemStatusUpdateReq req) {
        OrderItemStatusUpdateParam orderItemStatusUpdateParam = OrderItemConverter.convertToStatusUpdateParam(req);
        return orderItemCommandDomainService.updateStatus(orderItemStatusUpdateParam);
    }
}
