package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderItemExtraConverter;
import com.cosfo.ordercenter.application.service.order.OrderItemExtraQueryService;
import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderItemExtraResp;
import com.cosfo.ordercenter.domain.order.entity.OrderItemExtraEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemExtraQueryRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OrderItemExtraQueryServiceImpl implements OrderItemExtraQueryService {

    @Resource
    private OrderItemExtraQueryRepository orderItemExtraQueryRepository;

    @Override
    public List<OrderItemExtraResp> queryOrderItemExtraList(OrderItemExtraQueryReq orderItemExtraQueryReq) {
        OrderItemExtraQueryParam orderItemExtraQueryParam = OrderItemExtraConverter.convertToQueryParam(orderItemExtraQueryReq);
        List<OrderItemExtraEntity> orderItemExtraEntities = orderItemExtraQueryRepository.queryList(orderItemExtraQueryParam);
        return OrderItemExtraConverter.convertToExtraRespList(orderItemExtraEntities);
    }
}
