package com.cosfo.ordercenter.application.service.order.impl;

import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderAddressConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderItemConverter;
import com.cosfo.ordercenter.application.service.order.OrderQueryService;
import com.cosfo.ordercenter.application.service.order.assembler.OrderAssembler;
import com.cosfo.ordercenter.client.req.OrderAggQueryReq;
import com.cosfo.ordercenter.client.req.OrderOutQueryReq;
import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.ordercenter.client.resp.order.OrderAggResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.resp.order.OrderOutResp;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.facade.StoreQueryFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderQueryServiceImpl implements OrderQueryService {

    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderAddressQueryRepository orderAddressQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;
    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;

    @Resource
    private StoreQueryFacade storeQueryFacade;

    @Override
    public List<OrderOutResp> queryOrderInfo(OrderOutQueryReq orderOutQueryReq) {
        Long tenantId = orderOutQueryReq.getTenantId();
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        orderQueryParam.setTenantId(tenantId);
        orderQueryParam.setOrderNos(orderOutQueryReq.getOrderNos());
        List<OrderEntity> orders = orderQueryRepository.queryList(orderQueryParam);
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<Long> orderIds = orders.stream().map(OrderEntity::getId).collect(Collectors.toList());

        List<OrderAddressEntity> orderAddresses = orderAddressQueryRepository.queryByOrderIds(orderIds, tenantId);
        Map<Long, OrderAddressEntity> orderAddressMap = orderAddresses.stream().collect(Collectors.toMap(OrderAddressEntity::getOrderId, item -> item));


        List<OrderItemEntity> orderItems = orderItemQueryRepository.batchQueryByOrderIds(orderIds);
        Map<Long, List<OrderItemEntity>> orderItemListMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemEntity::getOrderId));
        List<Long> orderItemIds = orderItems.stream().map(OrderItemEntity::getId).collect(Collectors.toList());
        Map<Long, OrderItemSnapshotEntity> itemSnapshotMap = orderItemSnapshotQueryRepository.queryOrderItemSnapshot(orderItemIds);

        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setOrderItemIds(orderItemIds);
        List<OrderAfterSaleEntity> orderAfterSales = orderAfterSaleQueryRepository.queryListByCondition(orderAfterSaleQueryParam);
        Map<Long, List<OrderAfterSaleEntity>> orderAfterSaleDTOListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderAfterSales)) {
            orderAfterSaleDTOListMap = orderAfterSales.stream()
                    .collect(Collectors.groupingBy(OrderAfterSaleEntity::getOrderItemId));
        }

        return OrderAssembler.assembleOrderOutResp(orders, orderItemListMap, itemSnapshotMap, orderAfterSaleDTOListMap, orderAddressMap);
    }

    @Override
    public List<OrderAggResp> queryOrderAggByNos(OrderAggQueryReq orderAggQueryReq) {
        Long tenantId = orderAggQueryReq.getTenantId();
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        orderQueryParam.setTenantId(tenantId);
        orderQueryParam.setOrderNos(orderAggQueryReq.getOrderNos());
        List<OrderEntity> orders = orderQueryRepository.queryList(orderQueryParam);
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<Long> storeIds = orders.stream().map(OrderEntity::getStoreId).distinct().collect(Collectors.toList());
        Map<Long, MerchantStoreResultResp> storeResultRespMap = storeQueryFacade.queryStoreByIds(storeIds);

        List<Long> orderIds = orders.stream().map(OrderEntity::getId).collect(Collectors.toList());

        List<OrderAddressEntity> orderAddresses = orderAddressQueryRepository.queryByOrderIds(orderIds, tenantId);
        Map<Long, OrderAddressEntity> orderAddressMap = orderAddresses.stream().collect(Collectors.toMap(OrderAddressEntity::getOrderId, item -> item, (v1, v2) -> v1));

        List<OrderItemEntity> orderItems = orderItemQueryRepository.batchQueryByOrderIds(orderIds);
        Map<Long, List<OrderItemEntity>> orderId2OrderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItemEntity::getOrderId));

        List<OrderItemSnapshotEntity> snapshotList = orderItemSnapshotQueryRepository.queryByOrderIds(orderIds);
        Map<Long, OrderItemSnapshotEntity> snapshotMap = snapshotList.stream().collect(Collectors.toMap(OrderItemSnapshotEntity::getOrderItemId, snapshot -> snapshot, (v1, v2) -> v1));

        List<OrderAggResp> orderAggResps = orders.stream().map(o -> {
            OrderAggResp orderAggResp = OrderConverter.convertToOrderAggResp(o);

            List<OrderItemAndSnapshotResp> withSnapshotDTOList = OrderItemConverter.convertToItemAndSnapshotRespList(orderId2OrderItemMap.get(o.getId()), snapshotMap);
            orderAggResp.setOrderItemAndSnapshotRespList(withSnapshotDTOList);

            OrderAddressResp orderAddressResp = OrderAddressConverter.converterToResp(orderAddressMap.get(o.getId()));
            orderAggResp.setOrderAddressResp(orderAddressResp);

            String storeName = Optional.ofNullable(storeResultRespMap.get(o.getStoreId())).map(MerchantStoreResultResp::getStoreName).orElse(null);
            orderAggResp.setStoreName(storeName);

            return orderAggResp;
        }).collect(Collectors.toList());

        return orderAggResps;
    }


}
