package com.cosfo.ordercenter.application.service.order.impl;

import cn.hutool.core.util.NumberUtil;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderParamConverter;
import com.cosfo.ordercenter.application.service.order.OrderStatisticsQueryService;
import com.cosfo.ordercenter.application.service.order.assembler.OrderAssembler;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.resp.order.OrderDetailResp;
import com.cosfo.ordercenter.client.resp.order.OrderItemSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderSkuQuantityResp;
import com.cosfo.ordercenter.client.resp.order.OrderSummaryResp;
import com.cosfo.ordercenter.domain.order.entity.OrderDetailEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSaleQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderSkuQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.SupplierOrderEntity;
import com.cosfo.ordercenter.domain.order.param.query.ItemSaleQuantityQueryParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderCountQueryParam;
import com.cosfo.ordercenter.domain.order.param.query.SupplierOrderTotalQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderStatisticsQueryServiceImpl implements OrderStatisticsQueryService {

    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;

    @Override
    public OrderSummaryResp queryOrderSummary(OrderSummaryReq orderSummaryReq) {

        OrderSummaryResp summaryResp = new OrderSummaryResp();
        Integer orderQuantity = orderQueryRepository.countPayOrderQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);
        BigDecimal orderTotalPrice = orderQueryRepository.sumOrderTotalPrice(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);
        Integer orderStoreQuantity = orderQueryRepository.countPayOrderStoreQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);

        Integer directOrderQuantity = 0;
        Integer directOrderStoreQuantity = 0;
        BigDecimal directOrderTotalPrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderSummaryReq.getStoreIds())) {
            directOrderQuantity = orderQueryRepository.countPayOrderQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
            directOrderTotalPrice = orderQueryRepository.sumOrderTotalPrice(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
            directOrderStoreQuantity = orderQueryRepository.countPayOrderStoreQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
        }
        summaryResp.setPayOrderNum(orderQuantity);
        summaryResp.setDirectStorePayOrderNum(directOrderQuantity);
        summaryResp.setPayOrderTotalPrice(orderTotalPrice);
        summaryResp.setDirectStorePayOrderTotalPrice(directOrderTotalPrice);
        summaryResp.setPayOrderStoreNum(orderStoreQuantity);
        summaryResp.setDirectStorePayOrderStoreNum(directOrderStoreQuantity);
        return summaryResp;
    }

    @Override
    public Integer getWaitDeliveryQuantity(Long tenantId) {
        return orderQueryRepository.getWaitDeliveryNum(tenantId);
    }

    @Override
    public Integer getWaitAuditQuantity(Long tenantId) {
        return orderQueryRepository.countByStatusList(tenantId, Collections.singletonList(OrderStatusEnum.WAIT_AUDIT.getCode()));
    }

    @Override
    public List<OrderSkuQuantityResp> querySkuSaleQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantityEntity> orderSkuQuantities = orderQueryRepository.querySkuSaleQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return OrderAssembler.assembleOrderSkuQuantityResp(orderSkuQuantities);
    }

    @Override
    public List<OrderSkuQuantityResp> querySkuSaleWithStoreNoQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantityEntity> orderSkuQuantityEntities = orderQueryRepository.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return OrderAssembler.assembleOrderSkuQuantityResp(orderSkuQuantityEntities);
    }

    @Override
    public List<OrderSkuQuantityResp> querySkuSaleWithCityQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantityEntity> orderSkuQuantityEntities = orderQueryRepository.querySkuSaleWithCityQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return OrderAssembler.assembleOrderSkuQuantityResp(orderSkuQuantityEntities);
    }

    @Override
    public List<OrderDetailResp> queryOrderDetail(OrderDetailReq orderDetailReq) {
        List<OrderDetailEntity> orderDetails = orderQueryRepository.queryOrderDetail(orderDetailReq.getOrderIds(), orderDetailReq.getTenantId());
        return OrderAssembler.assembleOrderDetailResp(orderDetails);
    }

    @Override
    public OrderItemSaleResp querySkuSaleQuantity(List<Long> orderIds, Long tenantId) {
        OrderItemSaleResp orderItemSaleResp = new OrderItemSaleResp();
        Integer saleQuantity = orderItemQueryRepository.querySaleQuantity(tenantId, orderIds);
        Integer skuQuantity = orderItemQueryRepository.querySkuQuantity(tenantId, orderIds);
        orderItemSaleResp.setSaleQuantity(saleQuantity);
        orderItemSaleResp.setSkuQuantity(skuQuantity);
        return orderItemSaleResp;
    }

    @Override
    public Integer countOrderQuantity(OrderCountReq orderCountReq) {
        OrderCountQueryParam orderCountQueryParam = OrderParamConverter.converterToCountQueryParam(orderCountReq);
        return orderQueryRepository.countOrderQuantity(orderCountQueryParam);
    }

    @Override
    public Map<Long, Integer> countItemSaleQuantity(ItemSaleQuantityReq itemSaleQuantityReq) {
        ItemSaleQuantityQueryParam itemSaleQuantityQueryParam = OrderParamConverter.converterToSalQuantityQueryParam(itemSaleQuantityReq);
        List<OrderItemSaleQuantityEntity> orderItemSaleQuantities = orderQueryRepository.queryOrderItemSaleQuantity(itemSaleQuantityQueryParam);
        if (CollectionUtils.isEmpty(orderItemSaleQuantities)) {
            return Collections.emptyMap();
        }
        return orderItemSaleQuantities.stream().collect(Collectors.toMap(OrderItemSaleQuantityEntity::getItemId, OrderItemSaleQuantityEntity::getQuantity));
    }

    @Override
    public List<SupplierOrderTotalResp> querySupplierOrderSummary(SupplierOrderTotalReq supplierOrderTotalReq) {
        SupplierOrderTotalQueryParam supplierOrderTotalQueryParam = OrderParamConverter.convertToTotalQueryParam(supplierOrderTotalReq);
        List<SupplierOrderEntity> supplierOrderList = orderQueryRepository.querySupplierOrderList(supplierOrderTotalQueryParam);
        if (CollectionUtils.isEmpty(supplierOrderList)) {
            return Collections.emptyList();
        }

        Map<Long, List<SupplierOrderEntity>> supplierOrderMap = supplierOrderList.stream().collect(Collectors.groupingBy(SupplierOrderEntity::getSupplierId));

        return supplierOrderMap.entrySet().stream().map(entry -> {
            List<SupplierOrderEntity> supplierOrderDTOS = entry.getValue();
            // 供应商订单供应总金额
            BigDecimal supplierOrderTotalPrice = supplierOrderDTOS.stream()
                    .map(dto -> NumberUtil.mul(Optional.ofNullable(dto.getSupplyPrice()).orElse(BigDecimal.ZERO), dto.getAmount()))
                    .reduce(BigDecimal.ZERO, NumberUtil::add);
            SupplierOrderTotalResp supplierOrderTotalResp = new SupplierOrderTotalResp();
            supplierOrderTotalResp.setSupplierId(entry.getKey());
            supplierOrderTotalResp.setSupplierOrderTotalPrice(supplierOrderTotalPrice);
            // 订单项列表
            supplierOrderTotalResp.setOrderIds(supplierOrderDTOS.stream().map(SupplierOrderEntity::getOrderId).collect(Collectors.toSet()));
            return supplierOrderTotalResp;
        }).collect(Collectors.toList());
    }
}
