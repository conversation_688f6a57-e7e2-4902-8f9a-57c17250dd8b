package com.cosfo.ordercenter.application.service.stock;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;

/**
 * <AUTHOR>
 */
public interface StockService {

    /**
     * 释放库存
     * @return
     */
    boolean releaseStock(String orderNo, Long orderItemId, SaleStockChangeTypeEnum stockChangeTypeEnum, Integer amount, Long warehouseNo, Long storeId);


    /**
     * 锁定库存 针对三方仓订单售后
     * @return
     */
    boolean lockStockForAfterSale(OrderAfterSaleEntity orderAfterSale, Integer itemAmount);
}
