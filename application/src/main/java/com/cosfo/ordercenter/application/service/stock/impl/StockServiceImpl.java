package com.cosfo.ordercenter.application.service.stock.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.ordercenter.application.service.stock.StockService;
import com.cosfo.ordercenter.common.constants.Constant;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.facade.ProductsMappingQueryFacade;
import com.cosfo.ordercenter.facade.SaleInventoryCenterCommandFacade;
import com.cosfo.ordercenter.facade.StoreQueryFacade;
import com.cosfo.ordercenter.facade.dto.ProductsMappingDTO;
import com.cosfo.ordercenter.facade.input.ProductMappingQueryInput;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderReleaseResDTO;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockServiceImpl implements StockService {

    public static final Long XIANMU_TENANT_ID = 1L;

    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;
    @Resource
    private ProductsMappingQueryFacade productsMappingQueryFacade;
    @Resource
    private SaleInventoryCenterCommandFacade saleInventoryCenterCommandFacade;
    @Resource
    private OrderAddressQueryRepository orderAddressQueryRepository;
    @Resource
    private StoreQueryFacade storeService;

    @Override
    public boolean releaseStock(String orderNo, Long orderItemId, SaleStockChangeTypeEnum stockChangeTypeEnum, Integer amount, Long warehouseNo, Long storeId) {
        OrderItemSnapshotEntity itemSnapshot = orderItemSnapshotQueryRepository.queryByOrderItemId(orderItemId);
        ProductMappingQueryInput queryReq = new ProductMappingQueryInput();
        queryReq.setSkuIds(Collections.singletonList(itemSnapshot.getSkuId()));
        List<ProductsMappingDTO> skuMappings = productsMappingQueryFacade.selectMappingList(queryReq);
        OrderReleaseBySpecifySkuReqDTO inputDTO = new OrderReleaseBySpecifySkuReqDTO();
        inputDTO.setTenantId(itemSnapshot.getTenantId());
        inputDTO.setOrderNo(orderNo);
        inputDTO.setOperatorNo(orderNo);
        inputDTO.setIdempotentNo(orderNo);
        inputDTO.setOrderType(SaleStockChangeTypeEnum.MANUAL_CLOSED.getTypeName());
        inputDTO.setOperatorName(storeService.queryStoreName(storeId));


        OrderReleaseSkuDetailReqDTO releaseSkuDetailReqDTO = new OrderReleaseSkuDetailReqDTO();
        releaseSkuDetailReqDTO.setWarehouseNo(warehouseNo);
        releaseSkuDetailReqDTO.setSkuCode(skuMappings.get(0).getSku());
        releaseSkuDetailReqDTO.setReleaseQuantity(amount);

        inputDTO.setOrderReleaseSkuDetailReqDTOS(Lists.newArrayList(releaseSkuDetailReqDTO));
        OrderReleaseResDTO handle = saleInventoryCenterCommandFacade.orderReleaseBySpecifySku(inputDTO);
        return true;
    }

    @Override
    public boolean lockStockForAfterSale(OrderAfterSaleEntity orderAfterSale, Integer itemAmount) {
        OrderAddressEntity orderAddress = orderAddressQueryRepository.getByOrderId(orderAfterSale.getOrderId(), orderAfterSale.getTenantId());
        OrderItemSnapshotEntity orderItemSnapshot = orderItemSnapshotQueryRepository.queryByOrderItemId(orderAfterSale.getOrderItemId());

        OrderOccupyReqDTO orderOccupyReqDTO = new OrderOccupyReqDTO();
        orderOccupyReqDTO.setTenantId(orderAfterSale.getTenantId());
        orderOccupyReqDTO.setWarehouseTenantId(XIANMU_TENANT_ID);
        orderOccupyReqDTO.setOrderNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setOrderType(SaleStockChangeTypeEnum.SAAS_AFTER_SALE.getTypeName());
        orderOccupyReqDTO.setOperatorNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setIdempotentNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setProvince(orderAddress.getProvince());
        orderOccupyReqDTO.setCity(orderAddress.getCity());
        orderOccupyReqDTO.setArea(orderAddress.getArea());
        orderOccupyReqDTO.setAddress(orderAddress.getAddress());
        orderOccupyReqDTO.setPoi(orderAddress.getPoiNote());
        orderOccupyReqDTO.setOperatorName(storeService.queryStoreName(orderAfterSale.getStoreId()));

        // 商品明细
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = Lists.newArrayList();
        orderOccupyReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);

        ProductMappingQueryInput queryReq = new ProductMappingQueryInput();
        queryReq.setSkuIds(Collections.singletonList(orderItemSnapshot.getSkuId()));
        List<ProductsMappingDTO> skuMappings = productsMappingQueryFacade.selectMappingList(queryReq);;
        if (CollectionUtils.isEmpty(skuMappings) || StringUtils.isEmpty(skuMappings.get(0).getSku())) {
            throw new BizException("找不到SkuCode");
        }
        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
        orderOccupySkuDetailReqDTO.setSkuCode(skuMappings.get(0).getSku());
        orderOccupySkuDetailReqDTO.setOccupyQuantity(itemAmount);
        orderOccupySkuDetailReqDTO.setSkuTenantId(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemSnapshot.getGoodsType()) ? Constant.XIANMU_TENANT_ID : orderAfterSale.getTenantId());
        orderOccupySkuDetailReqDTO.setMarketItemPresaleSwitch(orderItemSnapshot.getPresaleSwitch());
        orderOccupySkuDetailReqDTOS.add(orderOccupySkuDetailReqDTO);
        saleInventoryCenterCommandFacade.orderOccupy(orderOccupyReqDTO);
        return true;
    }
}
