package com.cosfo.ordercenter.common.constants;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 */
public class Constant {

    /**
     * 售后订单业务编号
     */
    public static final String NORMAL_ORDER_AFTER_SALE_CODE = "AS";

    /**
     * 普通订单业务编号
     */
    public static final String NORMAL_ORDER_CODE = "OR";

    /**
     * openApi请求来源
     */
    public static final String OPEN_API_REQ_SOURCE = "openApi";

    /**
     * 生成订单号
     *
     * @param orderCode
     * @return
     */
    public static String createOrderNo(String orderCode) {
        return orderCode + IdUtil.getSnowflakeNextIdStr();
    }


    /**
     * 默认分批查询size
     */
    public static final Integer DEFAULT_SIZE = 500;

    public static final Long XIANMU_TENANT_ID = 1L;

}
