package com.cosfo.ordercenter.common.constants;


import com.cosfo.ordercenter.common.enums.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.common.enums.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.common.enums.OrderStatusEnum;

import java.util.Arrays;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 10:54
 * @Description:
 */
public class DeliveryConstant {

    /**
     * 查询同一配送日的订单状态
     * status :3, 4, 5, 10
     */
    public static final List<Integer> DAILY_DELIVERY_STATUS = Arrays.asList(OrderStatusEnum.WAIT_DELIVERY.getCode(),
        OrderStatusEnum.DELIVERING.getCode(),
        OrderStatusEnum.FINISHED.getCode(),
        OrderStatusEnum.WAITING_DELIVERY.getCode());

    /**
     * 发货前售后时，计算应退运费的有效订单状态
     * status:3, 4, 5, 7, 9, 10
     * 关闭订单时，也会走每日退运费，所以在计算有效状态时需要包含已关单
     */
    public static final List<Integer> DELIVERY_EFFECTIVE_STATUS = Arrays.asList(OrderStatusEnum.WAIT_DELIVERY.getCode(),
        OrderStatusEnum.DELIVERING.getCode(),
        OrderStatusEnum.FINISHED.getCode(),
        OrderStatusEnum.REFUNDED.getCode(),
        OrderStatusEnum.CLOSED.getCode(),
        OrderStatusEnum.WAITING_DELIVERY.getCode());

    /**
     * 参与计算的售后服务类型
     * 1-退款  2-退款录入账单 7-录入余额
     * 这3种类型都属于送货前售后
     */
    public static final List<Integer> SERVICE_TYPE = Arrays.asList(OrderAfterSaleServiceTypeEnum.REFUND.getValue(),
        OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue(),
        OrderAfterSaleServiceTypeEnum.BALANCE.getValue());

    /**
     * 计算退款时，本单已退的售后单状态
     * 3-处理中 4-退款中 5-已成功
     */
    public static final List<Integer> EFFECTIVE_AFTER_SALE_STATUS = Arrays.asList(
        OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
        OrderAfterSaleStatusEnum.REFUNDING.getValue(),
        OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());

    /**
     * 运费生成备注 - 通过每单阶梯价生成
     */
    public static final String DELIVERY_FEE_STEP = "通过每单阶梯价生成";

    /**
     * 运费生成备注 - 每日第二单起运费免费
     */
    public static final String DELIVERY_FEE_DAILY = "每日第二单起运费免费";

    /**
     * 运费生成备注 - 通过随仓报价生成
     */
    public static final String DELIVERY_FEE_FOLLOW = "通过随仓报价生成";

    /**
     * 运费生成衆主 - 部分退货不退运费
     */
    public static final String DELIVERY_SUB_RETURN = "部分退货不退运费";

    /**
     * 运费生成备注 - 没有需要计算运费的商品
     */
    public static final String DELIVERY_NO_ITEM = "没有需要计算运费的商品";

    /**
     * 运费生成备注 - 全局包邮免运费
     */
    public static final String DELIVERY_FEE_GLOBAL = "全局包邮免运费生成";

    /**
     * 运费快照-生成快照场景 1-下单
     */
    public static final Integer SCENE_PLACE_ORDER = 1;

    /**
     * 运费快照-生成快照场景 2-发货前售后
     */
    public static final Integer SCENE_NOT_SEND_AFTER_SALE = 2;

    /**
     * 是否为默认 1-是
     */
    public static final Integer DEFAULT_STATUS = 1;

    /**
     * 正常或审核通过
     */
    public static final Integer STORE_NORMAL_STATUS = 1;

}
