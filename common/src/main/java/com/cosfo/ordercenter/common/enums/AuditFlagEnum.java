package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditFlagEnum {


    /**
     * 审核不通过
     */
    AUDIT_FAIL(0,"审核不通过",4),

    /**
     * 审核通过
     */
    AUDIT_SUCCESS(1,"审核通过",1),
    ;

    /**
     * 标识
     */
    private Integer flag;

    /**
     * 描述
     */
    private String desc;

    /**
     * 状态码
     */
    private Integer status;
}
