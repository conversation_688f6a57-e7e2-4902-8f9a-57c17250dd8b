package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 售后创建请求来源
 *
 * @author: xiaowk
 * @date: 2024/1/2 下午1:32
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleReqSourceEnum implements Serializable {
    CHANGE_ORDER("change-order", "订单改单");

    private String reqSource;
    private String desc;

    public static OrderAfterSaleReqSourceEnum getByReqSource(String reqSource) {
        for (OrderAfterSaleReqSourceEnum reqSourceEnum : OrderAfterSaleReqSourceEnum.values()) {
            if (reqSourceEnum.reqSource.equals(reqSource)) {
                return reqSourceEnum;
            }
        }
        return null;
    }


    public static boolean isEqualReqSource(String reqSource, OrderAfterSaleReqSourceEnum reqSourceEnum) {
        if (reqSourceEnum == null) {
            return false;
        }
        return reqSourceEnum.reqSource.equals(reqSource);
    }

}
