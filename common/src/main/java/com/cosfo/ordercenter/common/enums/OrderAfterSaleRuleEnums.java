package com.cosfo.ordercenter.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/3/17
 */
public class OrderAfterSaleRuleEnums {

    @Getter
    @AllArgsConstructor
    public enum DefaultFlag {
        TRUE(0, "是"),
        FALSE(1, "不是");

        /**
         * 标识
         */
        private Integer flag;

        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum Type{
        /**
         * 非法数据
         */
        NULL_ERROR(-1, "非法数据"),
        /**
         * 配送仓库
         */
        WAREHOUSE(0,"配送仓库"),
        /**
         * 商品分组
         */
        CLASSIFICATION(1,"商品分组");
        /**
         * 标识
         */
        private Integer code;

        /**
         * 描述
         */
        private String desc;

        /**
         * 根据code获取类型
         *
         * @param code
         * @return
         */
        public static Type getByCode(Integer code){
            for(Type type : Type.values()){
                if(type.code.equals(code)){
                    return type;
                }
            }

            return Type.NULL_ERROR;
        }
    }
}
