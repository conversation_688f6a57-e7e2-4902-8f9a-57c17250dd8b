package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.cosfo.ordercenter.common.enums.OrderAfterSaleTypeEnum.DELIVERED;
import static com.cosfo.ordercenter.common.enums.OrderAfterSaleTypeEnum.NOT_SEND;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleServiceTypeEnum implements Serializable {
    /**
     * 退款
     */
    REFUND(1, "退款"),

    /**
     * 退款录入账单
     */
    REFUND_ENTER_BILL(2, "录入账单"),

    /**
     * 退货退款
     */
    RETURN_REFUND(3, "退货退款"),

    /**
     * 退货退款录入账单
     */
    RETURN_REFUND_ENTER_BILL(4, "退货录入账单"),

    /**
     * 换货
     */
    EXCHANGE(5, "换货"),

    /**
     * 补发
     */
    RESEND(6, "补发"),

    /**
     * 录入余额
     */
    BALANCE(7, "录入余额"),

    /**
     * 退货录入余额
     */
    RETURN_REFUND_BALANCE(8, "退货录入余额"),
    ;

    private Integer value;
    private String content;

    /**
     * get 描述
     *
     * @param type
     * @return
     */
    public static String getDesc(Integer type) {
        for (OrderAfterSaleServiceTypeEnum afterSaleServiceTypeEnum : OrderAfterSaleServiceTypeEnum.values()) {
            if (afterSaleServiceTypeEnum.value.equals(type)) {
                return afterSaleServiceTypeEnum.content;
            }
        }
        return null;
    }

    public static OrderAfterSaleServiceTypeEnum from(Integer type) {
        for (OrderAfterSaleServiceTypeEnum afterSaleServiceTypeEnum : OrderAfterSaleServiceTypeEnum.values()) {
            if (afterSaleServiceTypeEnum.value.equals(type)) {
                return afterSaleServiceTypeEnum;
            }
        }
        return null;
    }


    /**
     * 是否是已到货退款
     *
     * @param serviceType
     * @param afterSaleType
     * @return
     */
    public static Boolean verifyIsReceivedRefund(Integer serviceType, Integer afterSaleType) {
        //List<Integer> refundTypeList = Arrays.asList(ServiceType.REFUND.getValue(), ServiceType.REFUND_ENTER_BILL.getValue(), ServiceType.BALANCE.getValue(), ServiceType.RETURN_REFUND_BALANCE.getValue());
        Boolean refundFlag = Objects.equals(serviceType, REFUND.getValue())
                || Objects.equals(serviceType, REFUND_ENTER_BILL.getValue())
                || Objects.equals(serviceType, BALANCE.getValue());
        Boolean receivedFlag = Objects.equals(afterSaleType, DELIVERED.getType());
        return receivedFlag && refundFlag;

    }

    public static Boolean verifyInteraction(Integer serviceType) {
        if (Objects.equals(RETURN_REFUND.getValue(), serviceType)
                || Objects.equals(RETURN_REFUND_ENTER_BILL.getValue(), serviceType)
                || Objects.equals(EXCHANGE.getValue(), serviceType)
                || Objects.equals(RESEND.getValue(), serviceType)
                || Objects.equals(RETURN_REFUND_BALANCE.getValue(), serviceType)) {
            return true;
        }
        return false;
    }


    /**
     * 是否是退款
     *
     * @param serviceType
     * @return
     */
    public static Boolean verifyIsRefund(Integer serviceType) {
        Boolean refundFlag = Objects.equals(serviceType, REFUND.getValue())
                || Objects.equals(serviceType, RETURN_REFUND.getValue())
                || Objects.equals(serviceType, REFUND_ENTER_BILL.getValue())
                || Objects.equals(serviceType, RETURN_REFUND_ENTER_BILL.getValue())
                || Objects.equals(serviceType, BALANCE.getValue())
                || Objects.equals(serviceType, RETURN_REFUND_BALANCE.getValue());
        return refundFlag;
    }

    /**
     * 是否仅退款，不包含退货退款
     *
     * @param serviceType
     * @return
     */
    public static boolean verifyIsOnlyRefund(Integer serviceType) {
        Boolean refundFlag = Objects.equals(serviceType, REFUND.getValue())
                || Objects.equals(serviceType, REFUND_ENTER_BILL.getValue())
                || Objects.equals(serviceType, BALANCE.getValue());
        return refundFlag;
    }

    /**
     * 校验是否退货退款类型
     *
     * @param serviceType
     * @return
     */
    public static boolean verifyIsReturnRefund(Integer serviceType) {
        return Objects.equals(serviceType, RETURN_REFUND.getValue())
                || Objects.equals(serviceType, RETURN_REFUND_ENTER_BILL.getValue())
                || Objects.equals(serviceType, RETURN_REFUND_BALANCE.getValue());
    }


    /**
     * 返回退货退款类型列表
     *
     * @return
     */
    public static List<Integer> getReturnRefundList() {
        return Arrays.asList(RETURN_REFUND.getValue(),
                RETURN_REFUND_ENTER_BILL.getValue(),
                RETURN_REFUND_BALANCE.getValue());
    }

    /**
     * 根据支付类型以及到货类型获取售后类型
     *
     * @param payType
     * @param deliveryType
     * @return
     */
    public static Integer getServiceTypeByPayType(Integer payType, Integer deliveryType) {
        if (Objects.equals(payType, PayTypeEnum.WECHAT_PAY.getCode()) && Objects.equals(deliveryType, NOT_SEND.getType())) {
            return REFUND.getValue();
        } else if (Objects.equals(payType, PayTypeEnum.WECHAT_PAY.getCode()) && Objects.equals(deliveryType, DELIVERED.getType())) {
            return RETURN_REFUND.getValue();
        } else if (Objects.equals(payType, PayTypeEnum.BILL.getCode()) && Objects.equals(deliveryType, NOT_SEND.getType())) {
            return REFUND_ENTER_BILL.getValue();
        } else if (Objects.equals(payType, PayTypeEnum.BILL.getCode()) && Objects.equals(deliveryType, DELIVERED.getType())) {
            return RETURN_REFUND_ENTER_BILL.getValue();
        } else if (Objects.equals(payType, PayTypeEnum.BALANCE.getCode()) && Objects.equals(deliveryType, NOT_SEND.getType())) {
            return BALANCE.getValue();
        } else if (Objects.equals(payType, PayTypeEnum.BALANCE.getCode()) && Objects.equals(deliveryType, DELIVERED.getType())) {
            return RETURN_REFUND_BALANCE.getValue();
        } else {
            return REFUND.getValue();
        }
    }


}
