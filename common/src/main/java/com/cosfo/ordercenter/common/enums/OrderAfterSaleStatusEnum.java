package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderAfterSaleStatusEnum implements Serializable {
    /**
     * 待审核
     */
    UNAUDITED(1, "待审核"),
    /**
     * 库存处理中
     */
    INVENTORY_DEALING(2, "处理中"),
    /**
     * 账户金额处理中
     */
    REFUNDING(3, "退款中"),
    /**
     * 审核成功
     */
    AUDITED_SUCCESS(4, "已成功"),
    /**
     * 审核失败
     */
    AUDITED_FAILED(5, "已拒绝"),
    /**
     * 已取消
     */
    CANCEL(6, "已取消"),
    /**
     * 库存返还失败
     */
    INVENTORY_FAIl(7, "库存退款失败"),
    /**
     * 待退款
     */
    WAIT_REFUND(8, "待退款"),
    /**
     * 三方处理流程中
     */
    THIRD_PROCESSING(9, "三方处理中"),
    /**
     * 待退货
     */
    WAIT_REFUND_GOODS(10, "待退货"),
    /**
     * 退货中
     */
    REFUNDDING_GOODS(11, "退货中"),
    /**
     * 待确认（代仓售后定责为服务商则需要确认）
     */
    WAIT_CONFIRM(12, "待确认"),
    ;


    // 显示入库信息的售后状态
    public static final List<Integer> showStatusSetForReturnGoodsInboundInfo = Arrays.asList(REFUNDDING_GOODS.value, WAIT_REFUND.value, REFUNDING.value, AUDITED_SUCCESS.value);

    /**
     * 处理中或成功的售后状态
     */
    public static final List<Integer> dealingOrSuccessStatusList = Arrays.asList(INVENTORY_DEALING.value, REFUNDING.value, AUDITED_SUCCESS.value,
            INVENTORY_FAIl.value, WAIT_REFUND.value, THIRD_PROCESSING.value,
            WAIT_REFUND_GOODS.value, REFUNDDING_GOODS.value);


    /**
     * 售后订单状态编码
     */
    private Integer value;
    /**
     * 售后订单状态描述
     */
    private String content;

    public static String getStatusDesc(Integer code) {
        for (OrderAfterSaleStatusEnum afterSaleStatusEnum : OrderAfterSaleStatusEnum.values()) {
            if (afterSaleStatusEnum.value.equals(code)) {
                return afterSaleStatusEnum.content;
            }
        }
        return null;
    }

    public static OrderAfterSaleStatusEnum fromCode(Integer code) {
        for (OrderAfterSaleStatusEnum afterSaleStatusEnum : OrderAfterSaleStatusEnum.values()) {
            if (afterSaleStatusEnum.value.equals(code)) {
                return afterSaleStatusEnum;
            }
        }
        return null;
    }


    /**
     * 售后状态是否【处理中】的子状态，归属于【处理中】
     * @param status
     * @return
     */
    public static boolean checkSubStatusForInventoryDealing(Integer status) {
        return Arrays.asList(THIRD_PROCESSING.value, WAIT_REFUND_GOODS.value, REFUNDDING_GOODS.value).contains(status);
    }

    /**
     * 返回所有处理中或成功的售后状态，不包括待审核和失败、取消
     * @return
     */
    public static List<Integer> getDealingOrSuccessStatusList() {
        return Arrays.asList(INVENTORY_DEALING.value, REFUNDING.value, AUDITED_SUCCESS.value,
                INVENTORY_FAIl.value, WAIT_REFUND.value, THIRD_PROCESSING.value,
                WAIT_REFUND_GOODS.value, REFUNDDING_GOODS.value);
    }

    /**
     * 是否显示入库信息的判断
     * @param status
     * @param serviceType
     * @param warehouseType
     * @return
     */
    public static boolean isShowReturnGoodsInboundInfo(Integer status, Integer serviceType, Integer warehouseType) {
        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            return false;
        }

        if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            return false;
        }

        if (showStatusSetForReturnGoodsInboundInfo.contains(status) && OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(serviceType)) {
            return true;
        }
        return false;
    }

    public static String getDetailStatusDesc(Integer status, Integer serviceType, Integer warehouseType) {
        String statusDesc = "-";
        for (OrderAfterSaleStatusEnum afterSaleStatusEnum : OrderAfterSaleStatusEnum.values()) {
            if (afterSaleStatusEnum.value.equals(status)) {
                statusDesc = afterSaleStatusEnum.content;
            }
        }

        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            return statusDesc;
        }

        if (OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue().equals(status) && OrderAfterSaleServiceTypeEnum.verifyIsOnlyRefund(serviceType)) {
            statusDesc = "已同意-释放库存中";
        }

        if ((OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue().equals(status) || OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue().equals(status)) && OrderAfterSaleServiceTypeEnum.RESEND
                .getValue().equals(serviceType)) {
            statusDesc = "待补发";
        }

        if (OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(status) && OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
            statusDesc = "已补发";
        }

        if (OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue().equals(status)) {
            statusDesc = "待买家退货";
        }

        if (OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue().equals(status)) {
            statusDesc = "待仓库签收";
        }
        return statusDesc;
    }


    /**
     * 已申请的状态
     *
     * @return
     */
    public static List<Integer> getAppliedStatusList() {
        return Arrays.asList(
                OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
                OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
                OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(),
                OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue()
        );
    }

    /**
     * 返回给前端展示的状态
     *
     * @param code
     * @return
     */
    public static Integer getShowStatus(Integer code, Integer serviceType) {
        //1处理中 2已成功 3已失败 4已取消 5待退货 6待审核 7待卖家审核 8 待退款 9已补发 10待补发
        if (Objects.equals(code, OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()) && (!Objects.equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue(), serviceType))) {
            return 2;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.AUDITED_FAILED.getValue())) {
            return 3;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.CANCEL.getValue())) {
            return 4;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue())) {
            return 5;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.UNAUDITED.getValue())) {
            return 6;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue())) {
            return 7;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.WAIT_REFUND.getValue())) {
            return 8;
        } else if (Objects.equals(code, OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue()) && Objects.equals(OrderAfterSaleServiceTypeEnum.RESEND.getValue(), serviceType)) {
            return 9;
        } else if ((Objects.equals(code, OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue()) || OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue().equals(code))
                && OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
            return 10;
        } else {
            return 1;
        }

    }
}