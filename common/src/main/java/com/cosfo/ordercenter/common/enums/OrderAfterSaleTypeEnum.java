package com.cosfo.ordercenter.common.enums;

import java.io.Serializable;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
public enum OrderAfterSaleTypeEnum implements Serializable {
    DELIVERED(0, "配送后售后"),
    NOT_SEND(1, "配送前售后");

    private Integer type;
    private String desc;

    public static String getDesc(Integer code) {
        OrderAfterSaleTypeEnum[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            OrderAfterSaleTypeEnum afterSaleTypeEnum = var1[var3];
            if (afterSaleTypeEnum.type.equals(code)) {
                return afterSaleTypeEnum.desc;
            }
        }

        return null;
    }

    public Integer getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    private OrderAfterSaleTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
