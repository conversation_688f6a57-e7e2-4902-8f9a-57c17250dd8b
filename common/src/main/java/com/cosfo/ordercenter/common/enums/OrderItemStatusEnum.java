package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OrderItemStatusEnum {

    CREATING_ORDER(1,"下单中"),
    NO_PAYMENT(2,"待支付"),
    PAID(3,"已支付"),
    FINISHED(4,"已完成"),
    CANCELED(5,"已取消"),
    REFUND(6,"已退款");


    /**
     * 订单项状态编码
     */
    private Integer code;
    /**
     * 订单项状态描述
     */
    private String desc;
}
