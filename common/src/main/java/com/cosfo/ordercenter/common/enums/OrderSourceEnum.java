package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 订单来源
 *
 * @author: xiaowk
 * @date: 2023/9/27 上午11:45
 */
@Getter
@AllArgsConstructor
public enum OrderSourceEnum implements Serializable {

    INNER_SYSTEM(0, "内部系统"),
    OPENAPI(1, "openapi调用"),
    ;

    private final Integer value;
    private final String content;
}
