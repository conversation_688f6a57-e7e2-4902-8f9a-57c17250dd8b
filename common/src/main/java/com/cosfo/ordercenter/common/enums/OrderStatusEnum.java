package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    /**
     * 下单中
     */
    CREATING_ORDER(1,"下单中"),
    /**
     * 待支付
     */
    NO_PAYMENT(2,"待支付"),
    /**
     * 已支付 PAID
     */
    WAIT_DELIVERY(3,"已支付"),
    /**
     * 待收货
     */
    DELIVERING(4,"待收货"),
    /**
     * 已完成
     */
    FINISHED(5,"已完成"),
    /**
     * 已取消
     */
    CANCELED(6,"已取消"),
    /**
     * 已退款
     */
    REFUNDED(7,"已退款"),
    /**
     * 关单中
     */
    CLOSING(8,"关单中"),
    /**
     * 已关闭
     */
    CLOSED(9, "已关闭"),

    /**
     * 3 -> 10  -> 4 等待出库
     */
    WAITING_DELIVERY(10,"等待出库"),

    /**
     *  无仓订单部分配送时，会更新为当前状态
     *  3 -> 10 -> 11  -> 4 部分配送
     */
    SEGMENT_WAITING_DELIVERY(11,"部分配送"),
    /**
     * 出库中 自营仓订单 3 -> 10 -> 12 ->  4
     */
    OUT_OF_STORAGE(12,"出库中"),

    /**
     * 待审核 支付成功后，如果需要审核更新为13，不需要审核更新为10
     */
    WAIT_AUDIT(13,"待审核");
    ;

    /**
     * 订单状态编码
     */
    private Integer code;
    /**
     * 订单状态描述
     */
    private String desc;

    public static String getDesc(Integer code) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.code.equals(code)) {
                return orderStatusEnum.desc;
            }
        }

        return null;
    }

    /**
     * 能否发起售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyAfterSale(Integer status) {
        if (WAIT_DELIVERY.getCode().equals(status) || DELIVERING.getCode().equals(status) || FINISHED.getCode().equals(status)
                || WAITING_DELIVERY.getCode().equals(status)
                || SEGMENT_WAITING_DELIVERY.getCode().equals(status) || OUT_OF_STORAGE.getCode().equals(status)
                || WAIT_AUDIT.getCode().equals(status)) {
            return true;
        }

        return false;
    }

    /**
     * 能否发起配送前售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyNotSendAfterSale(Integer status) {
        if (WAIT_DELIVERY.getCode().equals(status)
                || WAITING_DELIVERY.getCode().equals(status)
                || WAIT_AUDIT.getCode().equals(status)) {
            return true;
        }

        return false;
    }

    /**
     * 能否发起配送后售后
     *
     * @param status
     * @return
     */
    public static boolean ableApplyDeliveredAfterSale(Integer status) {
        if (DELIVERING.getCode().equals(status) || FINISHED.getCode().equals(status)
                || SEGMENT_WAITING_DELIVERY.getCode().equals(status) || OUT_OF_STORAGE.getCode().equals(status)) {
            return true;
        }

        return false;
    }

    public static OrderStatusEnum fromCode(int code) {
        for (OrderStatusEnum orderStatusEnum : values()) {
            if (orderStatusEnum.getCode().equals(code)) {
                return orderStatusEnum;
            }
        }
        throw new IllegalArgumentException("OrderStatusEnum not found for code:" + code);
    }
}
