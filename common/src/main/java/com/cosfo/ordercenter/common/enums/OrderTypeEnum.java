package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

/**
 * 订单类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderTypeEnum implements Serializable {

    ORDINARY_ORDER(0, "普通订单"),
    COMBINE_ORDER(1, "组合订单"),
    PRESALE_ORDER(2,"预售订单"),

    ;

    private final Integer value;
    private final String content;
}
