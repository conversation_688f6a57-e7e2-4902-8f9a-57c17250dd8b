package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PayTypeEnum {
    /**
     * 微信支付
     */
    WECHAT_PAY(1,"微信支付"),
    /**
     * 账期支付
     */
    BILL(2,"账期支付"),
    /**
     * 余额支付
     */
    BALANCE(3,"余额支付");

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;

    /**
     * 获取支付类型
     *
     * @param code
     * @return
     */
    public static PayTypeEnum getPayType(Integer code){
        for (PayTypeEnum payTypeEnum : PayTypeEnum.values()){
            if(payTypeEnum.getCode().equals(code)){
                return payTypeEnum;
            }
        }

        return null;
    }
}
