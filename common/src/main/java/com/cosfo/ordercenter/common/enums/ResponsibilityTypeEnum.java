package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/1
 */
@Getter
@AllArgsConstructor
public enum ResponsibilityTypeEnum {
    /**
     * 供应商
     */
    SUPPLIER(0, "供应商"),
    /**
     * 品牌方
     */
    BRAND(1,"品牌方"),
    /**
     * 门店
     */
    MERCHANT(2,"门店");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;
}
