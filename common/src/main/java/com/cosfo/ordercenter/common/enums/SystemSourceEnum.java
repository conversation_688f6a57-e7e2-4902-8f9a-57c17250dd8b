package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SystemSourceEnum {

    MALL(0, "商城"),
    MANAGE(1, "帆台后台"),
    OMS(2, "Boss后台"),

    ;

    /**
     * 状态类型编码
     */
    private Integer code;
    /**
     * 状态类型描述
     */
    private String desc;


    /**
     * 获取请求来源
     *
     * @param code
     * @return
     */
    public static SystemSourceEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SystemSourceEnum systemSourceEnum : SystemSourceEnum.values()) {
            if (systemSourceEnum.getCode().equals(code)) {
                return systemSourceEnum;
            }
        }

        return null;
    }
}
