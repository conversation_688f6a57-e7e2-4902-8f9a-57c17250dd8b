package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 18:23
 * @Description:
 */
public class TenantDeliveryEnum {

    @Getter
    @AllArgsConstructor
    public enum TypeEnum {
        /**
         * 跟随供应商
         */
        FOLLOW_SUPPLIER(0, "跟随供应商"),
        /**
         * 免运费
         */
        FREE(1, "免运费"),
        /**
         * 自定义
         */
        CUSTOMIZE(2,"自定义");

        /**
         * 类型
         */
        private Integer type;
        /**
         * 描述
         */
        private String desc;

        public static TypeEnum getByType(Integer type) {
            for (TypeEnum enumType : TypeEnum.values()) {
                if (enumType.type.equals(type)) {
                    return enumType;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum FreeType{
        /**
         * 按金额
         */
        MONEY(0,"按金额"),
        /**
         * 按件数
         */
        NUMBER(1,"按件数");
        /**
         * 类型
         */
        private Integer type;
        /**
         * 描述
         */
        private String desc;
    }
}
