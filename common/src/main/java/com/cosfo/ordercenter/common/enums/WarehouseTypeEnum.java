package com.cosfo.ordercenter.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum WarehouseTypeEnum {
    PROPRIETARY(0,"无仓订单","品牌自营仓"),
    THREE_PARTIES(1,"三方优选仓", "三方优选仓"),
    SELF_SUPPLY(2,"自营仓", "品牌自营仓");

    /**
     * 配送仓类型编码
     */
    private Integer code;
    /**
     * 配送仓类型描述
     */
    private String desc;
    /**
     * 订单名称
     */
    private String name;

    public static WarehouseTypeEnum getByCode(Integer code){
        for(WarehouseTypeEnum warehouseTypeEnum : WarehouseTypeEnum.values()){
            if(warehouseTypeEnum.getCode().equals(code)){
                return warehouseTypeEnum;
            }
        }

        return null;
    }
}
