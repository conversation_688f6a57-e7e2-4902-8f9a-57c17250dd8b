package com.cosfo.ordercenter.common.exception;

import lombok.Getter;
import net.xianmu.common.exception.error.code.ErrorCode;

/**
 * @Author: fansongsong
 * @Date: 2023-09-25
 * @Description:
 */
@Getter
public enum OpenApiErrorCode implements ErrorCode {

    CREATE_AFTER_VALID_CODE(100001, "创建售后业务拦截异常"),
    CREATE_AFTER_SERVICE_TYPE_VALID_CODE(100002, "同一订单，批量售后不支持多种售后类型"),
    CREATE_AFTER_CONCURRENCE_VALID_CODE(100003, "批量售后并发限制异常"),
    AFTER_ORDER_NO_NO_EXIST_CODE(100004, "存在售后单已处理"),
    ORDER_STATUS_UN_SUPPORT_CODE(100005, "订单当前状态不支持发起配送后售后"),

    OR_EXIST_REPEAT_ORDER(200001, "存在重复的订单"),
    OR_EXIST_REPEAT_SKUCODE(200002, "存在重复的商品"),
    OR_ADDRESS_NOT_SUPPORTED_DELIVERY(200003, "门店地址不支持配送"),
    OR_EXIST_NOT_QUOTATION_ITEM(200004, "存在非鲜沐直供商品"),
    OR_PAY_ORDER_ERROR(200005, "订单记录支付账单失败"),
    ;


    private final Integer status;

    private final String msg;

    private final String code;

    OpenApiErrorCode(Integer status, String msg, String code) {
        this.status = status;
        this.msg = msg;
        this.code = code;
    }

    OpenApiErrorCode(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
        this.code = "OPENAPI-DEFAULT_ERROR";
    }


    @Override
    public Integer getStatus() {
        return status;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return msg;
    }
}
