package com.cosfo.ordercenter.common.exception;

import net.xianmu.common.exception.error.code.ErrorCode;
import net.xianmu.common.exception.error.code.ProviderErrorCode;

/**
 * @Author: fansongsong
 * @Date: 2023-09-25
 * @Description:
 */
public class OpenApiProviderException extends RuntimeException {

    private ErrorCode errorCode;

    public ErrorCode getErrorCode() {
        return this.errorCode;
    }

    public OpenApiProviderException() {
        this.errorCode = new ProviderErrorCode();
    }

    public OpenApiProviderException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = new ProviderErrorCode();
        this.errorCode = errorCode;
    }

    public OpenApiProviderException(Integer status, String code) {
        this(new ProviderErrorCode(status, code));
    }

    public OpenApiProviderException(String message) {
        super(message);
        this.errorCode = new ProviderErrorCode();
    }

    public OpenApiProviderException(String message, ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public OpenApiProviderException(String message, Integer status, String code) {
        this(message, (new ProviderErrorCode(status, code)));
    }

    public OpenApiProviderException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = new ProviderErrorCode();
    }

    public OpenApiProviderException(String message, Throwable cause, ErrorCode errorCode) {
        super(message, cause);
        this.errorCode = new ProviderErrorCode();
        this.errorCode = errorCode;
    }

    public OpenApiProviderException(String message, Throwable cause, Integer status, String code) {
        this(message, cause, new ProviderErrorCode(status, code));
    }

    public OpenApiProviderException(Throwable cause) {
        super(cause);
        this.errorCode = new ProviderErrorCode();
    }

    public OpenApiProviderException(Throwable cause, ErrorCode errorCode) {
        super(cause);
        this.errorCode = new ProviderErrorCode();
        this.errorCode = errorCode;
    }

    public OpenApiProviderException(Throwable cause, Integer status, String code) {
        this(cause, (new ProviderErrorCode(status, code)));
    }
}
