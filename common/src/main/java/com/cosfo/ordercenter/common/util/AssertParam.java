package com.cosfo.ordercenter.common.util;

import com.cosfo.ordercenter.common.exception.OpenApiProviderException;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.error.code.ErrorCode;

/**
 * <AUTHOR>
 */
public class AssertParam {

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new BizException(message);
        }
    }

    /**
     * result不为true，抛出业务异常
     *
     * @param result
     * @param message
     */
    public static void expectTrue(boolean result, String message, ErrorCode errorCode) {
        if (!result) {
            throw new BizException(message, errorCode);
        }
    }

    public static void notNullWithOpenApiProviderException(Object object, String message, ErrorCode errorCode) {
        if (object == null) {
            throw new OpenApiProviderException(message, errorCode);
        }
    }
}
