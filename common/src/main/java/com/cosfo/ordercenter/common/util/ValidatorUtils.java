package com.cosfo.ordercenter.common.util;

import net.xianmu.common.exception.ParamsException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 18:05
 * @Description: 用于方法内部调用时，入参的校验。
 * * 支持javax.validation的注解。也可自定义实现ConstraintValidator。
 * * 使用方法：ValidatorUtils.validateEntity(POJO);
 */
public class ValidatorUtils {


    private static Validator validator;

    static {
        validator = Validation.buildDefaultValidatorFactory().getValidator();
    }

    /**
     * 校验对象
     *
     * @param object 待校验对象
     * @param groups 待校验的组
     * @throws ParamsException 校验不通过，则报RRException异常
     */
    public static void validateEntity(Object object, Class<?>... groups) throws ParamsException {
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(object, groups);
        if (!constraintViolations.isEmpty()) {
            ConstraintViolation<Object> constraint = constraintViolations.iterator().next();
            throw new ParamsException(constraint.getMessage());
        }
    }

    public static boolean isNotNull(Object obj) {
        try {
            if (obj == null || obj.toString().equals("null") || obj.toString().equals("")) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return true;
        }
    }


}
