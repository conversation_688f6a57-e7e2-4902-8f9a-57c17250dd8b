package com.cosfo.ordercenter.dao.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
public interface OrderAfterSaleDao extends IService<OrderAfterSale> {

    /**
     * 根据条件查询售后单
     *
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSale> queryListByCondition(OrderAfterSaleQueryParam orderAfterSaleQueryParam);

    /**
     * 批量插入售后单
     * @param orderAfterSales
     * @return
     */
    List<Long> batchAdd(List<OrderAfterSale> orderAfterSales);

    /**
     * 售后单查询
     * @param req
     * @return
     */
    List<OrderAfterSale> queryList(OrderAfterSaleQueryReq req);

    /**
     * 售后单分页查询
     * @param req
     * @return
     */
    Page<OrderAfterSale> queryPage(OrderAfterSalePageQueryReq req);

    /**
     * 根据售后no查询售后单
     * @param afterSaleNo
     * @return
     */
    OrderAfterSale queryByAfterSaleNo(String afterSaleNo);

    /**
     * 统计售后单数量
     * @param orderAfterSaleCountReq
     * @return
     */
    Integer countOrderAfterSale(OrderAfterSaleCountReq orderAfterSaleCountReq);


    /**
     * 根据订单查询售后单
     * @param orderId
     * @param tenantId
     * @return
     */
    List<OrderAfterSale> queryByOrderId(Long orderId, Long tenantId);

//    /**
//     * 查询可售后数量
//     * @param tenantId
//     * @param orderId
//     * @return
//     */
//    Map<Long, OrderAfterSaleEnableDTO> queryEnableApply(Long tenantId, Long orderId);

    /**
     * 查询售后记录最近使用的退回地址id
     * @param tenantId
     * @return
     */
    Long getRecentlyUsedReturnAddressId(Long tenantId);

    List<OrderAfterSale> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleReq req);

    /**
     * 更新售后单状态
     * @param orderAfterSaleStatusUpdateReq
     * @return
     */
    boolean updateStatus(OrderAfterSaleStatusUpdateReq orderAfterSaleStatusUpdateReq);

    /**
     * 根据售后单no查询
     * @param orderAfterSaleNos
     * @return
     */
    List<OrderAfterSale> queryByNos(List<String> orderAfterSaleNos);


    /**
     * 根据售后单id查询
     */
    List<OrderAfterSale> queryByIds(List<Long> orderAfterSaleIds);

    /**
     * 根据订单统计售后单数量
     * @param req
     * @return
     */
    Map<Long, Integer> countOrderAfterSaleByOrderId(OrderAfterSaleCountReq req);

    /**
     * 售后单自动完结
     * @return
     */
    boolean autoFinish();


    /**
     * 更新城配仓号
     * @param afterSaleId
     * @param sourceStoreNo
     * @param storeNo
     * @return
     */
    boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo);

    /**
     * 根据入参批量查询
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSale> queryListByParam(OrderAfterSaleQueryParam orderAfterSaleQueryParam);
}
