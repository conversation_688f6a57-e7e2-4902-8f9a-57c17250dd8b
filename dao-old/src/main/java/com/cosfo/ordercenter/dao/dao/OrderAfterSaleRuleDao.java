package com.cosfo.ordercenter.dao.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/8/11 上午10:43
 */
public interface OrderAfterSaleRuleDao extends IService<OrderAfterSaleRule> {

    /**
     * 初始化售后规则时，插入记录，如果存在，则更新
     * @param orderAfterSaleRule
     * @return
     */
    Long addOrUpdate(OrderAfterSaleRule orderAfterSaleRule);

    /**
     * 查询租户的售后规则
     * @param tenantId
     * @return
     */
    List<OrderAfterSaleRule> queryByTenantId(Long tenantId);

    Integer updateRule(OrderAfterSaleRule orderAfterSaleRule);

    Integer deleteRule(Long id);
}
