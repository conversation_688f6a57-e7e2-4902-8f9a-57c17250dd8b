package com.cosfo.ordercenter.dao.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.dto.SupplierOrderDTO;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryQueryParam;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryUpdateParam;
import com.cosfo.ordercenter.dao.model.param.OrderQueryParam;
import com.cosfo.ordercenter.dao.model.param.OrderStatusUpdateParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderDetail;
import com.cosfo.ordercenter.dao.model.po.OrderItemSaleQuantity;
import com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface OrderDao extends IService<Order> {

    /**
     * 计算订单金额
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    BigDecimal sumOrderTotalPrice(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款订单
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    Integer countPayOrderQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款店铺
     * @param start
     * @param end
     * @param tenantId
     * @param storeIds
     * @return
     */
    Integer countPayOrderStoreQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);


    /**
     * 计算待配送数量
     * @param tenantId
     * @return
     */
    Integer getWaitDeliveryNum(Long tenantId);

    /**
     * 批量更新订单状态
     * @param orderStatusBatchUpdateReq
     * @return
     */
    Integer batchUpdateStatus(OrderStatusBatchUpdateReq orderStatusBatchUpdateReq);

    /**
     * 查询sku维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantity> querySkuSaleQuantity(List<Long> skuIds,
                                                Long tenantId,
                                                LocalDateTime startTime,
                                                LocalDateTime endTime);


    /**
     * 查询sku，仓库维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantity> querySkuSaleWithStoreNoQuantity(List<Long> skuIds,
                                                           Long tenantId,
                                                           LocalDateTime startTime,
                                                           LocalDateTime endTime);

    /**
     * 查询sku、城市维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantity> querySkuSaleWithCityQuantity(List<Long> skuIds,
                                                        Long tenantId,
                                                        LocalDateTime startTime,
                                                        LocalDateTime endTime);


    /**
     * 查询订单明细列表
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderDetail> queryOrderDetail(List<Long> orderIds, Long tenantId);


    /**
     * oms分页查询
     * @param orderQueryReq
     * @return
     */
    Page<Order> queryPage(OrderOmsQueryReq orderQueryReq);

    /**
     * 分页查询
     * @param orderQueryReq
     * @return
     */
    Page<Order> queryPage(OrderQueryReq orderQueryReq);


    /**
     * 订单列表
     * @param queryParam
     * @return
     */
    List<Order> queryList(OrderQueryReq queryParam);


    /**
     * 查询待配送订单
     * @param queryParam
     * @return
     */
    List<String> queryNeedDeliveryOrder(OrderDeliveryQueryParam queryParam);

    /**
     * 按订单no批量查询
     * @param orderNos
     * @return
     */
    List<Order> queryByOrderNos(List<String> orderNos);

    /**
     * 按id更新订单
     * @param orderDTO
     * @return
     */
    Boolean updateById(OrderDTO orderDTO);

    /**
     * 更新订单状态
     * @param updateReq
     * @return
     */
    Boolean updateStatus(OrderStatusUpdateReq updateReq);

    /**
     * 根据来源状态更新订单状态
     * @param updateParam
     * @return
     */
    Boolean updateStatusByOriginStatus(OrderStatusUpdateParam updateParam);


    /**
     * 更新支付方式
     * @param orderDTO
     * @return
     */
    Boolean updatePayType(OrderDTO orderDTO);

    /**
     * 自提
     * @param req
     * @return
     */
    Boolean selfLifting(OrderSelfLiftReq req);

    /**
     * 批量更新分账完成时间
     * @param orderList
     * @return
     */
    Boolean batchUpdateProfitSharingFinishTime(List<Order> orderList);

    /**
     * 查询组合包订单
     * @param combineId
     * @param tenantId
     * @return
     */
    List<Order> queryByCombineId(Long combineId, Long tenantId);

    /**
     * 批量查询组合包订单
     * @param combineIds
     * @param tenantId
     * @return
     */
    List<Order> queryByCombineIds(Set<Long> combineIds, Long tenantId);


    /**
     * 统计订单数量
     * @param orderCountReq
     * @return
     */
    Integer countOrderQuantity(OrderCountReq orderCountReq);


    /**
     * 查询小于配送时间切状态是4的订单
     *
     * @param orderAutoFinishDTO
     * @return
     */
    List<Order> queryNeedAutoFinishedOrder(OrderAutoFinishDTO orderAutoFinishDTO);


    /**
     * 更新订单状态为完成
     * @param orderIds
     * @return
     */
    int orderFinish(List<Long> orderIds);


    /**
     * 更新配送时间
     * @param param
     * @return
     */
    boolean updateDeliveryTime(OrderDeliveryUpdateParam param);


    /**
     * 更新订单配送时间
     * @param orderNos
     * @param deliveryTime
     * @return
     */
    Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime);

    /**
     * 更新自提完成
     * @param req
     * @return
     */
    boolean selfLiftingFinish(OrderSelfLiftingFinishReq req);

    /**
     * 查询门店在某天配送时间的订单
     * @param storeId
     * @param deliveryTime
     * @return
     */
    List<Order> queryByStoreIdAndDeliveryTime(Long storeId, LocalDateTime deliveryTime, Integer warehouseType);

    /**
     * 根据外部订单号查询订单信息
     * @param orderQueryParam
     * @return
     */
    List<Order> queryListByCustomerOrderIds(OrderQueryParam orderQueryParam);

    /**
     * 查询商品周期内售卖数量
     * @param saleQuantityReq
     * @return
     */
    List<OrderItemSaleQuantity> queryOrderItemSaleQuantity(ItemSaleQuantityReq saleQuantityReq);

    Order updateDeliveryFee(Long orderId,BigDecimal newDeliveryFee,BigDecimal oriDeliveryFee);

    /**
     * 查询供应商订单信息
     * @param supplierOrderTotalReq
     * @return
     */
    List<SupplierOrderDTO> querySupplierOrderList(SupplierOrderTotalReq supplierOrderTotalReq);

    /**
     * 更新订单的外部订单号
     * @param orderId
     * @param newCustomerOrderId
     * @return
     */
    Boolean updateOrderCustomerOrderId(Long orderId, String newCustomerOrderId);
}
