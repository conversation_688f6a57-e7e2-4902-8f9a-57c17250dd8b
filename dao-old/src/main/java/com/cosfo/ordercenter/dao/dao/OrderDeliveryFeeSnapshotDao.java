package com.cosfo.ordercenter.dao.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.dao.model.po.OrderDeliveryFeeSnapshot;

/**
 * 下单运费快照(OrderDeliveryFeeSnapshot)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-14 13:46:12
 */
public interface OrderDeliveryFeeSnapshotDao extends IService<OrderDeliveryFeeSnapshot> {


    /**
     * 将该订单所有的对应场景的快照记录，更新为无效
     *
     * @param orderId
     */
    boolean updateEffectiveFlag(Long orderId, Integer scence);
}
