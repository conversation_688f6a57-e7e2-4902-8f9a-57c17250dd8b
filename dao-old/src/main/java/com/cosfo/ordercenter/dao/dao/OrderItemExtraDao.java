package com.cosfo.ordercenter.dao.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.dao.model.param.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;

import java.util.List;

/**
 * 订单子项外部系统扩展表
 *
 * <AUTHOR>
 */
public interface OrderItemExtraDao extends IService<OrderItemExtra> {

    /**
     *  单条保存
     * @param orderItemExtra
     */
    void saveOrderItemExtra(OrderItemExtra orderItemExtra);

    /**
     * 批量保存
     * @param orderItemExtraList
     * @return
     */
    boolean batchSave(List<OrderItemExtra> orderItemExtraList);


    /**
     * 批量查询
     * @param param
     * @return
     */
    List<OrderItemExtra> queryList(OrderItemExtraQueryParam param);

}
