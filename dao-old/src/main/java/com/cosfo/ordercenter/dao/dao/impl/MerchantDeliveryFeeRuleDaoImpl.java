package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.dao.dao.MerchantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.dao.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.ordercenter.dao.model.po.MerchantDeliveryFeeRule;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 门店运费规则表(MerchantDeliveryFeeRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:36:54
 */
@Service
public class MerchantDeliveryFeeRuleDaoImpl extends ServiceImpl<MerchantDeliveryFeeRuleMapper, MerchantDeliveryFeeRule> implements MerchantDeliveryFeeRuleDao {

    @Override
    public List<MerchantDeliveryFeeRule> listByParam(MerchantDeliveryRuleQueryReq param) {
        return list(buildQueryWrapper(param));
    }

    private LambdaQueryWrapper<MerchantDeliveryFeeRule> buildQueryWrapper(MerchantDeliveryRuleQueryReq param) {
        LambdaQueryWrapper<MerchantDeliveryFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MerchantDeliveryFeeRule::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getWarehouseType()), MerchantDeliveryFeeRule::getType, param.getWarehouseType());

        queryWrapper.orderByAsc(MerchantDeliveryFeeRule::getPriority);
        return queryWrapper;
    }
}
