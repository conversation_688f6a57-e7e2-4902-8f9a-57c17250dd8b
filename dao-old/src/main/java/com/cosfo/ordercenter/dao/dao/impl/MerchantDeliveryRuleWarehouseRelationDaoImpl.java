package com.cosfo.ordercenter.dao.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.MerchantDeliveryRuleWarehouseRelationDao;
import com.cosfo.ordercenter.dao.mapper.MerchantDeliveryRuleWarehouseRelationMapper;
import com.cosfo.ordercenter.dao.model.param.QueryDeliveryWarehouseParam;
import com.cosfo.ordercenter.dao.model.po.MerchantDeliveryRuleWarehouseRelation;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 运费规则仓库信息关联表(MerchantDeliveryRuleWarehouseRelation)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:36
 */
@Service
public class MerchantDeliveryRuleWarehouseRelationDaoImpl extends ServiceImpl<MerchantDeliveryRuleWarehouseRelationMapper, MerchantDeliveryRuleWarehouseRelation> implements MerchantDeliveryRuleWarehouseRelationDao {

    @Override
    public Set<Long> listRuleIds(QueryDeliveryWarehouseParam param) {
        if (Objects.isNull(param)
            || Objects.isNull(param.getTenantId())
            || CollectionUtil.isEmpty(param.getWarehouseNoList())
            || CollectionUtil.isEmpty(param.getRuleIds())) {
            throw new ParamsException("参数错误");
        }
        List<MerchantDeliveryRuleWarehouseRelation> ruleWarehouseRelations = listByParam(param);
        return ruleWarehouseRelations.stream()
            .map(MerchantDeliveryRuleWarehouseRelation::getRuleId)
            .collect(Collectors.toSet());
    }

    private List<MerchantDeliveryRuleWarehouseRelation> listByParam(QueryDeliveryWarehouseParam param) {
        return list(buildQueryParam(param));
    }

    private LambdaQueryWrapper<MerchantDeliveryRuleWarehouseRelation> buildQueryParam(QueryDeliveryWarehouseParam param) {
        LambdaQueryWrapper<MerchantDeliveryRuleWarehouseRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MerchantDeliveryRuleWarehouseRelation::getTenantId, param.getTenantId());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getRuleIds()), MerchantDeliveryRuleWarehouseRelation::getRuleId, param.getRuleIds());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getWarehouseNoList()), MerchantDeliveryRuleWarehouseRelation::getWarehouseNo, param.getWarehouseNoList());

        return queryWrapper;
    }
}
