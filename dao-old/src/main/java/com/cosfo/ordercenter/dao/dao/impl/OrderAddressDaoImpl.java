package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.mapper.OrderAddressMapper;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
@Service
public class OrderAddressDaoImpl extends ServiceImpl<OrderAddressMapper, OrderAddress> implements OrderAddressDao {

    @Override
    public List<OrderAddress> queryByOrderIds(List<Long> orderIds, Long tenantId) {
        LambdaQueryWrapper<OrderAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), OrderAddress::getTenantId, tenantId);
        queryWrapper.in(!CollectionUtils.isEmpty(orderIds), OrderAddress::getOrderId, orderIds);
        return list(queryWrapper);
    }

    @Override
    public OrderAddress getByOrderId(Long orderId, Long tenantId) {
        LambdaQueryWrapper<OrderAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), OrderAddress::getTenantId, tenantId);
        queryWrapper.eq(Objects.nonNull(orderId), OrderAddress::getOrderId, orderId);
        return getOne(queryWrapper);
    }

    @Override
    public Long add(OrderAddress orderAddress) {
        save(orderAddress);
        return orderAddress.getId();
    }
}
