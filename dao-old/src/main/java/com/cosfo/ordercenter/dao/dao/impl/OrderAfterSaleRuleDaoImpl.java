package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleRuleDao;
import com.cosfo.ordercenter.dao.mapper.OrderAfterSaleRuleMapper;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *
 * @author: xiaowk
 * @time: 2023/8/11 上午10:44
 */
@Service
public class OrderAfterSaleRuleDaoImpl extends ServiceImpl<OrderAfterSaleRuleMapper, OrderAfterSaleRule> implements OrderAfterSaleRuleDao {

    @Override
    public Long addOrUpdate(OrderAfterSaleRule orderAfterSaleRule) {
        List<OrderAfterSaleRule> list = baseMapper.queryByTenantId(orderAfterSaleRule.getTenantId());
        OrderAfterSaleRule existObj = list.stream().filter(e -> Objects.equals(e.getDefaultFlag(), orderAfterSaleRule.getDefaultFlag())).findFirst().orElse(null);
        if (existObj == null) {
            baseMapper.insertSelective(orderAfterSaleRule);
            return orderAfterSaleRule.getId();
        }
        orderAfterSaleRule.setId(existObj.getId());
        baseMapper.updateByPrimaryKeySelective(orderAfterSaleRule);
        return orderAfterSaleRule.getId();
    }

    @Override
    public List<OrderAfterSaleRule> queryByTenantId(Long tenantId) {
        return baseMapper.queryByTenantId(tenantId);
    }

    @Override
    public Integer updateRule(OrderAfterSaleRule orderAfterSaleRule) {
        if(orderAfterSaleRule == null || orderAfterSaleRule.getId() == null){
            throw new BizException("售后规则id不能为空");
        }
        return baseMapper.updateByPrimaryKeySelective(orderAfterSaleRule);
    }

    @Override
    public Integer deleteRule(Long id) {
        return baseMapper.deleteByPrimaryKey(id);
    }
}
