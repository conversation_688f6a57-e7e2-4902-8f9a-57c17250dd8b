package com.cosfo.ordercenter.dao.dao.impl;

import com.cosfo.ordercenter.dao.model.po.OrderCombineSnapshot;
import com.cosfo.ordercenter.dao.mapper.OrderCombineSnapshotMapper;
import com.cosfo.ordercenter.dao.dao.OrderCombineSnapshotDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 组合订单快照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class OrderCombineSnapshotDaoImpl extends ServiceImpl<OrderCombineSnapshotMapper, OrderCombineSnapshot> implements OrderCombineSnapshotDao {

    @Override
    public boolean batchSave(List<OrderCombineSnapshot> snapshotList) {
        return baseMapper.insertList(snapshotList) == snapshotList.size();
    }
}
