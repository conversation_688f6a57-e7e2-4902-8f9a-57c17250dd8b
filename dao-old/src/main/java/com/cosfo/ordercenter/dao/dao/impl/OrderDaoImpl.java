package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.common.util.DateUtil;
import com.cosfo.ordercenter.client.common.FulfillmentTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.mapper.OrderMapper;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.dto.SupplierOrderDTO;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryQueryParam;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryUpdateParam;
import com.cosfo.ordercenter.dao.model.param.OrderQueryParam;
import com.cosfo.ordercenter.dao.model.param.OrderStatusUpdateParam;
import com.cosfo.ordercenter.dao.model.po.*;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Service
public class OrderDaoImpl extends ServiceImpl<OrderMapper, Order> implements OrderDao {

    /**
     * 需要计算限购数量的售后状态
     */
    private final List<Integer> SALE_LIMIT_AFTER_SALE_STATUS = Arrays.asList(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
            OrderAfterSaleStatusEnum.REFUNDING.getValue(),
            OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
            OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());

    /**
     * 需要计算限购数量的订单状态
     */
    private final List<Integer> SALE_LIMIT_ORDER_STATUS = Arrays.asList(OrderStatusEnum.NO_PAYMENT.getCode(),
            OrderStatusEnum.WAIT_DELIVERY.getCode(),
            OrderStatusEnum.DELIVERING.getCode(),
            OrderStatusEnum.FINISHED.getCode(),
            OrderStatusEnum.WAITING_DELIVERY.getCode(),
            OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(),
            OrderStatusEnum.OUT_OF_STORAGE.getCode(),
            OrderStatusEnum.WAIT_AUDIT.getCode());

    /**
     * 需要更新订单完成时间状态
     */
    private final List<Integer> ORDER_FINISH_STATUS = Arrays.asList(OrderStatusEnum.FINISHED.getCode(), OrderStatusEnum.CLOSED.getCode(), OrderStatusEnum.REFUNDED.getCode());


    @Override
    public BigDecimal sumOrderTotalPrice(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return baseMapper.sumOrderTotalPrice(start, end, tenantId, storeIds);
    }

    @Override
    public Integer countPayOrderQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return baseMapper.countPayOrderQuantity(start, end, tenantId, storeIds);
    }

    @Override
    public Integer countPayOrderStoreQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return baseMapper.countPayOrderStoreQuantity(start, end, tenantId, storeIds);
    }

    @Override
    public Integer getWaitDeliveryNum(Long tenantId) {
        return baseMapper.countWaitDeliveryQuantity(tenantId);
    }

    @Override
    public Integer batchUpdateStatus(OrderStatusBatchUpdateReq updateReq) {
        return baseMapper.batchUpdateStatus(updateReq);
    }

    @Override
    public List<OrderSkuQuantity> querySkuSaleQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.querySkuSaleQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderSkuQuantity> querySkuSaleWithStoreNoQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.querySkuSaleWithStoreNoQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderSkuQuantity> querySkuSaleWithCityQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.querySkuSaleWithCityQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderDetail> queryOrderDetail(List<Long> orderIds, Long tenantId) {
        return baseMapper.queryOrderDetail(orderIds, tenantId);
    }

    @Override
    public Page<Order> queryPage(OrderOmsQueryReq orderQueryReq) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(orderQueryReq);
        orderLambdaQueryWrapper.orderByDesc(Order::getId, Order::getCombineOrderId);
        return page(new Page<>(orderQueryReq.getPageNum(), orderQueryReq.getPageSize()), orderLambdaQueryWrapper);
    }

    @Override
    public Page<Order> queryPage(OrderQueryReq orderQueryReq) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(orderQueryReq);
        orderLambdaQueryWrapper.orderByDesc(Order::getId, Order::getCombineOrderId);
        return page(new Page<>(orderQueryReq.getPageNum(), orderQueryReq.getPageSize()), orderLambdaQueryWrapper);
    }

    @Override
    public List<Order> queryList(OrderQueryReq queryParam) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(queryParam);
        orderLambdaQueryWrapper.gt(queryParam.getMaxId() != null, Order::getId, queryParam.getMaxId());
        if (Objects.nonNull(queryParam.getBatchSize())) {
            orderLambdaQueryWrapper.last("limit " + queryParam.getBatchSize());
        }
        orderLambdaQueryWrapper.orderByAsc(Order::getId);
        return list(orderLambdaQueryWrapper);
    }

    @Override
    public List<String> queryNeedDeliveryOrder(OrderDeliveryQueryParam queryParam) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, queryParam.getTenantId());
        queryWrapper.eq(Order::getStatus, OrderStatusEnum.WAITING_DELIVERY.getCode());
        queryWrapper.eq(Order::getWarehouseType, WarehouseTypeEnum.SELF_SUPPLY.getCode());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getWarehouseNoList()), Order::getWarehouseNo, queryParam.getWarehouseNoList());
        queryWrapper.le(queryParam.getPayTime() != null, Order::getPayTime, queryParam.getPayTime());
        queryWrapper.le(queryParam.getCreateTime() != null, Order::getCreateTime, queryParam.getCreateTime());
        List<Order> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(Order::getOrderNo).collect(Collectors.toList());
    }

    @Override
    public List<Order> queryByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Order::getOrderNo, orderNos);
        return list(queryWrapper);
    }

    @Override
    public Boolean updateById(OrderDTO orderDTO) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, orderDTO.getId());
        updateWrapper.set(orderDTO.getFinishedTime() != null, Order::getFinishedTime, orderDTO.getFinishedTime());
        updateWrapper.set(orderDTO.getPayType() != null, Order::getPayType, orderDTO.getPayType());
        updateWrapper.set(orderDTO.getOnlinePayChannel() != null, Order::getOnlinePayChannel, orderDTO.getOnlinePayChannel());
        return update(updateWrapper);
    }

    @Override
    public Boolean updateStatus(OrderStatusUpdateReq updateReq) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, updateReq.getOrderId());
        updateWrapper.eq(updateReq.getOriginStatus() != null, Order::getStatus, updateReq.getOriginStatus());
        updateWrapper.eq(updateReq.getTenantId() != null, Order::getTenantId, updateReq.getTenantId());

        updateWrapper.set(Order::getStatus, updateReq.getStatus());

        if (ORDER_FINISH_STATUS.contains(updateReq.getStatus())) {
            updateWrapper.setSql("finished_time = IFNULL(finished_time,now())");
        }

        if (OrderStatusEnum.DELIVERING.getCode().equals(updateReq.getStatus())) {
            updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
        }
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateReq.getStatus())) {
            updateWrapper.set(Order::getBeginDeliveryTime, LocalDateTime.now());
            if (updateReq.getDeliveryTime() != null) {
                updateWrapper.set(Order::getDeliveryTime, updateReq.getDeliveryTime());
            }
        }
        if (OrderStatusEnum.WAIT_DELIVERY.getCode().equals(updateReq.getStatus())
                || OrderStatusEnum.WAIT_AUDIT.getCode().equals(updateReq.getStatus())
                || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateReq.getStatus())) {
            updateWrapper.set(Order::getPayTime, LocalDateTime.now());
            updateWrapper.setSql("total_price = payable_price");
        }
        return update(updateWrapper);
    }

    @Override
    public Boolean updateStatusByOriginStatus(OrderStatusUpdateParam updateParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, updateParam.getOrderId());
        updateWrapper.in(!CollectionUtils.isEmpty(updateParam.getOriginStatusList()), Order::getStatus, updateParam.getOriginStatusList());
        updateWrapper.eq(updateParam.getTenantId() != null, Order::getTenantId, updateParam.getTenantId());

        updateWrapper.set(Order::getStatus, updateParam.getStatus());

        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateParam.getStatus())) {
            updateWrapper.set(Order::getBeginDeliveryTime, LocalDateTime.now());
        }

        return update(updateWrapper);
    }

    @Override
    public Boolean updatePayType(OrderDTO orderDTO) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, orderDTO.getId());
        updateWrapper.set(Order::getPayType, orderDTO.getPayType());
        updateWrapper.set(Order::getOnlinePayChannel, orderDTO.getOnlinePayChannel());
        return update(updateWrapper);
    }

    @Override
    public Boolean selfLifting(OrderSelfLiftReq req) {
        Order order = getById(req.getOrderId());
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, order.getId());
        updateWrapper.set(Order::getStatus, OrderStatusEnum.DELIVERING.getCode());
        // 三方仓订单 城配履约 自提更新为已完成，快递履约是待收货
        if (order.getWarehouseType() != 2 && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(order.getFulfillmentType())) {
            updateWrapper.set(Order::getStatus, OrderStatusEnum.FINISHED.getCode());
            updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
            updateWrapper.set(Order::getFinishedTime, LocalDateTime.now());
        }
        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchUpdateProfitSharingFinishTime(List<Order> orderList) {
        return updateBatchById(orderList);
    }

    @Override
    public List<Order> queryByCombineId(Long combineId, Long tenantId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, tenantId);
        queryWrapper.eq(Order::getCombineOrderId, combineId);
        return list(queryWrapper);
    }

    @Override
    public List<Order> queryByCombineIds(Set<Long> combineIds, Long tenantId) {
        if (CollectionUtils.isEmpty(combineIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, tenantId);
        queryWrapper.in(Order::getCombineOrderId, combineIds);
        return list(queryWrapper);
    }

    @Override
    public Integer countOrderQuantity(OrderCountReq orderCountReq) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, orderCountReq.getTenantId());
        queryWrapper.in(!CollectionUtils.isEmpty(orderCountReq.getStatusList()), Order::getStatus, orderCountReq.getStatusList());
        queryWrapper.eq(orderCountReq.getStoreId() != null, Order::getStoreId, orderCountReq.getStoreId());
        return Long.valueOf(count(queryWrapper)).intValue();
    }

    @Override
    public List<Order> queryNeedAutoFinishedOrder(OrderAutoFinishDTO orderAutoFinishDTO) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Order::getId);
        queryWrapper.le(Order::getDeliveryTime, orderAutoFinishDTO.getDeliveryTimeEnd());
        queryWrapper.ge(Order::getDeliveryTime, orderAutoFinishDTO.getDeliveryTimeStart());
        queryWrapper.eq(Order::getStatus, OrderStatusEnum.DELIVERING.getCode());
        queryWrapper.orderByAsc(Order::getDeliveryTime);
        queryWrapper.last("limit " + orderAutoFinishDTO.getLimit());
        return list(queryWrapper);
    }

    @Override
    public int orderFinish(List<Long> orderIds) {
        return baseMapper.batchUpdateStatusByOrderIds(orderIds, Collections.singletonList(OrderStatusEnum.DELIVERING.getCode()), OrderStatusEnum.FINISHED.getCode());
    }

    @Override
    public boolean updateDeliveryTime(OrderDeliveryUpdateParam param) {
        if(param.getDeliveryTime() == null && param.getFulfillmentNo() == null){
            return true;
        }
        Order order = this.getById(param.getOrderId());
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, param.getOrderId());
        updateWrapper.in(Order::getStatus, param.getOriginStatusList());
        // 只有三方仓订单需要更新配送时间
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(order.getWarehouseType())) {
            updateWrapper.set(param.getDeliveryTime() != null, Order::getDeliveryTime, param.getDeliveryTime());
        }
        updateWrapper.set(param.getFulfillmentNo() != null, Order::getFulfillmentNo, param.getFulfillmentNo());
        return update(updateWrapper);
    }

    private static MPJLambdaWrapper<Order> getOrderLambdaQueryWrapper(OrderQueryReq orderQueryReq) {
        if (orderQueryReq.getDeliveryTime() != null) {
            orderQueryReq.setDeliveryStartTime(DateUtil.startOfDay(orderQueryReq.getDeliveryTime()));
            orderQueryReq.setDeliveryEndTime(DateUtil.endOfDay(orderQueryReq.getDeliveryTime()));
        }
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(orderQueryReq.getTenantId() != null, Order::getTenantId, orderQueryReq.getTenantId());
        queryWrapper.eq(orderQueryReq.getStatus() != null, Order::getStatus, orderQueryReq.getStatus());
        queryWrapper.eq(orderQueryReq.getWarehouseType() != null, Order::getWarehouseType, orderQueryReq.getWarehouseType());
        queryWrapper.eq(orderQueryReq.getOrderNo() != null, Order::getOrderNo, orderQueryReq.getOrderNo());
        queryWrapper.eq(orderQueryReq.getWarehouseNo() != null, Order::getWarehouseNo, orderQueryReq.getWarehouseNo());
        queryWrapper.eq(orderQueryReq.getPayType() != null, Order::getPayType, orderQueryReq.getPayType());
        queryWrapper.eq(orderQueryReq.getOrderId() != null, Order::getId, orderQueryReq.getOrderId());
        queryWrapper.eq(orderQueryReq.getOrderSource() != null, Order::getOrderSource, orderQueryReq.getOrderSource());
        queryWrapper.eq(orderQueryReq.getFulfillmentType() != null, Order::getFulfillmentType, orderQueryReq.getFulfillmentType());
        queryWrapper.eq(StringUtils.isNotBlank(orderQueryReq.getPlanOrderNo()), Order::getPlanOrderNo, orderQueryReq.getPlanOrderNo());
        queryWrapper.between(orderQueryReq.getDeliveryStartTime() != null && orderQueryReq.getDeliveryEndTime() != null, Order::getDeliveryTime, orderQueryReq.getDeliveryStartTime(), orderQueryReq.getDeliveryEndTime());
        queryWrapper.between(orderQueryReq.getCreateStartTime() != null && orderQueryReq.getCreateEndTime() != null, Order::getCreateTime, orderQueryReq.getCreateStartTime(), orderQueryReq.getCreateEndTime());
        queryWrapper.between(orderQueryReq.getStartTime() != null && orderQueryReq.getEndTime() != null, Order::getFinishedTime, orderQueryReq.getStartTime(), orderQueryReq.getEndTime());
        queryWrapper.ne(orderQueryReq.getNeOrderId() != null, Order::getId, orderQueryReq.getNeOrderId());
        queryWrapper.eq(orderQueryReq.getSupplierTenantId() != null, Order::getSupplierTenantId, orderQueryReq.getSupplierTenantId());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStoreIds()), Order::getStoreId, orderQueryReq.getStoreIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStatusList()), Order::getStatus, orderQueryReq.getStatusList());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getAccountIds()), Order::getAccountId, orderQueryReq.getAccountIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getTenantIds()), Order::getTenantId, orderQueryReq.getTenantIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getOrderNos()), Order::getOrderNo, orderQueryReq.getOrderNos());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getCustomerOrderIds()), Order::getCustomerOrderId, orderQueryReq.getCustomerOrderIds());
        boolean orderItemJoinFlag = false;
        boolean orderItemSnapshotJoinFlag = false;
        if (!CollectionUtils.isEmpty(orderQueryReq.getOrderItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getId, orderQueryReq.getOrderItemIds());
        }

        if (!CollectionUtils.isEmpty(orderQueryReq.getItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getItemId, orderQueryReq.getItemIds());
        }

        if (!CollectionUtils.isEmpty(orderQueryReq.getSupplierTenantIds())) {
            orderItemSnapshotJoinFlag = true;
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItemSnapshot::getSupplierTenantId, orderQueryReq.getSupplierTenantIds());
        }

        if (orderItemJoinFlag) {
            queryWrapper.leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId);
        }

        if (orderItemSnapshotJoinFlag) {
            queryWrapper.leftJoin(OrderItemSnapshot.class, OrderItemSnapshot::getOrderItemId, OrderItem::getId);
        }
        queryWrapper.groupBy(Order::getId);
        return queryWrapper;
    }

    private static MPJLambdaWrapper<Order> getOrderLambdaQueryWrapper(OrderOmsQueryReq orderQueryReq) {
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(orderQueryReq.getTenantId() != null, Order::getTenantId, orderQueryReq.getTenantId());
        queryWrapper.eq(orderQueryReq.getStatus() != null, Order::getStatus, orderQueryReq.getStatus());
        queryWrapper.eq(orderQueryReq.getWarehouseType() != null, Order::getWarehouseType, orderQueryReq.getWarehouseType());
        queryWrapper.eq(orderQueryReq.getOrderNo() != null, Order::getOrderNo, orderQueryReq.getOrderNo());
        queryWrapper.eq(orderQueryReq.getWarehouseNo() != null, Order::getWarehouseNo, orderQueryReq.getWarehouseNo());
        queryWrapper.eq(orderQueryReq.getPayType() != null, Order::getPayType, orderQueryReq.getPayType());
        queryWrapper.eq(orderQueryReq.getSupplierTenantId() != null, Order::getSupplierTenantId, orderQueryReq.getSupplierTenantId());
        queryWrapper.between(orderQueryReq.getCreateStartTime() != null && orderQueryReq.getCreateEndTime() != null, Order::getCreateTime, orderQueryReq.getCreateStartTime(), orderQueryReq.getCreateEndTime());
        queryWrapper.between(orderQueryReq.getStartTime() != null && orderQueryReq.getEndTime() != null, Order::getFinishedTime, orderQueryReq.getStartTime(), orderQueryReq.getEndTime());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStoreIds()), Order::getStoreId, orderQueryReq.getStoreIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStatusList()), Order::getStatus, orderQueryReq.getStatusList());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getAccountIds()), Order::getAccountId, orderQueryReq.getAccountIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getTenantIds()), Order::getTenantId, orderQueryReq.getTenantIds());
        queryWrapper.eq(orderQueryReq.getCustomerOrderId() != null, Order::getCustomerOrderId, orderQueryReq.getCustomerOrderId());
        boolean orderItemJoinFlag = false;
        if (!CollectionUtils.isEmpty(orderQueryReq.getOrderItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getId, orderQueryReq.getOrderItemIds());
        }

        if (!CollectionUtils.isEmpty(orderQueryReq.getItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getItemId, orderQueryReq.getItemIds());
        }
        if (orderItemJoinFlag) {
            queryWrapper.leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId);
        }
        if (orderItemJoinFlag) {
            queryWrapper.groupBy(Order::getId);
        }
        return queryWrapper;

    }

    @Override
    public Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime) {
        return baseMapper.updateOrderDeliveryTime(orderNos, deliveryTime);
    }

    @Override
    public boolean selfLiftingFinish(OrderSelfLiftingFinishReq req) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, req.getOrderId());

        updateWrapper.set(Order::getStatus, OrderStatusEnum.FINISHED.getCode());
        updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
        updateWrapper.setSql("finished_time = IFNULL(finished_time,now())");
        return update(updateWrapper);
    }

    @Override
    public List<Order> queryByStoreIdAndDeliveryTime(Long storeId, LocalDateTime deliveryTime, Integer warehouseType) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStoreId, storeId);
        queryWrapper.eq(Order::getDeliveryTime, deliveryTime);
        queryWrapper.eq(Order::getWarehouseType, warehouseType);
        return list(queryWrapper);
    }

    @Override
    public List<Order> queryListByCustomerOrderIds(OrderQueryParam orderQueryParam) {
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(orderQueryParam.getTenantId() != null, Order::getTenantId, orderQueryParam.getTenantId());
        queryWrapper.in( Order::getCustomerOrderId, orderQueryParam.getCustomerOrderIds());
        return list(queryWrapper);
    }

    @Override
    public List<OrderItemSaleQuantity> queryOrderItemSaleQuantity(ItemSaleQuantityReq saleQuantityReq) {
        return baseMapper.queryOrderItemSaleQuantity(saleQuantityReq.getStartDay(),
                saleQuantityReq.getEndDay(),
                saleQuantityReq.getTenantId(),
                saleQuantityReq.getMerchantStoreId(),
                saleQuantityReq.getItemIds(),
                SALE_LIMIT_AFTER_SALE_STATUS,
                SALE_LIMIT_ORDER_STATUS);
    }

    @Override
    public Order updateDeliveryFee(Long orderId, BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee) {
        Order order = getById(orderId);
        if (Objects.isNull(order)) {
            throw new BizException("订单不存在！");
        }
        if (!OrderStatusEnum.NO_PAYMENT.getCode().equals(order.getStatus())){
            throw new BizException("该笔订单状态异常！请刷新页面");
        }
        if (order.getDeliveryFee().compareTo(oriDeliveryFee) != 0){
            throw new BizException("该笔订单运费已刷新，请刷新页面！");
        }
        BigDecimal subFee = order.getDeliveryFee().subtract(newDeliveryFee);
        order.setDeliveryFee(newDeliveryFee);
        order.setPayablePrice(order.getPayablePrice().subtract(subFee));
        order.setUpdateTime(LocalDateTime.now());
        updateById(order);
        return order;
    }

    @Override
    public List<SupplierOrderDTO> querySupplierOrderList(SupplierOrderTotalReq supplierOrderTotalReq) {
        return baseMapper.querySupplierOrderList(supplierOrderTotalReq);
    }

    @Override
    public Boolean updateOrderCustomerOrderId(Long orderId, String newCustomerOrderId) {
        return baseMapper.updateOrderCustomerOrderId(orderId, newCustomerOrderId) > 0;
    }
}
