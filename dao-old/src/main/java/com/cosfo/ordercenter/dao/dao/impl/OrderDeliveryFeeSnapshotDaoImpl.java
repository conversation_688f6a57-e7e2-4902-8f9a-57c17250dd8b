package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.OrderDeliveryFeeSnapshotDao;
import com.cosfo.ordercenter.dao.mapper.OrderDeliveryFeeSnapshotMapper;
import com.cosfo.ordercenter.dao.model.po.OrderDeliveryFeeSnapshot;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

/**
 * 下单运费快照(OrderDeliveryFeeSnapshot)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:46:12
 */
@Service
public class OrderDeliveryFeeSnapshotDaoImpl extends ServiceImpl<OrderDeliveryFeeSnapshotMapper, OrderDeliveryFeeSnapshot> implements OrderDeliveryFeeSnapshotDao {

    @Override
    public boolean updateEffectiveFlag(Long orderId, Integer scence) {
        LambdaUpdateWrapper<OrderDeliveryFeeSnapshot> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderDeliveryFeeSnapshot::getEffectiveFlag, NumberUtils.INTEGER_ZERO);
        updateWrapper.eq(OrderDeliveryFeeSnapshot::getOrderId, orderId);
        updateWrapper.eq(OrderDeliveryFeeSnapshot::getScene, scence);
        return update(updateWrapper);
    }
}
