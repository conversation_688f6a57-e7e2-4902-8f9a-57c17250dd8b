package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.mapper.OrderItemMapper;
import com.cosfo.ordercenter.dao.model.param.OrderItemStatusBatchUpdateParam;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 订单详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Service
public class OrderItemDaoImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemDao {

    @Override
    public List<OrderItemWithSnapshot> batchQueryOrderItemDetail(List<Long> orderIds, List<Long> supplierIds) {
        return baseMapper.batchQueryOrderItemDetail(orderIds, supplierIds);
    }

    @Override
    public Integer querySkuQuantity(Long tenantId, List<Long> orderIds) {
        return baseMapper.querySkuQuantity(tenantId, orderIds);
    }

    @Override
    public Integer querySaleQuantity(Long tenantId, List<Long> orderIds) {
        return baseMapper.querySaleQuantity(tenantId, orderIds);
    }

    @Override
    public int updateDeliveryQuantity(Long orderItemId, Integer quantity) {
        return baseMapper.updateDeliveryQuantity(orderItemId, quantity);
    }

    @Override
    public List<OrderItem> queryByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderItem::getOrderId, orderId);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<OrderItem> batchQueryByOrderIds(List<Long> orderIds) {
        LambdaQueryWrapper<OrderItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(OrderItem::getOrderId, orderIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public Boolean updateAfterSaleExpiryTime(OrderItemUpdateReq dto) {
        LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderItem::getAfterSaleExpiryTime, dto.getAfterSaleExpiryTime());
        updateWrapper.eq(OrderItem::getId, dto.getOrderItemId());
        return update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchUpdateAfterSaleExpiryTime(List<OrderItem> orderItemList) {
        return updateBatchById(orderItemList);
    }

//    @Override
//    public OrderItemWithSnapshot queryDetailById(Long orderItemId) {
//        OrderItem orderItem = getById(orderItemId);
//
//        return null;
//    }

    @Override
    public List<OrderItem> batchQuery(Long tenantId, List<Long> orderItemIds) {
        if (tenantId == null && CollectionUtils.isEmpty(orderItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), OrderItem::getTenantId, tenantId);
        queryWrapper.in(!CollectionUtils.isEmpty(orderItemIds), OrderItem::getId, orderItemIds);
        return list(queryWrapper);
    }

    @Override
    public boolean batchSave(List<OrderItem> orderItemList) {
        return baseMapper.batchSave(orderItemList) == orderItemList.size();
    }

    @Override
    public Boolean updateStatus(OrderItemStatusUpdateReq req) {
        LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderItem::getId, req.getOrderItemId());
        updateWrapper.set(OrderItem::getStatus, req.getStatus());
        return update(updateWrapper);
    }

    @Override
    public List<OrderItem> queryByIds(List<Long> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItem::getId, orderItemIds);
        return list(queryWrapper);
    }

    @Override
    public boolean batchUpdateStatus(OrderItemStatusBatchUpdateParam updateParam) {
        LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(updateParam.getTenantId() != null, OrderItem::getTenantId, updateParam.getTenantId());
        updateWrapper.in(OrderItem::getOrderId, updateParam.getOrderIds());

        updateWrapper.set(OrderItem::getStatus, updateParam.getStatus());
        updateWrapper.set(updateParam.getStoreNo() != null, OrderItem::getStoreNo, updateParam.getStoreNo());
        return update(updateWrapper);
    }


    @Override
    public boolean updateStoreNo(Long orderId, Integer sourceStoreNo, Integer storeNo) {
        LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(sourceStoreNo != null, OrderItem::getStoreNo, sourceStoreNo);
        updateWrapper.eq(OrderItem::getOrderId, orderId);

        updateWrapper.set(storeNo != null, OrderItem::getStoreNo, storeNo);
        return update(updateWrapper);
    }

    @Override
    public List<OrderItemWithSnapshot> queryOrderItemVOByOrderIds(Long tenantId, List<Long> orderIds) {
        return baseMapper.queryOrderItemVOByOrderIds(tenantId,orderIds);
    }
}
