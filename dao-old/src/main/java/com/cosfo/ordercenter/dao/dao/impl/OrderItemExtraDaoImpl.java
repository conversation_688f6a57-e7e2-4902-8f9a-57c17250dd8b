package com.cosfo.ordercenter.dao.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.OrderItemExtraDao;
import com.cosfo.ordercenter.dao.mapper.OrderItemExtraMapper;
import com.cosfo.ordercenter.dao.model.param.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-09-22
 * @Description:
 */
@Service
public class OrderItemExtraDaoImpl extends ServiceImpl<OrderItemExtraMapper, OrderItemExtra> implements OrderItemExtraDao {

    @Override
    public void saveOrderItemExtra(OrderItemExtra orderItemExtra) {
        save(orderItemExtra);
    }

    @Override
    public boolean batchSave(List<OrderItemExtra> orderItemExtraList) {
        if (CollectionUtils.isEmpty(orderItemExtraList)) {
            return false;
        }
        return orderItemExtraList.size() == baseMapper.batchSave(orderItemExtraList);
    }

    @Override
    public List<OrderItemExtra> queryList(OrderItemExtraQueryParam param) {
        LambdaQueryWrapper<OrderItemExtra> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), OrderItemExtra::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getOrderId()), OrderItemExtra::getOrderId, param.getOrderId());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getCustomerOrderItemIdList()), OrderItemExtra::getCustomerOrderItemId, param.getCustomerOrderItemIdList());
        return list(queryWrapper);
    }
}
