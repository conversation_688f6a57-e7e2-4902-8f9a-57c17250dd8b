package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.dao.mapper.OrderItemSnapshotMapper;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Service
public class OrderItemSnapshotDaoImpl extends ServiceImpl<OrderItemSnapshotMapper, OrderItemSnapshot> implements OrderItemSnapshotDao {

    @Override
    public List<OrderItemSnapshot> queryList(OrderItemSnapshotQueryReq queryReq) {
        LambdaQueryWrapper<OrderItemSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderItemSnapshot::getTenantId, queryReq.getTenantId());
        queryWrapper.eq(Objects.nonNull(queryReq.getOrderId()), OrderItemSnapshot::getOrderId, queryReq.getOrderId());
        queryWrapper.in(!CollectionUtils.isEmpty(queryReq.getSupplierIds()), OrderItemSnapshot::getSupplierTenantId, queryReq.getSupplierIds());
        List<OrderItemSnapshot> list = list(queryWrapper);
        return list;
    }

    @Override
    public List<OrderItemSnapshot> queryByOrderItemIds(List<Long> orderItemIds) {
        if(CollectionUtils.isEmpty(orderItemIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItemSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItemSnapshot::getOrderItemId, orderItemIds);
        return list(queryWrapper);
    }

    @Override
    public List<OrderItemSnapshot> queryByOrderIds(List<Long> orderIds) {
        if(CollectionUtils.isEmpty(orderIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderItemSnapshot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItemSnapshot::getOrderId, orderIds);
        return list(queryWrapper);
    }

    @Override
    public OrderItemSnapshot queryByOrderItemId(Long orderItemId) {
        LambdaQueryWrapper<OrderItemSnapshot> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrderItemSnapshot::getOrderItemId, orderItemId);
        OrderItemSnapshot orderItemSnapshot = getOne(lambdaQueryWrapper);
        return orderItemSnapshot;
    }

    @Override
    public Boolean batchUpdateTaskId(List<Long> orderItemIds, Long orderId) {
        LambdaUpdateWrapper<OrderItemSnapshot> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(OrderItemSnapshot::getOrderItemId, orderItemIds);
        lambdaUpdateWrapper.set(OrderItemSnapshot::getOrderId, orderId);
        return update(lambdaUpdateWrapper);
    }

    @Override
    public boolean batchSave(List<OrderItemSnapshot> snapshotList) {
        if (CollectionUtils.isEmpty(snapshotList)) {
            return false;
        }
        return snapshotList.size() == baseMapper.batchSave(snapshotList);
    }
}
