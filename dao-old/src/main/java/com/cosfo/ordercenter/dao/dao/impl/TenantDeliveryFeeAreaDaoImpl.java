package com.cosfo.ordercenter.dao.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.dao.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.ordercenter.dao.mapper.TenantDeliveryFeeAreaMapper;
import com.cosfo.ordercenter.dao.model.po.TenantDeliveryFeeArea;
import org.springframework.stereotype.Service;


/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/15
 */
@Service
public class TenantDeliveryFeeAreaDaoImpl extends ServiceImpl<TenantDeliveryFeeAreaMapper, TenantDeliveryFeeArea> implements TenantDeliveryFeeAreaDao {

    @Override
    public TenantDeliveryFeeArea queryAreaRule(Long tenantId, String province, String city, String area) {
        LambdaQueryWrapper<TenantDeliveryFeeArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantDeliveryFeeArea::getTenantId, tenantId);
        queryWrapper.eq(TenantDeliveryFeeArea::getProvince, province);
        queryWrapper.eq(TenantDeliveryFeeArea::getCity, city);
        queryWrapper.eq(TenantDeliveryFeeArea::getArea, area);
        return getOne(queryWrapper);
    }
}
