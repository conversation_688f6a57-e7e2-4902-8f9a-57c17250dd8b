package com.cosfo.ordercenter.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OrderAfterSaleRuleMapper extends BaseMapper<OrderAfterSaleRule> {
    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);
    int deleteByPrimaryKeys(List<Long> list);

    /**
     * 新增售后规则
     *
     * @param record
     * @return
     */
    int insert(OrderAfterSaleRule record);
    int batchInsert(List<OrderAfterSaleRule> list);


    /**
     * 新增售后规则
     *
     * @param record
     * @return
     */
    int insertSelective(OrderAfterSaleRule record);

    /**
     * 根据Id查询售后规则
     *
     * @param id
     * @return
     */
    OrderAfterSaleRule selectByPrimaryKey(Long id);

    /**
     * 更新售后规则
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderAfterSaleRule record);

    /**
     * 更新售后规则
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderAfterSaleRule record);

    List<OrderAfterSaleRule> queryByTenantId(Long tenantId);

    /**
     * 查询售后规则
     *
     * @return
     */
    List<OrderAfterSaleRule> listAll();
}