package com.cosfo.ordercenter.dao.mapper;

import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface OrderItemSnapshotMapper extends BaseMapper<OrderItemSnapshot> {

    /**
     * 批量保存
     * @param snapshotList
     * @return
     */
    int batchSave(@Param("snapshotList") List<OrderItemSnapshot> snapshotList);

}
