package com.cosfo.ordercenter.dao.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderAutoFinishTaskParamDTO implements Serializable {

    /**
     * 配送日期左偏移量
     */
    private Integer deliveryDayStartOffset;

    /**
     * 配送日期右偏移
     */
    private Integer deliveryDayEndOffset;

    /**
     * 单次执行订单数量
     */
    private Integer limit;
}
