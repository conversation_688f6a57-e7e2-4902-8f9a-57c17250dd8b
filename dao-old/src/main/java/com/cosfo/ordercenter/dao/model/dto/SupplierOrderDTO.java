package com.cosfo.ordercenter.dao.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: fansongsong
 * @Date: 2023-10-19
 * @Description:
 */
@Data
public class SupplierOrderDTO implements Serializable {

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 订单子项
     */
    private Long orderItemId;

    /**
     * 订单子项
     */
    private Integer amount;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 供应商ID
     */
    private Long supplierId;
}
