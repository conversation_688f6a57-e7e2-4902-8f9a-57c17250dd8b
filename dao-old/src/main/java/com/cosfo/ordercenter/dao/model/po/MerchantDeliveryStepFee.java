package com.cosfo.ordercenter.dao.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阶梯运费(MerchantDeliveryStepFee)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:41:43
 */
@Data
@TableName(value = "merchant_delivery_step_fee", autoResultMap = true)
public class MerchantDeliveryStepFee implements Serializable {
    private static final long serialVersionUID = 119720805987218687L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 运费规则ID
     */
    private Long ruleId;
    /**
     * 门槛类型  0-金额 1-数量 2-重量
     */
    private Integer feeRule;
    /**
     * 阶梯门槛。即满xx元/件/kg
     */
    private BigDecimal stepThreshold;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;


    /**
     * 运费计算方式 0-固定运费为delivery_fee 1-动态计算
     */
    @TableField("cal_type")
    private Integer calType;

    /**
     * 运费动态计算规则 json
     */
    @TableField(value = "deliveryfee_cal_rule", typeHandler = JacksonTypeHandler.class)
    private DeliveryFeeCalRule deliveryfeeCalRule;

}

