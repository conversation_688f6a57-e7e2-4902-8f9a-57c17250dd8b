package com.cosfo.ordercenter.dao.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 售后订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderAfterSale implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 订单项Id
     */
    private Long orderItemId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 账户ID
     */
    private Long accountId;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 售后类型 0 已到货 1 未到货
     */
    private Integer afterSaleType;

    /**
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发
     */
    private Integer serviceType;

    /**
     * 申请金额
     */
    private BigDecimal applyPrice;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;

    /**
     * 配送费
     */
    private BigDecimal deliveryFee;

    /**
     * 售后原因
     */
    private String reason;

    /**
     * 客户留言
     */
    private String userRemark;

    /**
     * 售后凭证照片
     */
    private String proofPicture;

    /**
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消
系统内部状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款9 三方处理中
     */
    private Integer status;

    /**
     * 处理结果
     */
    private String handleRemark;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 审核时间
     */
    private LocalDateTime handleTime;

    /**
     * 处理时间（回收、补发时间）
     */
    private LocalDateTime recycleTime;

    /**
     * 回收详情
     */
    private String recycleDetails;

    /**
     * 责任类型0供应商1品牌方2门店
     */
    private Integer responsibilityType;
    /**
     * 城配仓 编码
     */
    private Integer storeNo;
    /**
     * 配送仓类型 0,自营仓1优选仓
     */
    private Integer warehouseType;

    /**
     * 自动完成时间
     */
    private LocalDateTime autoFinishedTime;

    /**
     * 申请数量
     */
    private Integer applyQuantity;

    /**
     * 后台售后备注
     */
    private String adminRemark;

    /**
     * 后台售后备注更新时间
     */
    private LocalDateTime adminRemarkTime;

    /**
     * 退货二次审核说明
     */
    private String secondHandleRemark;

    /**
     * 退回地址Id
     */
    private Long returnAddressId;

    /**
     * 自营退货库存仓no
     */
    private String returnWarehouseNo;

    /**
     * 外部系统售后单号
     */
    private String customerAfterSaleOrderNo;

    /**
     * 服务商审核时间
     */
    private LocalDateTime serviceProviderAuditTime;

    /**
     * 回收凭证照片
     */
    private String recyclePicture;

    /**
     * 回收数量明细
     */
    private String recycleQuantityDetail;

    /**
     *  退款凭证
     */
    private String refundReceipt;
}
