package com.cosfo.ordercenter.dao.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 下单运费快照(OrderDeliveryFeeSnapshot)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:46:12
 */
@Data
@TableName("order_delivery_fee_snapshot")
public class OrderDeliveryFeeSnapshot implements Serializable {
    private static final long serialVersionUID = -46348791476110468L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单运费
     */
    private BigDecimal orderDeliveryFee;
    /**
     * 订单信息
     */
    private String orderInfo;
    /**
     * 规则列表
     */
    private String ruleInfo;
    /**
     * 命中规则的运费
     */
    private String hitRuleFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;


    /**
     * 计算运费场景 1-下单 2-发货前售后
     */
    private Integer scene;

    /**
     * 有效标识 1-有效 0-无效
     */
    private Integer effectiveFlag;

}

