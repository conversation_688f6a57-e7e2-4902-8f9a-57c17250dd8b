package com.cosfo.ordercenter.dao.model.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * tenant_delivery_fee_rule
 *
 * <AUTHOR>
@Data
public class TenantDeliveryFeeRule implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 0、采用鲜沐运费 1、免运费 2,自定义
     */
    private Integer type;

    /**
     * 默认运费金额
     */
    private BigDecimal defaultPrice;

    /**
     * 满减限制
     */
    private BigDecimal freeNumber;

    /**
     * 满减类型 0按金额 1按限制
     */
    private Integer freeType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
