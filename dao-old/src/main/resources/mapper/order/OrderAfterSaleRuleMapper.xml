<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.dao.mapper.OrderAfterSaleRuleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="apply_end_time" jdbcType="INTEGER" property="applyEndTime" />
    <result column="auto_finished_time" jdbcType="INTEGER" property="autoFinishedTime" />
    <result column="deal_type" jdbcType="TINYINT" property="dealType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="type" property="type"/>
    <result column="default_flag" property="defaultFlag"/>
    <result column="rule" property="rule"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, delivery_type, apply_end_time, auto_finished_time, deal_type, create_time,
    update_time, type, default_flag, rule
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_after_sale_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="queryByTenantId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_after_sale_rule
    where tenant_id = #{tenantId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_after_sale_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByPrimaryKeys"  parameterType="list">
    delete from order_after_sale_rule
    where id in
    <foreach collection="list" open="(" close=")" separator="," item="id">
      #{id}
    </foreach>
  </delete>
  <insert id="batchInsert" parameterType="list">
    insert into order_after_sale_rule (tenant_id, delivery_type, apply_end_time,
    auto_finished_time, deal_type, type, default_flag, rule)
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.tenantId,jdbcType=BIGINT}, #{item.deliveryType,jdbcType=TINYINT}, #{item.applyEndTime,jdbcType=INTEGER},
      #{item.autoFinishedTime,jdbcType=INTEGER}, #{item.dealType,jdbcType=TINYINT}, #{type}, #{defaultFlag}, #{rule})
    </foreach>
  </insert>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule" useGeneratedKeys="true">
    insert into order_after_sale_rule (tenant_id, delivery_type, apply_end_time,
      auto_finished_time, deal_type, create_time,
      update_time, type, default_flag, rule)
    values (#{tenantId,jdbcType=BIGINT}, #{deliveryType,jdbcType=TINYINT}, #{applyEndTime,jdbcType=INTEGER},
      #{autoFinishedTime,jdbcType=INTEGER}, #{dealType,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},  #{type}, #{defaultFlag}, #{rule})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule" useGeneratedKeys="true">
    insert into order_after_sale_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="deliveryType != null">
        delivery_type,
      </if>
      <if test="applyEndTime != null">
        apply_end_time,
      </if>
      <if test="autoFinishedTime != null">
        auto_finished_time,
      </if>
      <if test="dealType != null">
        deal_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="defaultFlag != null">
        default_flag,
      </if>
      <if test="rule != null">
        rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="applyEndTime != null">
        #{applyEndTime,jdbcType=INTEGER},
      </if>
      <if test="autoFinishedTime != null">
        #{autoFinishedTime,jdbcType=INTEGER},
      </if>
      <if test="dealType != null">
        #{dealType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag},
      </if>
      <if test="rule != null">
        #{rule},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule">
    update order_after_sale_rule
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="deliveryType != null">
        delivery_type = #{deliveryType,jdbcType=TINYINT},
      </if>
      <if test="applyEndTime != null">
        apply_end_time = #{applyEndTime,jdbcType=INTEGER},
      </if>
      <if test="autoFinishedTime != null">
        auto_finished_time = #{autoFinishedTime,jdbcType=INTEGER},
      </if>
      <if test="dealType != null">
        deal_type = #{dealType,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag},
      </if>
      <if test="rule != null">
        rule = #{rule},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule">
    update order_after_sale_rule
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      delivery_type = #{deliveryType,jdbcType=TINYINT},
      apply_end_time = #{applyEndTime,jdbcType=INTEGER},
      auto_finished_time = #{autoFinishedTime,jdbcType=INTEGER},
      deal_type = #{dealType,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      type = #{type},
      default_flag = #{defaultFlag},
      rule = #{rule}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    order_after_sale_rule
  </select>
</mapper>