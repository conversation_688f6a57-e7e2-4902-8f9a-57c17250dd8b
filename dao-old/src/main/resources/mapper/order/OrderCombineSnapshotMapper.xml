<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.dao.mapper.OrderCombineSnapshotMapper">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        combine_order_id,
        combine_item_id,
        item_id,
        quantity,
        original_price,
        order_item_id,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.cosfo.ordercenter.dao.model.po.OrderCombineSnapshot">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="combine_order_id" property="combineOrderId"/>
        <result column="combine_item_id" property="combineItemId"/>
        <result column="item_id" property="itemId"/>
        <result column="quantity" property="quantity"/>
        <result column="original_price" property="originalPrice"/>
        <result column="order_item_id" property="orderItemId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

<!--auto generated by MybatisCodeHelper on 2023-08-19-->
    <insert id="insertList">
        INSERT INTO order_combine_snapshot(
        tenant_id,
        combine_order_id,
        combine_item_id,
        item_id,
        quantity,
        original_price,
        order_item_id
        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.tenantId},
            #{element.combineOrderId},
            #{element.combineItemId},
            #{element.itemId},
            #{element.quantity},
            #{element.originalPrice},
            #{element.orderItemId}
            )
        </foreach>
    </insert>
</mapper>
