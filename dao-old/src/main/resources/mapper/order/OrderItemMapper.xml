<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.dao.mapper.OrderItemMapper">
    <select id="batchQueryOrderItemDetail" resultType="com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot">
        select
        oi.order_id orderId,
        oi.item_id itemId,
        oi.amount amount,
        oi.delivery_quantity deliveryQuantity,
        oi.payable_price payablePrice,
        oi.total_price totalPrice,
        oi.item_id itemId,
        oi.id orderItemId,
        oi.after_sale_expiry_time afterSaleExpiryTime,
        oi.store_no storeNo,
        oi.tenant_id tenantId,
        oi.status status,
        oi.order_type orderType,
        ois.item_code,
        ois.supplier_name supplierName,
        ois.specification_unit specificationUnit,
        ois.after_sale_unit afterSaleUnit,
        ois.supplier_tenant_id supplierTenantId,
        ois.title,
        ois.main_picture mainPicture,
        ois.supplier_sku_id supplySkuId,
        ois.specification,
        ois.warehouse_type warehouseType,
        ois.goods_type goodsType,
        ois.delivery_type deliveryType,
        ois.supply_price supplyPrice,
        ois.sku_id skuId,
        ois.sku_code skuCode,
        ois.custom_sku_code customSkuCode,
        ois.presale_switch presaleSwitch
        from order_item oi
        left join order_item_snapshot ois on oi.tenant_id = ois.tenant_id and oi.id = ois.order_item_id
        <where>
            <if test="orderIds != null and orderIds.size() > 0">
                and oi.order_id in
                <foreach collection="orderIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="supplierIds != null and supplierIds.size() > 0 ">
                and ois.supplier_tenant_id in
                <foreach collection="supplierIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="querySaleQuantity" resultType="integer">
        select
        sum(i.amount)
        from
        order_item i
        where i.tenant_id = #{tenantId}
        and i.order_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateDeliveryQuantity">
        update
            order_item
        set delivery_quantity = delivery_quantity + #{quantity}
        where id = #{orderItemId}
          and amount >= delivery_quantity + #{quantity}
    </update>

    <select id="querySkuQuantity" resultType="integer">
        select
        count(distinct s.sku_id)
        from
        order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
        where i.tenant_id = #{tenantId}
        and i.order_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <insert id="batchSave" useGeneratedKeys="true" keyProperty="id">
        insert into order_item
        (
        tenant_id,
        order_id,
        item_id,
        amount,
        payable_price,
        total_price,
        store_no,
        status,
        after_sale_expiry_time,
        order_type
        )
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.tenantId},
            #{item.orderId},
            #{item.itemId},
            #{item.amount},
            #{item.payablePrice},
            #{item.totalPrice},
            #{item.storeNo},
            #{item.status},
            #{item.afterSaleExpiryTime},
            #{item.orderType}
            )
        </foreach>
    </insert>

    <select id="queryOrderItemVOByOrderIds" resultType="com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot">
        select
        i.id orderItemId, i.item_id itemId, i.amount, i.payable_price payablePrice, i.total_price totalPrice,
        s.supplier_tenant_id supplierTenantId, s.title, s.main_picture mainPicture, s.specification, s.sku_id skuId,
        s.supplier_sku_id supplySkuId, i.order_id orderId,
        s.supply_price supplyPrice, s.delivery_type deliveryType, s.warehouse_type warehouseType,s.specification_unit
        specificationUnit, i.after_sale_expiry_time afterSaleExpiryTime,
        s.goods_type goodsType, weight
        from
        order_item i
        left join order_item_snapshot s on i.id = s.order_item_id
        where i.tenant_id = #{tenantId}
        and i.order_id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

</mapper>
