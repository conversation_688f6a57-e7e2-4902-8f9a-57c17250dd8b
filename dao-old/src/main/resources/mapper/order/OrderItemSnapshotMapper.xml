<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.dao.mapper.OrderItemSnapshotMapper">
    <insert id="batchSave">
        insert into order_item_snapshot
        (tenant_id,
        order_item_id,
        sku_id,
        supplier_tenant_id,
        supplier_sku_id,
        area_item_id,
        title,
        main_picture,
        specification_unit,
        specification,
        supply_price,
        warehouse_type,
        delivery_type,
        supplier_name,
        max_after_sale_amount,
        after_sale_unit,
        pricing_type,
        pricing_number,
        after_sale_rule,
        goods_type,
        order_id,
        item_code,
        buy_multiple_Switch,
        buy_multiple,
        sku_code,
        custom_sku_code
        )
        values
        <foreach collection="snapshotList" item="snapshot" separator=",">
            (#{snapshot.tenantId},
            #{snapshot.orderItemId},
            #{snapshot.skuId},
            #{snapshot.supplierTenantId},
            #{snapshot.supplierSkuId},
            #{snapshot.areaItemId},
            #{snapshot.title},
            #{snapshot.mainPicture},
            #{snapshot.specificationUnit},
            #{snapshot.specification},
            #{snapshot.supplyPrice},
            #{snapshot.warehouseType},
            #{snapshot.deliveryType},
            #{snapshot.supplierName},
            #{snapshot.maxAfterSaleAmount},
            #{snapshot.afterSaleUnit},
            #{snapshot.pricingType},
            #{snapshot.pricingNumber},
            #{snapshot.afterSaleRule},
            #{snapshot.goodsType},
            #{snapshot.orderId},
            #{snapshot.itemCode},
            #{snapshot.buyMultipleSwitch},
            #{snapshot.buyMultiple},
            #{snapshot.skuCode},
            #{snapshot.customSkuCode}
            )
        </foreach>
    </insert>
</mapper>
