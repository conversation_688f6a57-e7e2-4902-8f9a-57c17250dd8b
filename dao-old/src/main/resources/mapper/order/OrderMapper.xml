<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.dao.mapper.OrderMapper">
    <select id="sumOrderTotalPrice" resultType="java.math.BigDecimal">
        select
            ifnull(sum(total_price), 0)
        from
            `order` o
        where o.pay_time > #{startTime}
          and o.pay_time &lt; #{endTime}
          and o.status not in (1,2)
          and o.tenant_id = #{tenantId}
        <if test="storeIds != null and storeIds.size() > 0">
            and o.store_id in
            <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countPayOrderQuantity" resultType="integer">
        select
            count(o.id)
        from
            `order` o
        where o.pay_time &gt; #{startTime}
          and o.pay_time &lt; #{endTime}
          and o.status not in (1,2)
          and o.tenant_id = #{tenantId}
        <if test="storeIds != null and storeIds.size() > 0">
            and o.store_id in
            <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="countPayOrderStoreQuantity" resultType="integer">
        select
            count(distinct o.store_id)
        from
            `order` o
        where o.pay_time &gt; #{startTime}
        and o.pay_time &lt; #{endTime}
        and o.status not in (1,2)
        and o.tenant_id = #{tenantId}
        <if test="storeIds != null and storeIds.size() > 0">
            and o.store_id in
            <foreach collection="storeIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="countWaitDeliveryQuantity" resultType="integer">
        select
            count(o.id)
        from
            `order` o
        where o.status in (3,10,11)
          and o.tenant_id = #{tenantId}
    </select>

    <update id="batchUpdateStatus">
        update `order`
        set
        status = #{req.updateStatus}
        <if test='req.updateStatus == "12"'>
            ,begin_delivery_time = now()
        </if>
        where
        1 = 1
        <if test="req.tenantId">
            and tenant_id = #{req.tenantId}
        </if>

        <if test="req.originStatus != null">
            and status = #{req.originStatus}
        </if>
        <if test="req.orderNos != null and req.orderNos.size() > 0">
            and order_no IN
            <foreach collection="req.orderNos" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="req.orderIds != null and req.orderIds.size() > 0">
            and id IN
            <foreach collection="req.orderIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateStatusByOrderIds">
        update `order`
        set status = #{updateStatus},
            update_time = now()
        <if test="updateStatus == 5">
            ,finished_time = now()
        </if>
        <where>
            id in
            <foreach collection="orderIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>

            <if test="originStatusList != null and originStatusList.size > 0">
                and status in
                <foreach collection="originStatusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
        </where>


    </update>

    <select id="querySkuSaleQuantity" resultType="com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity">
        select
        s.sku_id skuId, sum(i.amount) saleQuantity
        from `order` o
        left join `order_item` i on o.id = i.order_id
        left join `order_item_snapshot` s on i.id = s.order_item_id
        where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12)
        and s.sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and o.create_time between #{startTime} and #{endTime}
        group by s.sku_id
    </select>


    <select id="querySkuSaleWithStoreNoQuantity" resultType="com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity">
        select
        s.sku_id skuId, i.store_no warehouseId, sum(i.amount) saleQuantity
        from `order` o
        left join `order_item` i on o.id = i.order_id and o.tenant_id = i.tenant_id
        left join `order_item_snapshot` s on i.id = s.order_item_id
        where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12) and i.store_no >= 0
        and s.sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and o.create_time between #{startTime} and #{endTime}
        group by s.sku_id,i.store_no
    </select>

    <select id="querySkuSaleWithCityQuantity" resultType="com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity">
        select
        s.sku_id skuId, oa.city city, sum(i.amount) saleQuantity
        from `order` o
        left join `order_item` i on o.id = i.order_id and o.tenant_id = i.tenant_id
        left join `order_item_snapshot` s on i.id = s.order_item_id
        left join `order_address` oa on oa.order_id = o.id
        where o.tenant_id = #{tenantId} and o.`status` in (3,4,5,10,11,12) and i.store_no >= 0
        and s.sku_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and o.create_time between #{startTime} and #{endTime}
        group by s.sku_id, oa.city
    </select>

    <select id="queryOrderDetail" resultType="com.cosfo.ordercenter.dao.model.po.OrderDetail">
        select
        o.id orderId, o.order_no orderNo, o.store_id storeId, o.create_time createTime,
        o.pay_time payTime, o.pay_type payType, o.delivery_time deliveryTime, o.finished_time finishedTime,
        s.title, item.item_id itemId, s.sku_id skuId, s.specification, item.amount, item.payable_price skuPrice, o.delivery_fee deliveryFee,
        o.total_price orderPrice, item.id orderItemId, s.supplier_tenant_id supplyTenantId, s.supplier_sku_id supplySkuId,
        s.supply_price supplyPrice,s.main_picture mainPicture, item.item_id itemId, o.warehouse_type warehouseType,
        s.pricing_type pricingType, s.pricing_number pricingNumber, s.goods_type goodsType, item.total_price skuTotalPrice, s.weight
        from
        `order` o
        left join `order_item` item on item.tenant_id = o.tenant_id and item.order_id = o.id
        left join `order_item_snapshot` s on s.tenant_id = o.tenant_id and s.order_item_id = item.id
        where
        o.tenant_id = #{tenantId}
        and o.id in
        <foreach collection="orderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>


    <update id="updateOrderDeliveryTime">
        update  `order`
        set
        status = (case status
            when 5 then 4
            else status end),
        delivery_time = #{deliveryTime},
        update_time = now()
        where warehouse_type = 1
        and order_no IN
        <foreach collection="orderNoList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </update>

    <update id="updateOrderCustomerOrderId">
        update  `order`
        set
        customer_order_id = #{newCustomerOrderId}
        where id = #{orderId}
    </update>

    <select id="queryOrderItemSaleQuantity" resultType="com.cosfo.ordercenter.dao.model.po.OrderItemSaleQuantity">
        select
            oi.`item_id` ,
            SUM(oi.`amount`) - sum(ifnull(af.amount, 0)) as quantity
        from
            `order` o
                left join `order_item` oi on o.id = oi.order_id
                left join `order_after_sale` af on af.order_item_id = oi.id
                and af.after_sale_type = 1
                and af.status in
        <foreach collection="afterSaleStatus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        where
            o.create_time between #{startTime} and #{endTime}
          and o.tenant_id = #{tenantId}
          and o.status in
        <foreach collection="orderStatus" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
          and o.store_id = #{storeId}
          and oi.`item_id` in
            <foreach collection="itemIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        GROUP by oi.`item_id`
    </select>
    <select id="querySupplierOrderList" resultType="com.cosfo.ordercenter.dao.model.dto.SupplierOrderDTO">
        select
            o.id orderId,
            item.id orderItemId,
            item.amount,
            s.supply_price supplyPrice,
            s.supplier_tenant_id supplierId
        from
        `order` o
        LEFT JOIN `order_item` item ON item.tenant_id = o.tenant_id
        AND item.order_id = o.id
        LEFT JOIN `order_item_snapshot` s ON s.tenant_id = o.tenant_id
        AND s.order_item_id = item.id
        where
        o.tenant_id = #{req.tenantId}
        and s.supplier_tenant_id IN
        <foreach collection="req.supplierIds" item="supplierId" open="(" close=")" separator=",">
            #{supplierId}
        </foreach>
        <if test="req.statusList != null and req.statusList.size > 0">
            and o.status in
            <foreach collection="req.statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="req.warehouseType != null">
            and o.warehouse_type = #{req.warehouseType}
        </if>
    </select>


  <select id="getWaitFulfillmentOrderNoByTime" resultType="java.lang.String">
    select order_no
    from `order`
    where
      `status` = 10
      and fulfillment_no is null
      and create_time <![CDATA[>=]]> #{startTime}
      and create_time <![CDATA[<=]]> #{endTime}
      and pay_time <![CDATA[<=]]> #{endTime}
  </select>

</mapper>
