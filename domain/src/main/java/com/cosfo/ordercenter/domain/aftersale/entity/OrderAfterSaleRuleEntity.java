package com.cosfo.ordercenter.domain.aftersale.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * order_after_sale_rule
 * <AUTHOR>
@Data
public class OrderAfterSaleRuleEntity implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 配送方式
     */
    private Integer deliveryType;

    /**
     * 申请结束天数
     */
    private Integer applyEndTime;

    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;

    /**
     * 处理方式：0同意1拒绝
     */
    private Integer dealType;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 类型 0配送仓库 1商品分组
     */
    private Integer type;
    /**
     * 是否默认0默认 1非默认
     */
    private Integer defaultFlag;
    /**
     * 规则
     */
    private String rule;

    private static final long serialVersionUID = 1L;
}