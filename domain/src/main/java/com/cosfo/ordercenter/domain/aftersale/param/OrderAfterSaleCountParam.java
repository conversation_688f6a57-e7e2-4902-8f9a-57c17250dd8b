package com.cosfo.ordercenter.domain.aftersale.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleCountParam implements Serializable {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 售后单状态
     * @see com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum
     */
    private List<Integer> statusList;

    /**
     * 订单id
     */
    private List<Long> orderIds;
}
