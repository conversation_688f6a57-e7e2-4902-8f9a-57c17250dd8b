package com.cosfo.ordercenter.domain.aftersale.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderAfterSaleListQueryParam implements Serializable {

    /**
     * 售后单状态
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消\n系统内部状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款9 三方处理中
     * @see
     */
    private List<Integer> statusList;

    /**
     * 售后类型
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发 7、退款录入余额 8、 退货退款录入余额
     */
    private List<Integer> serviceType;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应商id
     */
    private List<Long> supplierIds;

    /**
     * 售后完成时间搜索
     */
    private LocalDateTime finishTimeStart;

    /**
     * 售后完成时间搜索
     */
    private LocalDateTime finishTimeEnd;

    /**
     * 根据订单ids搜索
     */
    private List<Long> orderIds;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    /**
     * 订单明细id
     */
    private List<Long> orderItemIds;

    /**
     * 售后类型 0 已到货 1 未到货
     */
    private Integer afterSaleType;
}
