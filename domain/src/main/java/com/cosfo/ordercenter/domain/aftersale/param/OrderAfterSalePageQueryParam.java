package com.cosfo.ordercenter.domain.aftersale.param;

import com.cosfo.ordercenter.domain.order.param.query.PageQueryParam;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSalePageQueryParam extends PageQueryParam {

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后订单编号
     */
    private String afterSaleOrderNo;


    /**
     * 售后单状态
     * 状态 1,待审核 2，已成功 3，已失败 4 ，已取消\n系统内部状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款9 三方处理中
     * @see
     */
    private List<Integer> statusList;


    /**
     * 售后类型 0-配送后售后 1-配送前售后
     * @see OrderAfterSaleTypeEnum
     */
    private Integer afterSaleType;

    /**
     * 售后服务类型
     * 售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发 7、退款录入余额 8、 退货退款录入余额
     */
    private List<Integer> serviceTypeList;

    /**
     * 店铺Id
     */
    private List<Long> storeIds;

    /**
     * 账户ID
     */
    private List<Long> accountIds;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 供应商id
     */
    private Long supplierTenantId;

    /**
     * 供应商id
     */
    private List<Long> supplierIds;


    /**
     * 租户编号列表
     */
    private List<Long> tenantIds;

    /**
     * 商品ids
     */
    private List<Long> itemIds;

    /**
     * list滚动查询，上次查询获取到的最大id
     */
    private Long maxId;

    /**
     * list每次获取数量，最大500
     */
    private Integer batchSize;

    /**
     * 仓库编号
     * -1 无仓 -2 三方仓 自营仓编号
     */
    private Integer warehouseNo;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 审核时间 - 开始（包含当前时间）
     */
    private LocalDateTime handleTimeBegin;
    /**
     *  审核时间 - 结束（包含当前时间）
     */
    private LocalDateTime handleTimeEnd;
}
