package com.cosfo.ordercenter.domain.aftersale.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderAfterSaleQueryParam {
    /**
     * 售后单编号
     */
    private List<String> afterSaleOrderNos;
    /**
     * 订单项Id
     */
    private List<Long> orderItemIds;
    /**
     * 订单ID
     */
    private List<Long> orderIds;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 外部系统售后单编号
     */
    private List<String> customerAfterSaleOrderNos;
}
