package com.cosfo.ordercenter.domain.aftersale.param.command;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleAuditCommandParam implements Serializable {
    /**
     * 售后单
     */
    private String afterSaleOrderNo;

    /**
     * 售后单id
     */
    private Long orderAfterSaleId;

    /**
     * 审核备注
     */
    private String handleRemark;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 回收时间
     */
    private LocalDateTime recycleTime;
    /**
     * 回收数量
     */
    private Integer amount;

    /**
     * 退款金额
     */
    private BigDecimal totalPrice;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 责任方
     */
    private String responsibilityType;


    // ---------------------------------------以下为manage售后审核参数-------
    /**
     * 配送方式 0其他 1物流快递
     */
    private Integer deliveryType;

    /**
     * 物流公司
     */
    private String deliveryCompany;

    /**
     * 配送单号
     */
    private String deliveryNo;

    /**
     * 配送备注，配送方式0其他时传递
     */
    private String remark;


    /**
     * 计划量 （有仓 退货退款 第二次审核 传递）
     */
    private Integer quantity;


    /**
     * 实际入库量（有仓 退货退款 第二次审核 传递）
     */
    private Integer actualQuantity;

    /**
     * 退回地址Id
     */
    private Long returnAddressId;

    /**
     * 退货库存仓no
     */
    private String returnWarehouseNo;

    /**
     * 供应商申请金额
     */
    private BigDecimal supplierApplyPrice;

    /**
     * 供应商实退金额
     */
    private BigDecimal supplierTotalRefundPrice;

    /**
     * 请求来源
     * @see SystemSourceEnum
     */
    private Integer systemSource;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    /**
     * 货品类型
     */
    private Integer goodsType;

    /**
     * 是否需要服务商审核
     */
    private boolean needServiceProviderAudit;


    /**
     *  退款凭证
     */
    private String refundReceipt;
}
