package com.cosfo.ordercenter.domain.aftersale.param.command;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleModifyQuantityParam implements Serializable {

    private Long afterSaleId;

    private String operatorName;

    private LocalDateTime updateTime;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 申请金额
     */
    private BigDecimal applyPrice;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;

}
