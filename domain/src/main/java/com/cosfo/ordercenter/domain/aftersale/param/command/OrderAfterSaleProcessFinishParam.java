package com.cosfo.ordercenter.domain.aftersale.param.command;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderAfterSaleProcessFinishParam implements Serializable {

    /**
     * 售后订单号
     */
    private String orderAfterSaleNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 应回收/配送数量
     */
    private Integer shouldCount;

    /**
     * 0配送 1回收
     */
    private Integer deliveryType;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 0正常 1异常
     */
    private Integer state;

    /**
     * 缺货原因
     */
    private String remark;

    /**
     * 回收凭证照片
     */
    private String recyclePicture;

    /**
     * 回收详细描述
     */
    private String recycleDetailMessage;

    /**
     * 回收数据明细信息
     */
    private String recycleQuantityDetail;

    /**
     * 商品项完成配送类型 0配送1回收
     */
    private Integer itemFinishType;
}
