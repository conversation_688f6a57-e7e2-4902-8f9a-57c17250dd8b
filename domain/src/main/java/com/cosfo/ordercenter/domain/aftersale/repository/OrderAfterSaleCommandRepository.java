package com.cosfo.ordercenter.domain.aftersale.repository;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAddCommand;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleStatusUpdateCommandParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleCommandRepository {


    /**
     * 批量插入售后单
     * @param orderAfterSales
     * @return
     */
    List<Long> batchAdd(List<OrderAfterSaleEntity> orderAfterSales);


    /**
     * 更新售后单状态
     * @param orderAfterSaleStatusUpdateReq
     * @return
     */
    boolean updateStatus(OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateReq);


    /**
     * 售后单自动完结
     * @return
     */
    boolean autoFinish();



    /**
     * 更新城配仓号
     * @param afterSaleId
     * @param sourceStoreNo
     * @param storeNo
     * @return
     */
    boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo);


    /**
     * 保存售后单
     *
     * @param afterSaleEntity
     * @return
     */
    Long save(OrderAfterSaleEntity afterSaleEntity);

    boolean updateById(OrderAfterSaleEntity orderaftersaleEntity);
}
