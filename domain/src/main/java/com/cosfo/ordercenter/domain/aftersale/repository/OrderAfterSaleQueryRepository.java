package com.cosfo.ordercenter.domain.aftersale.repository;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.*;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleQueryRepository {

    /**
     * 根据条件查询售后单
     *
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSaleEntity> queryListByCondition(OrderAfterSaleQueryParam orderAfterSaleQueryParam);



    /**
     * 售后单查询
     * @param req
     * @return
     */
    List<OrderAfterSaleEntity> queryList(OrderAfterSaleListQueryParam req);

    /**
     * 售后单分页查询
     * @param req
     * @return
     */
    PageInfo<OrderAfterSaleEntity> queryPage(OrderAfterSalePageQueryParam req);

    /**
     * 根据售后no查询售后单
     * @param afterSaleNo
     * @return
     */
    OrderAfterSaleEntity queryByAfterSaleNo(String afterSaleNo);

    /**
     * 统计售后单数量
     * @param orderAfterSaleCountReq
     * @return
     */
    Integer countOrderAfterSale(OrderAfterSaleCountParam orderAfterSaleCountReq);


    /**
     * 根据订单查询售后单
     * @param orderId
     * @param tenantId
     * @return
     */
    List<OrderAfterSaleEntity> queryByOrderId(Long orderId, Long tenantId);

    /**
     * 查询售后记录最近使用的退回地址id
     * @param tenantId
     * @return
     */
    Long getRecentlyUsedReturnAddressId(Long tenantId);

    List<OrderAfterSaleEntity> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleParam req);

    /**
     * 根据售后单no查询
     * @param orderAfterSaleNos
     * @return
     */
    List<OrderAfterSaleEntity> queryByNos(List<String> orderAfterSaleNos);

    /**
     * 根据售后单no查询
     *
     * @param orderAfterSaleNo
     * @return
     */
    OrderAfterSaleEntity queryByNo(String orderAfterSaleNo);

    /**
     * 根据售后单id查询
     */
    List<OrderAfterSaleEntity> queryByIds(List<Long> orderAfterSaleIds);

    /**
     * 根据订单统计售后单数量
     * @param req
     * @return
     */
    Map<Long, Integer> countOrderAfterSaleByOrderId(OrderAfterSaleCountParam req);

    /**
     * 根据入参批量查询
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSaleEntity> queryListByParam(OrderAfterSaleQueryParam orderAfterSaleQueryParam);


    OrderAfterSaleEntity queryById(Long id);
}
