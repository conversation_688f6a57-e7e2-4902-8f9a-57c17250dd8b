package com.cosfo.ordercenter.domain.aftersale.repository;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleRuleCommandRepository {
    /**
     * 初始化售后规则时，插入记录，如果存在，则更新
     * @param orderAfterSaleRule
     * @return
     */
    Long addOrUpdate(OrderAfterSaleRuleCommandParam orderAfterSaleRule);


    Integer updateRule(OrderAfterSaleRuleCommandParam orderAfterSaleRule);

    Integer deleteRule(Long id);
}

