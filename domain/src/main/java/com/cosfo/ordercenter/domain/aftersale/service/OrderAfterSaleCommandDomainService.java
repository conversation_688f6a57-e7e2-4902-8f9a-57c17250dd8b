package com.cosfo.ordercenter.domain.aftersale.service;

import com.cosfo.ordercenter.common.enums.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleModifyQuantityParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRecycleFailRefundParam;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleStatusUpdateCommandParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleCommandRepository;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleCommandDomainService {


    private final static List<Integer> AFTER_SALE_PROCESS_STATUS = Lists.newArrayList(
            OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
            OrderAfterSaleStatusEnum.REFUNDING.getValue(),
            OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
            OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
            OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
            OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue());

    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderAfterSaleCommandRepository orderAfterSaleCommandRepository;


    public boolean updateById(OrderAfterSaleEntity orderAfterSale) {
        return orderAfterSaleCommandRepository.updateById(orderAfterSale);
    }

    public boolean updateStatus(OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateReq) {
        return orderAfterSaleCommandRepository.updateStatus(orderAfterSaleStatusUpdateReq);
    }

    public boolean autoFinish() {
        return orderAfterSaleCommandRepository.autoFinish();
    }

    public boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo) {
        return orderAfterSaleCommandRepository.updateStoreNo(afterSaleId, sourceStoreNo, storeNo);
    }

    public Long save(OrderAfterSaleEntity afterSaleEntity) {
        return orderAfterSaleCommandRepository.save(afterSaleEntity);
    }

    public List<Long> batchAdd(List<OrderAfterSaleEntity> orderAfterSales) {
        return orderAfterSaleCommandRepository.batchAdd(orderAfterSales);
    }

    /**
     * 更新售后单数量和金额
     * @param modifyQuantityParam
     * @return
     */
    public Boolean modifyQuantity(OrderAfterSaleModifyQuantityParam modifyQuantityParam) {
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(modifyQuantityParam.getAfterSaleId());
        update.setOperatorName(modifyQuantityParam.getOperatorName());
        update.setUpdateTime(LocalDateTime.now());
        update.setAmount(modifyQuantityParam.getAmount());
        update.setApplyPrice(modifyQuantityParam.getApplyPrice());
        update.setTotalPrice(modifyQuantityParam.getTotalPrice());
        return orderAfterSaleCommandRepository.updateById(update);
    }

    public Boolean recycleFailRefund(OrderAfterSaleRecycleFailRefundParam recycleFailRefundParam) {
        OrderAfterSaleEntity update = new OrderAfterSaleEntity();
        update.setId(recycleFailRefundParam.getAfterSaleId());
        update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getValue());
        update.setTotalPrice(recycleFailRefundParam.getTotalPrice());
        return orderAfterSaleCommandRepository.updateById(update);
    }
}
