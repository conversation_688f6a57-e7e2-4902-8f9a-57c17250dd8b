package com.cosfo.ordercenter.domain.delivery.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 门店运费规则表(MerchantDeliveryFeeRule)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 11:54:22
 */
@Data
public class MerchantDeliveryFeeRuleEntity implements Serializable {
    private static final long serialVersionUID = -11903759875648620L;
    /**
     * primary key
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 0、无仓 1、三方仓 2、自营仓
     */
    private Integer type;
    /**
     * 运费/ 加价运费 / 实时上浮百分比
     */
    private BigDecimal deliveryFee;
    /**
     * 免运费金额
     */
    private BigDecimal freeDeliveryPrice;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 0,每单1,每日2,基于仓运费报价
     */
    private Integer ruleType;
    /**
     * 0,固定 1实时加价 2实时上浮
     */
    private Integer priceType;
    /**
     * 实时加价运费，实时上浮百分比
     */
    private BigDecimal relateNumber;
    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;
    /**
     * 免运费规则，0金额，1数量，2重量
     */
    private Integer freeDeliveryType;
    /**
     * 免运费数量
     */
    private Integer freeDeliveryQuantity;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 选中商品ID列表
     */
    private Set<Long> hitItemIds;
    /**
     * 选中配送区域名称列表
     */
    private Set<List<String>> hitAreas;

    /**
     * 包含后续新增商品 0 - 不包含 1 - 包含
     */
    private Boolean includeNewFlag;

    /**
     * 匹配区域类型 0 - 按照配送区域 1 - 按照选中门店
     */
    private Integer matchRegionType;

    /**
     * 选中门店ID列表
     */
    private Set<Long> hitStoreIds;

    /**
     * 包含所有门店及后续新增门店 0 - 不包含 1 - 包含
     */
    private Boolean includeAllStoreFlag;


    /**
     * 匹配商品类型 0-根据商品设置 1-根据货源设置
     */
    private Integer matchItemType;

    /**
     * 货源类型,和goodsType不同, 0:全部，1:自营货品，2自营货品-代仓，3供应商直发货品
     */
    private Integer hitGoodsSource;

    /**
     * 履约类型：0-城配履约；1-快递履约
     */
    private Integer fulfillmentType;

}

