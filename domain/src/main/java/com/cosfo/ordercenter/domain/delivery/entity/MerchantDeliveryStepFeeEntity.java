package com.cosfo.ordercenter.domain.delivery.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阶梯运费(MerchantDeliveryStepFee)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:41:43
 */
@Data
public class MerchantDeliveryStepFeeEntity implements Serializable {
    private static final long serialVersionUID = 119720805987218687L;
    /**
     * primary key
     */
    private Long id;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 运费规则ID
     */
    private Long ruleId;
    /**
     * 门槛类型  0-金额 1-数量 2-重量
     */
    private Integer feeRule;
    /**
     * 阶梯门槛。即满xx元/件/kg
     */
    private BigDecimal stepThreshold;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

}

