package com.cosfo.ordercenter.domain.delivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 16:55
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryItemFeeCommandParam implements Serializable {
    private static final long serialVersionUID = -4146326931509403249L;

    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 最终运费
     */
    private BigDecimal deliveryFee;
}
