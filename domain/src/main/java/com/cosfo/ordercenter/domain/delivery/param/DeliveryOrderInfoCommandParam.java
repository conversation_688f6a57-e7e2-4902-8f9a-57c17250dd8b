package com.cosfo.ordercenter.domain.delivery.param;

import com.cosfo.ordercenter.common.enums.WarehouseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 17:17
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryOrderInfoCommandParam implements Serializable {
    private static final long serialVersionUID = 6504138243250854955L;

    /**
     * 订单总金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 订单总件数
     */
    private Integer orderTotalCount;

    /**
     * 门店地址-省
     */
    private String storeProvince;

    /**
     * 门店地址-市
     */
    private String storeCity;

    /**
     * 门店地址-区
     */
    private String storeArea;

    /**
     * 门店地址-门牌地址
     */
    private String storePoi;

    /**
     * 仓库类型
     */
    private Integer warehouseType;

    private WarehouseTypeEnum warehouseTypeEnum;

    /**
     * 仓库编号
     */
    private Long warehouseNo;
}
