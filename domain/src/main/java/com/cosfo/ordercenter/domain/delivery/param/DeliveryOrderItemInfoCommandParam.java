package com.cosfo.ordercenter.domain.delivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 17:18
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryOrderItemInfoCommandParam implements Serializable {
    private static final long serialVersionUID = 5862216981639315130L;


    /**
     * item_id
     */
    private Long itemId;

    /**
     * 商品数量
     */
    private Integer itemCount;

    /**
     * 单品总价
     */
    private BigDecimal itemTotalPrice;

    private Long skuId;

    private Long supplierSkuId;
}
