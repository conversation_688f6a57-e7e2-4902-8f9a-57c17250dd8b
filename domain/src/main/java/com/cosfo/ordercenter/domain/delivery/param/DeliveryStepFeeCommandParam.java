package com.cosfo.ordercenter.domain.delivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 14:45
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryStepFeeCommandParam implements Serializable {
    private static final long serialVersionUID = 7151191616300416293L;

    /**
     * 阶梯门槛。即满xx元/件
     */
    private BigDecimal stepThreshold;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
}
