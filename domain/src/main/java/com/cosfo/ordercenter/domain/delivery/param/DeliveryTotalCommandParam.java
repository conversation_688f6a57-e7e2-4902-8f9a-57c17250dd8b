package com.cosfo.ordercenter.domain.delivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 17:04
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryTotalCommandParam implements Serializable {
    private static final long serialVersionUID = 8341272062790805943L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 供应商租户
     */
    private Long supplierTenantId;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 订单信息
     */
    private DeliveryOrderInfoCommandParam orderInfoDTO;

    /**
     * 订单商品信息
     */
    private List<DeliveryOrderItemInfoCommandParam> orderItemInfoDTOList;

    /**
     * 相关订单信息
     */
    private DeliveryRelatedOrderInfoCommandParam relatedOrderInfoDTO;

}
