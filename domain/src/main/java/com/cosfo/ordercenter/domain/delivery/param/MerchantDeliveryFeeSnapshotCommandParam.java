//package com.cosfo.ordercenter.domain.delivery.param;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.List;
//
///**
// * @author: monna.chen
// * @Date: 2023/8/18 15:06
// * @Description:
// */
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class MerchantDeliveryFeeSnapshotCommandParam implements Serializable {
//    private static final long serialVersionUID = -541428980105487522L;
//
//
//    /**
//     * 租户ID
//     */
//    private Long tenantId;
//
//    /**
//     * 订单号
//     */
//    private Long orderId;
//
//    /**
//     * 订单运费
//     */
//    private BigDecimal deliveryFee;
//
//    /**
//     * 计算运费场景 1-下单 2-发货前售后
//     */
//    private Integer scene;
//
//    /**
//     * 订单信息
//     */
//    private DeliveryTotalCommandParam orderInfo;
//
//    /**
//     * 规则列表
//     */
//    private List<MerchantDeliveryRuleInfoCommandParam> ruleList;
//
//    /**
//     * 命中规则的运费
//     */
//    private List<DeliveryItemFeeCommandParam> hitRuleList;
//
//    /**
//     * 备注
//     */
//    private String remark;
//}
