//package com.cosfo.ordercenter.domain.delivery.param;
//
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.List;
//import java.util.Set;
//
///**
// * @author: monna.chen
// * @Date: 2023/8/18 14:32
// * @Description:
// */
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class MerchantDeliveryRuleInfoCommandParam implements Serializable {
//    private static final long serialVersionUID = 119245041284830127L;
//
//
//    /**
//     * 规则ID
//     */
//    private Long ruleId;
//
//    /**
//     * 租户ID
//     */
//    private Long tenantId;
//
//    /**
//     * 仓库类型
//     *
//     * @see WarehouseTypeEnum
//     */
//    private Integer warehouseType;
//
//    /**
//     * 规则类型
//     *
//     * @see MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum
//     */
//    private Integer ruleType;
//
//    /**
//     * 是否为默认规则
//     *
//     * @see MerchantDeliveryFeeRuleEnum.MerchantAddressDefaultTypeEnum
//     */
//    private Integer defaultType;
//
//    /**
//     * 门槛类型  0-金额 1-数量
//     *
//     * @see MerchantDeliveryFeeRuleEnum.DeliveryThresholdType
//     */
//    private Integer deliveryType;
//
//    /**
//     * 阶梯价
//     */
//    private List<DeliveryStepFeeDTO> stepFeeDescList;
//
//    /**
//     * 优先级
//     */
//    private Integer priority;
//
//    /**
//     * 命中商品ID
//     */
//    private Set<Long> hitItemIds;
//
//    /**
//     * 命中区域列表
//     */
//    private Set<List<String>> hitAreaList;
//
//    /**
//     * 订单仓库编号。自营仓这个字段为空时表示当前规则未命中
//     */
//    private Long warehouseNo;
//
//    /**
//     * 包含后续新增商品 0 - 不包含 1 - 包含
//     */
//    private Boolean includeNewFlag;
//
//    /**
//     * 三方随仓相关属性
//     */
//    /**
//     * 加价类型
//     *
//     * @see MerchantDeliveryFeeRuleEnum.MerchatDeliveryFeeRulePriceTypeEnum
//     */
//    private Integer priceType;
//
//    /**
//     * 加价金额
//     */
//    private BigDecimal relateNumber;
//}
