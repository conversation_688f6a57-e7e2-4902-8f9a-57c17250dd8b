package com.cosfo.ordercenter.domain.delivery.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 14:26
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantDeliveryRuleQueryParam implements Serializable {
    private static final long serialVersionUID = -4619674268009040658L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 仓库类型
     * @see com.cosfo.ordercenter.client.common.WarehouseTypeEnum
     */
    private Integer warehouseType;

    /**
     * 仓库编号
     */
    private Long warehouseNo;

    /**
     * 是否是仓库的默认数据0:非默认类型;1:默认类型
     */
    private Integer defaultType;
}
