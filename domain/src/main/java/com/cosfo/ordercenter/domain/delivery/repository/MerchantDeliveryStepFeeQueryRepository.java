package com.cosfo.ordercenter.domain.delivery.repository;

import com.cosfo.ordercenter.domain.delivery.entity.MerchantDeliveryStepFeeEntity;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryStepFeeParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MerchantDeliveryStepFeeQueryRepository {

    /**
     * 顺序条件查询阶梯运费
     *
     * @param param
     * @return
     */
    List<MerchantDeliveryStepFeeEntity> listByParam(QueryDeliveryStepFeeParam param);

    /**
     * 倒序查询阶梯运费
     *
     * @param param
     * @return
     */
    List<MerchantDeliveryStepFeeEntity> listStepDesc(QueryDeliveryStepFeeParam param);
}
