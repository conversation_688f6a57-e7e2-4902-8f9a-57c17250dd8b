package com.cosfo.ordercenter.domain.delivery.repository;

import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;

/**
 * <AUTHOR>
 */
public interface OrderDeliveryFeeSnapshotCommandRepository {


    /**
     * 将该订单所有的对应场景的快照记录，更新为无效
     *
     * @param orderId
     */
    boolean updateEffectiveFlag(Long orderId, Integer scence);


    boolean save(OrderDeliveryFeeSnapshotEntity snapshotEntity);
}
