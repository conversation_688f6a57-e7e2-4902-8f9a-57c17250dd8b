package com.cosfo.ordercenter.domain.delivery.service;

import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;
import com.cosfo.ordercenter.domain.delivery.repository.OrderDeliveryFeeSnapshotCommandRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderDeliveryFeeSnapshotCommandDomainService {

    @Resource
    private OrderDeliveryFeeSnapshotCommandRepository orderDeliveryFeeSnapshotCommandRepository;

    public boolean updateEffectiveFlag(Long orderId, Integer scence) {
        return orderDeliveryFeeSnapshotCommandRepository.updateEffectiveFlag(orderId, scence);
    }


    public boolean save(OrderDeliveryFeeSnapshotEntity snapshotEntity) {
        return orderDeliveryFeeSnapshotCommandRepository.save(snapshotEntity);
    }

}
