package com.cosfo.ordercenter.domain.order.entity;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Getter
@Setter
public class OrderItemEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * itemId编码
     */
    private Long itemId;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 城配仓 编码
     */
    private Integer storeNo;

    /**
     * 1 下单中 2 待支付 3 已支付 4已完成 5 已取消 6 已退款
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 可申请售后过期时间
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 0-普通订单,1=组合订单, 2=预售订单
     */
    private Integer orderType;

    /**
     * 已配数量(无仓订单有效)
     */
    private Integer deliveryQuantity;


}
