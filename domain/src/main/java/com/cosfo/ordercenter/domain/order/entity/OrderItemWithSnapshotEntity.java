package com.cosfo.ordercenter.domain.order.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderItemWithSnapshotEntity {
    private Long orderItemId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * itemId编码
     */
    private Long itemId;

    /**
     * item自有编码
     */
    private String itemCode;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 单价
     */
    private BigDecimal payablePrice;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 城配仓 编码
     */
    private Integer storeNo;

    /**
     * 1 下单中 2 待支付 3 已支付 4已完成 5 已取消 6 已退款
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 可申请售后过期时间
     */
    private LocalDateTime afterSaleExpiryTime;

    /**
     * 0-普通订单,1=组合订单, 2=预售订单
     */
    private Integer orderType;

    /**
     * 已配数量(无仓订单有效)
     */
    private Integer deliveryQuantity;

    /**
     * 供应商Id
     */
    private Long supplierTenantId;
    /**
     * 名称
     */
    private String title;
    /**
     * 主图片
     */
    private String mainPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 仓库类型
     */
    private Integer warehouseType;
    /**
     * 配送方式 0 品牌方配送 1三方配送
     */
    private Integer deliveryType;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 供应商报价
     */
    private BigDecimal supplyPrice;
    /**
     * skuID
     */
    private Long skuId;
//
//    /**
//     * 供应SKU_id
//     */
//    private Long supplySkuId;


    /**
     * 售后规则快照
     */
    private String afterSaleRule;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;


    /**
     * 供应商商品skuId
     */
    private Long supplierSkuId;


    /**
     * 货品sku编码
     */
    private String skuCode;
    /**
     * 货品自有编码
     */
    private String customSkuCode;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 货品重量
     */
    private BigDecimal weight;
}
