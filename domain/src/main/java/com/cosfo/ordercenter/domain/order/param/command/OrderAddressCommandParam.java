package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * order_address
 * <AUTHOR>
@Data
public class OrderAddressCommandParam implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户编码
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 收货人
     */
    private String contactName;

    /**
     * 收货人联系电话
     */
    private String contactPhone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 收获地址
     */
    private String address;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * poi
     */
    private String poiNote;

    private static final long serialVersionUID = 1L;
}
