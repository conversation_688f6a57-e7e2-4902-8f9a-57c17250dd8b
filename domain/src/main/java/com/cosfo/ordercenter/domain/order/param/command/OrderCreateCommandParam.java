//package com.cosfo.ordercenter.domain.order.param.command;
//
//import lombok.Data;
//
//import javax.validation.constraints.NotEmpty;
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@Data
//public class OrderCreateCommandParam implements Serializable {
//
//    /**
//     * 订单数据
//     */
//    @NotNull(message = "订单信息不能为空")
//    private OrderCommandParam orderCommandParam;
//
//    /**
//     * 订单明细
//     */
//    @NotEmpty(message = "订单项不能为空")
//    private List<OrderItemCreateParam> orderItemList;
//
//    /**
//     * 地址信息
//     */
//    @NotNull(message = "地址信息不能为空")
//    private OrderAddressCommandParam orderAddressDTO;
//
//    /**
//     * 运费快照信息
//     */
//    private MerchantDeliveryFeeSnapshotCommandParam deliveryFeeSnapshotDTO;
//}
