package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderFulfillmentOrderCreateCommandParam implements Serializable {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 履约单号
     */
    private Long fulfillmentNo;

    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 城配仓号
     */
    private Integer storeNo;
}
