package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Getter
@Setter
public class OrderItemSnapshotAddParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单商品id
     */
    private Long orderItemId;

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 供应商Id
     */
    private Long supplierTenantId;

    /**
     * 供应商skuId
     */
    private Long supplierSkuId;

    /**
     * 区域商品Id
     */
    private Long areaItemId;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品图片
     */
    private String mainPicture;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 规格
     */
    private String specification;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 供应商报价
     */
    private BigDecimal supplyPrice;

    /**
     * 已废弃
     */
    private Integer warehouseType;

    /**
     * 已废弃
     */
    private Integer deliveryType;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 定价方式0、百分比上浮 1、定额上浮 2、固定价
     */
    private Integer pricingType;

    /**
     * 定价数值
     */
    private BigDecimal pricingNumber;

    /**
     * 售后规则快照
     */
    private String afterSaleRule;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 货品sku编码
     */
    private String skuCode;
    /**
     * 货品自有编码
     */
    private String customSkuCode;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 货品重量
     */
    private BigDecimal weight;
}
