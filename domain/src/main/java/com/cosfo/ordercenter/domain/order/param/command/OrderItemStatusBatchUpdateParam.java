package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderItemStatusBatchUpdateParam {

    /**
     * 订单id
     */
    private List<Long> orderIds;

    /**
     * 租户id
     */
    private Long tenantId;


    /**
     * 订单项状态
     * @see com.cosfo.ordercenter.client.common.OrderItemStatusEnum
     */
    private Integer status;

    /**
     * 城配仓id
     */
    private Integer storeNo;
}
