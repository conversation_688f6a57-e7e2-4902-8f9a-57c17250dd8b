package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderItemUpdateDeliveryQuantityParam implements Serializable {

    /**
     * 配送明细
     */
    private List<OrderItemQuantity> orderItemQuantities;

    /**
     * 订单id
     */
    private Long orderId;

    @Data
    public static class OrderItemQuantity implements Serializable {
        /**
         * 订单明细id
         */
        private Long orderItemId;

        /**
         * 配送数量
         */
        private Integer quantity;
    }
}
