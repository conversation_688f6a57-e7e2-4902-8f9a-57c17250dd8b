package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderStatusBatchUpdateParam implements Serializable {

    /**
     * orderNo list
     */
    private List<String> orderNos;

    /**
     * 订单id
     */
    private List<Long> orderIds;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 更新后状态
     */
    private Integer updateStatus;

    /**
     * 原始状态
     */
    private Integer originStatus;

    /**
     * 原始状态
     */
    private List<Integer> originStatusList;

}
