package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class OrderStatusUpdateParam implements Serializable {

    /**
     * 更新状态
     */
    private Integer status;

    /**
     * 原始状态
     */
    private Integer originStatus;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;


    /**
     * 订单编号
     */
    private String orderNo;


    /**
     * 配送时间
     */
    private LocalDate deliveryTime;
}
