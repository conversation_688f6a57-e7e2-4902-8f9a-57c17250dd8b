package com.cosfo.ordercenter.domain.order.param.command;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProfitSharingFinishTimeParam implements Serializable {

    /**
     * 更新分账完成订单列表
     */
    private List<ProfitSharingFinishTimeOrder> profitSharingFinishTimeOrderList;

    @Data
    public static class ProfitSharingFinishTimeOrder implements Serializable {

        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 分账完成时间
         */
        private LocalDateTime profitSharingFinishTime;
    }
}
