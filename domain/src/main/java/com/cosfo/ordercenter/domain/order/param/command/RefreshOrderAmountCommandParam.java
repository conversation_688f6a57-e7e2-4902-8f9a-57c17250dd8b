//package com.cosfo.ordercenter.domain.order.param.command;
//
//import com.cosfo.ordercenter.domain.delivery.param.MerchantDeliveryFeeSnapshotCommandParam;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//
///**
// * @author: monna.chen
// * @Date: 2023/10/17 15:21
// * @Description:
// */
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//public class RefreshOrderAmountCommandParam implements Serializable {
//    private static final long serialVersionUID = 4440787494323855639L;
//
//    /**
//     * 订单ID
//     */
//    private Long orderId;
//
//    /**
//     * 原订单运费
//     */
//    private BigDecimal oriDeliveryFee;
//
//    /**
//     * 更新后的运费
//     */
//    private MerchantDeliveryFeeSnapshotCommandParam deliveryFeeSnapshotDTO;
//
//}
