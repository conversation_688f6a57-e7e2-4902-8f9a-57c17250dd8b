package com.cosfo.ordercenter.domain.order.param.query;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ItemSaleQuantityQueryParam implements Serializable {

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 门店id
     */
    private Long merchantStoreId;

    /**
     * 商品项
     */
    private List<Long> itemIds;

    /**
     * 下单开始时间
     */
    private LocalDateTime startDay;

    /**
     * 下单结束时间
     */
    private LocalDateTime endDay;
}
