package com.cosfo.ordercenter.domain.order.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-09-22
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class OrderItemExtraQueryParam {

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 外部系统子订单号
     */
    private List<String> customerOrderItemIdList;
}
