package com.cosfo.ordercenter.domain.order.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderOmsQueryParam extends PageQueryParam {
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 详情页订单导出传订单编号
     */
    private Long orderId;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 配送仓库
     */
    private Integer warehouseType;
    /**
     * 注册手机号
     */
    private String phone;
    /**
     * 开始时间  finishTime
     */
    private LocalDateTime startTime;
    /**
     * 结束时间 finishTime
     */
    private LocalDateTime endTime;

    /**
     * 开始时间  createTime
     */
    private LocalDateTime createStartTime;
    /**
     * 结束时间 createTime
     */
    private LocalDateTime createEndTime;

    /**
     * 店铺Id
     */
    private List<Long> storeIds;

    /**
     * 账户ID
     */
    private List<Long> accountIds;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空")
    private Long supplierTenantId;
    /**
     * 供应商ids
     */
    private List<Long> supplierTenantIds;

    /**
     * 租户列表
     */
    private List<Long> tenantIds;

    /**
     * 订单状态
     */
    private List<Integer> statusList;

    /**
     * 1微信 2账期 3余额
     */
    private Integer payType;

    /**
     * 商品id
     */
    private List<Long> itemIds;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 订单明细id
     */
    private List<Long> orderItemIds;

    /**
     * list滚动查询，上次查询获取到的最大id
     */
    private Long maxId;

    /**
     * list每次获取数量，最大500
     */
    private Integer batchSize;

    /**
     * 外部系统订单号
     */
    private String customerOrderId;
}
