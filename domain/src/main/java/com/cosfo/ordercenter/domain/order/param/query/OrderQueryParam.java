package com.cosfo.ordercenter.domain.order.param.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderQueryParam extends PageQueryParam {
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 详情页订单导出传订单编号
     */
    private Long orderId;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 配送仓库
     */
    private Integer warehouseType;
    /**
     * 注册手机号
     */
    private String phone;
    /**
     * 开始时间  finishTime
     */
    private LocalDateTime startTime;
    /**
     * 结束时间 finishTime
     */
    private LocalDateTime endTime;

    /**
     * 开始时间  createTime
     */
    private LocalDateTime createStartTime;
    /**
     * 结束时间 createTime
     */
    private LocalDateTime createEndTime;

    /**
     * 店铺Id
     */
    private List<Long> storeIds;

    /**
     * 账户ID
     */
    private List<Long> accountIds;

    /**
     * 供应商id
     */
    private Long supplierTenantId;
    /**
     * 供应商ids
     */
    private List<Long> supplierTenantIds;

    /**
     * 租户列表
     */
    private List<Long> tenantIds;

    /**
     * 订单状态
     */
    private List<Integer> statusList;

    /**
     * 1微信 2账期 3余额
     */
    private Integer payType;

    /**
     * 商品id
     */
    private List<Long> itemIds;

    /**
     * 货品id
     */
    private Long skuId;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 订单明细id
     */
    private List<Long> orderItemIds;

    /**
     * list滚动查询，上次查询获取到的最大id
     */
    private Long maxId;

    /**
     * list每次获取数量，最大500
     */
    private Integer batchSize;

    /**
     * 订单编号
     */
    private List<String> orderNos;

    /**
     * 配送日期
     * = 查询
     */
    private LocalDate deliveryTime;

    /**
     * 配送开始时间
     */
    private LocalDateTime deliveryStartTime;

    /**
     * 配送结束时间
     */
    private LocalDateTime deliveryEndTime;

    /**
     * 排除订单id
     */
    private Long neOrderId;

    /**
     * 外部系统订单号列表
     */
    private List<String> customerOrderIds;


    /**
     * 订单来源:0：内部系统; 1：openapi调用; 2:总部代下单
     */
    private Integer orderSource;

    /**
     * 计划单编号
     */
    private String planOrderNo;

    /**
     * 0-普通订单,1=组合订单, 2=预售订单
     */
    private Integer orderType;

    /**
     * 查询结果是否按照orderId排序顺序返回
     */
    private Boolean sortOrderIdAsc;

    /**
     * 时间查询类型 1-下单时间 2-支付时间 3-配送时间 4-完成时间
     */
    private Integer timeQueryType;

    /**
     * 开始时间 根据时间查询类型
     */
    private LocalDateTime queryStartTime;
    /**
     * 结束时间 根据时间查询类型
     */
    private LocalDateTime queryEndTime;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;
}
