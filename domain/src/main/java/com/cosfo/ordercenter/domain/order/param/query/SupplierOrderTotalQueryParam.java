package com.cosfo.ordercenter.domain.order.param.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-10-19
 * @Description:
 */
@Data
public class SupplierOrderTotalQueryParam implements Serializable {

    private static final long serialVersionUID = -4619674268009040658L;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 供应商ids
     */
    private Collection<Long> supplierIds;

    /**
     * 订单状态
     */
    private List<Integer> statusList;

    /**
     * 订单类型 0无仓订单 1,三方仓订单 2自营仓订单
     */
    private Integer warehouseType;
}
