package com.cosfo.ordercenter.domain.order.repository;


import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
public interface OrderAddressQueryRepository {

    /**
     * 查询订单地址
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderAddressEntity> queryByOrderIds(List<Long> orderIds, Long tenantId);

    /**
     * 查询订单地址（单个）
     * @param orderId
     * @param tenantId
     * @return
     */
    OrderAddressEntity getByOrderId(Long orderId,Long tenantId);

}
