package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemCommandParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusBatchUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemCommandRepository {

    /**
     * 更新配送数量
     *
     * @param orderItemId
     * @param quantity
     * @return
     */
    int updateDeliveryQuantity(Long orderItemId, Integer quantity);


    /**
     * 更新订单明细最后售后时间
     *
     * @param dto
     * @return
     */
    Boolean updateAfterSaleExpiryTime(OrderItemUpdateParam dto);

    /**
     * 批量更新最后可售后时间
     *
     * @param orderItemList
     * @return
     */
    Boolean batchUpdateAfterSaleExpiryTime(List<OrderItemCommandParam> orderItemList);

    /**
     * 批量保存
     *
     * @param orderItemList
     * @return
     */
    List<OrderItemEntity> batchSave(List<OrderItemCommandParam> orderItemList);

    /**
     * 更新订单明细状态
     *
     * @param req
     * @return
     */
    Boolean updateStatus(OrderItemStatusUpdateParam req);

    /**
     * 批量更新订单项
     *
     * @param updateParam
     * @return
     */
    boolean batchUpdateStatus(OrderItemStatusBatchUpdateParam updateParam);

    /**
     * 更新城配仓号
     * @param orderId
     * @param sourceStoreNo
     * @param storeNo
     * @return
     */
    boolean updateStoreNo(Long orderId, Integer sourceStoreNo, Integer storeNo);
}
