package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemExtraCommandRepository {

    /**
     *  单条保存
     * @param orderItemExtra
     */
    void saveOrderItemExtra(OrderItemExtraAddParam orderItemExtra);

    /**
     * 批量保存
     * @param orderItemExtraList
     * @return
     */
    boolean batchSave(List<OrderItemExtraAddParam> orderItemExtraList);


}
