package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;

import java.util.List;

/**
 * <p>
 * 订单详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
public interface OrderItemQueryRepository {



    /**
     * 批量查询订单明细项
     *
     * @param orderIds
     * @param supplierIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> batchQueryOrderItemDetail(List<Long> orderIds, List<Long> supplierIds);


    /**
     * 统计sku数量
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySkuQuantity(Long tenantId, List<Long> orderIds);

    /**
     * 统计商品销售数量
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySaleQuantity(Long tenantId, List<Long> orderIds);




    /**
     * 根据orderId查询订单明细
     *
     * @param orderId
     * @return
     */
    List<OrderItemEntity> queryByOrderId(Long orderId);

    /**
     * 批量查询
     *
     * @param orderIds
     * @return
     */
    List<OrderItemEntity> batchQueryByOrderIds(List<Long> orderIds);

//    /**
//     * 查询订单明细项以及快照
//     * @param orderItemId
//     * @return
//     */
//    OrderItemWithSnapshot queryDetailById(Long orderItemId);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param orderItemIds
     * @return
     */
    List<OrderItemEntity> batchQuery(Long tenantId, List<Long> orderItemIds);




    /**
     * 根据 orderItemId 批量查询
     *
     * @param orderItemIds
     * @return
     */
    List<OrderItemEntity> queryByIds(List<Long> orderItemIds);




    /**
     * 根据orderId批量查询
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> queryOrderItemVOByOrderIds(Long tenantId, List<Long> orderIds);


    /**
     * 查询订单明细项以及快照
     * @param orderId
     * @return
     */
    List<OrderItemWithSnapshotEntity> queryItemWithSnapshotByOrderId(Long orderId);

    OrderItemEntity queryById(Long orderItemId);
}
