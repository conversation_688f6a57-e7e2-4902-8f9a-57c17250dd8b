package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderItemSnapshotCommandRepository {

    /**
     * 批量更新 snapshot taskId
     *
     * @param orderItemIds
     * @param orderId
     * @return
     */
    Boolean batchUpdateTaskId(List<Long> orderItemIds, Long orderId);

    /**
     * 批量保存
     * @param snapshotList
     * @return
     */
    boolean batchSave(List<OrderItemSnapshotAddParam> snapshotList);
}
