package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemSnapshotQueryParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderItemSnapshotQueryRepository {

    /**
     * 批量查询订单明细快照
     * @param queryReq
     * @return
     */
    List<OrderItemSnapshotEntity> queryList(OrderItemSnapshotQueryParam queryReq);

    /**
     * 按orderItemIds批量查询
     * @param orderItemIds
     * @return
     */
    List<OrderItemSnapshotEntity> queryByOrderItemIds(List<Long> orderItemIds);

    /**
     * 按orderIds批量查询
     * @param orderIds
     * @return
     */
    List<OrderItemSnapshotEntity> queryByOrderIds(List<Long> orderIds);

    /**
     * 按找orderItemId查询
     * @param orderItemId
     * @return
     */
    OrderItemSnapshotEntity queryByOrderItemId(Long orderItemId);

    /**
     * 查询订单项快照
     *
     * @param orderItems
     * @return
     */
    Map<Long, OrderItemSnapshotEntity> queryOrderItemSnapshot(List<Long> orderItemIds);
}
