package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.entity.*;
import com.cosfo.ordercenter.domain.order.param.query.*;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface OrderQueryRepository {

    /**
     * 计算订单金额
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    BigDecimal sumOrderTotalPrice(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款订单
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    Integer countPayOrderQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款店铺
     * @param start
     * @param end
     * @param tenantId
     * @param storeIds
     * @return
     */
    Integer countPayOrderStoreQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);


    /**
     * 计算待配送数量
     * @param tenantId
     * @return
     */
    Integer getWaitDeliveryNum(Long tenantId);

    /**
     * 计算待审核数量
     * @param tenantId
     * @return
     */
    Integer countByStatusList(Long tenantId, List<Integer> statusList);

    /**
     * 查询sku维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleQuantity(List<Long> skuIds,
                                                      Long tenantId,
                                                      LocalDateTime startTime,
                                                      LocalDateTime endTime);


    /**
     * 查询sku，仓库维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithStoreNoQuantity(List<Long> skuIds,
                                                                 Long tenantId,
                                                                 LocalDateTime startTime,
                                                                 LocalDateTime endTime);

    /**
     * 查询sku、城市维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithCityQuantity(List<Long> skuIds,
                                                              Long tenantId,
                                                              LocalDateTime startTime,
                                                              LocalDateTime endTime);


    /**
     * 查询订单明细列表
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderDetailEntity> queryOrderDetail(List<Long> orderIds, Long tenantId);


    /**
     * oms分页查询
     * @param orderOmsQueryParam
     * @return
     */
    PageInfo<OrderEntity> queryPage(OrderOmsQueryParam orderOmsQueryParam);

    /**
     * 分页查询
     * @param orderQueryParam
     * @return
     */
    PageInfo<OrderEntity> queryPage(OrderQueryParam orderQueryParam);


    /**
     * 订单列表
     * @param queryParam
     * @return
     */
    List<OrderEntity> queryList(OrderQueryParam queryParam);


    /**
     * 查询待配送订单
     * @param queryParam
     * @return
     */
    List<String> queryNeedDeliveryOrder(OrderDeliveryQueryParam queryParam);

    /**
     * 按订单no批量查询
     * @param orderNos
     * @return
     */
    List<OrderEntity> queryByOrderNos(List<String> orderNos);

    /**
     * 查询组合包订单
     * @param combineId
     * @param tenantId
     * @return
     */
    List<OrderEntity> queryByCombineId(Long combineId, Long tenantId);

    /**
     * 批量查询组合包订单
     * @param combineIds
     * @param tenantId
     * @return
     */
    List<OrderEntity> queryByCombineIds(Set<Long> combineIds, Long tenantId);


    /**
     * 统计订单数量
     * @param orderCountQueryParam
     * @return
     */
    Integer countOrderQuantity(OrderCountQueryParam orderCountQueryParam);


    /**
     * 查询小于配送时间切状态是4的订单
     *
     * @param autoFinishQueryParam
     * @return
     */
    List<OrderEntity> queryNeedAutoFinishedOrder(OrderAutoFinishQueryParam autoFinishQueryParam);

    /**
     * 查询门店在某天配送时间的订单
     * @param storeId
     * @param deliveryTime
     * @return
     */
    List<OrderEntity> queryByStoreIdAndDeliveryTime(Long storeId, LocalDateTime deliveryTime, Integer warehouseType);

    /**
     * 根据外部订单号查询订单信息
     * @param orderQueryParam
     * @return
     */
    List<OrderEntity> queryListByCustomerOrderIds(OrderQueryParam orderQueryParam);

    /**
     * 查询商品周期内售卖数量
     * @param saleQuantityQueryParam
     * @return
     */
    List<OrderItemSaleQuantityEntity> queryOrderItemSaleQuantity(ItemSaleQuantityQueryParam saleQuantityQueryParam);

    /**
     * 查询供应商订单信息
     * @param queryParam
     * @return
     */
    List<SupplierOrderEntity> querySupplierOrderList(SupplierOrderTotalQueryParam queryParam);

    /**
     * 根据id获取订单
     * @param orderId
     * @return
     */
    OrderEntity queryById(Long orderId);

    /**
     * 根据订单no查询
     * @param orderNo
     * @return
     */
    OrderEntity queryByNo(String orderNo);

    /**
     * 根据订单id批量查询
     * @param ids
     * @return
     */
    List<OrderEntity> queryByIds(List<Long> ids);

    /**
     * 根据订单no批量查询
     * @param nos
     * @return
     */
    List<OrderEntity> queryByNos(List<String> nos);

}
