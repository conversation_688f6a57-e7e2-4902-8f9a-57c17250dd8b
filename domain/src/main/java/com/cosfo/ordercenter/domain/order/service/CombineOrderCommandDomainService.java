package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.repository.CombineOrderCommandRepository;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CombineOrderCommandDomainService {

    @Resource
    private CombineOrderCommandRepository combineOrderCommandRepository;

    public Long add(java.lang.Long combineItemId, Long tenantId) {
        return combineOrderCommandRepository.add(combineItemId, tenantId);
    }
}
