package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressCommandRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class OrderAddressCommandDomainService {

    @Resource
    private OrderAddressCommandRepository orderAddressCommandRepository;

    public Long add(OrderAddressCommandParam addressCommandParam) {
        return orderAddressCommandRepository.add(addressCommandParam);
    }
}
