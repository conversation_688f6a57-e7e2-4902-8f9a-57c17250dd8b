package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.param.command.OrderCombineSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderCombineSnapshotCommandRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCombineSnapshotCommandDomainService {

    @Resource
    private OrderCombineSnapshotCommandRepository orderCombineSnapshotCommandRepository;

    public boolean batchSave(List<OrderCombineSnapshotAddParam> snapshotList) {
        return orderCombineSnapshotCommandRepository.batchSave(snapshotList);
    }
}
