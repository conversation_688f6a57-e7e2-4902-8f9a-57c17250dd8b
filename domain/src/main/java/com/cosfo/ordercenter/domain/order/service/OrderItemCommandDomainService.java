package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.repository.OrderItemCommandRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class OrderItemCommandDomainService {

    @Resource
    private OrderItemCommandRepository orderItemCommandRepository;

    public Boolean batchUpdateAfterSaleExpiryTime(List<OrderItemCommandParam> orderItemList) {
        return orderItemCommandRepository.batchUpdateAfterSaleExpiryTime(orderItemList);
    }


    public Boolean updateAfterSaleExpiryTime(OrderItemUpdateParam dto) {
        return orderItemCommandRepository.updateAfterSaleExpiryTime(dto);
    }

    public Boolean updateDeliveryQuantity(Long orderItemId, Integer quantity) {
        return orderItemCommandRepository.updateDeliveryQuantity(orderItemId, quantity) > 0;
    }

    public Boolean updateStatus(OrderItemStatusUpdateParam req) {
        return orderItemCommandRepository.updateStatus(req);
    }

    public List<OrderItemEntity> batchSave(List<OrderItemCommandParam> orderItemList) {
        return orderItemCommandRepository.batchSave(orderItemList);
    }

    public boolean batchUpdateStatus(OrderItemStatusBatchUpdateParam updateParam) {
        return orderItemCommandRepository.batchUpdateStatus(updateParam);
    }

    public boolean updateStoreNo(Long orderId, Integer sourceStoreNo, Integer storeNo) {
        return orderItemCommandRepository.updateStoreNo(orderId, sourceStoreNo, storeNo);
    }
}
