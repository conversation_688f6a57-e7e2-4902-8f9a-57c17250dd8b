package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemExtraCommandRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemExtraCommandDomainService {

    @Resource
    private OrderItemExtraCommandRepository orderItemExtraCommandRepository;

    public boolean batchSave(List<OrderItemExtraAddParam> orderItemExtraList) {
        return orderItemExtraCommandRepository.batchSave(orderItemExtraList);
    }
}
