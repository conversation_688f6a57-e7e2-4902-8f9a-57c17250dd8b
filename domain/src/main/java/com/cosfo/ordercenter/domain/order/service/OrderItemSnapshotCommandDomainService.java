package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotCommandRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemSnapshotCommandDomainService {

    @Resource
    private OrderItemSnapshotCommandRepository orderItemSnapshotCommandRepository;

    public boolean batchSave(List<OrderItemSnapshotAddParam> snapshotList) {
        return orderItemSnapshotCommandRepository.batchSave(snapshotList);
    }
}
