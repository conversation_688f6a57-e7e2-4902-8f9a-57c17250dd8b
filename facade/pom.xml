<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cosfo</groupId>
        <artifactId>order-center</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>order-center-facade</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
        </dependency>


        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>oms-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-client</artifactId>
        </dependency>

    </dependencies>
</project>