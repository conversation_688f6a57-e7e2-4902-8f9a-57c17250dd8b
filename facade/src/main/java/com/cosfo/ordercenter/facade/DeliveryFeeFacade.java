package com.cosfo.ordercenter.facade;

import net.xianmu.common.result.DubboResponse;
import net.xianmu.marketing.center.client.freight.provider.DistributionRulesProvider;
import net.xianmu.marketing.center.client.freight.req.DistributionRulesFeeReq;
import net.xianmu.marketing.center.client.freight.resp.DistributionRulesFeeResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DeliveryFeeFacade {

    @DubboReference
    private DistributionRulesProvider distributionRulesProvider;

    public DistributionRulesFeeResp queryDeliveryFee(DistributionRulesFeeReq distributionRulesFeeReq) {
        DubboResponse<DistributionRulesFeeResp> resp = distributionRulesProvider.queryDeliveryFee(distributionRulesFeeReq);
        if (resp.isSuccess()) {
            return resp.getData();
        }
        return null;
    }
}
