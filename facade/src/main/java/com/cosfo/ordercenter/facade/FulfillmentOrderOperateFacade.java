package com.cosfo.ordercenter.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.req.InsertAfterSaleLogisticsReq;
import net.summerfarm.ofc.client.req.RefundFreezeOrderReq;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FulfillmentOrderOperateFacade {

    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;

    /**
     * 未配送售后退款一阶段冻结履约单
     *
     * @param refundFreezeOrderReq 冻结请求参数
     * @return
     */
    public void refundFirstFreezeFulfillment(RefundFreezeOrderReq refundFreezeOrderReq) {
        DubboResponse<Void> response = fulfillmentOrderOperateProvider.refundFirstFreezeFulfillment(refundFreezeOrderReq);
        if (!response.isSuccess()) {
            throw new ProviderException("不好意思，申请失败，已经开始配送啦");
        }
    }

    /**
     * 插入售后物流
     *
     * @param insertAfterSaleLogisticsReq 插入售后物流要求
     * @return {@link DubboResponse}<{@link Void}>
     */
    public void insertAfterSaleLogistics(InsertAfterSaleLogisticsReq insertAfterSaleLogisticsReq) {
        DubboResponse<Void> response = fulfillmentOrderOperateProvider.insertAfterSaleLogistics(insertAfterSaleLogisticsReq);
        if (!response.isSuccess()) {
            throw new ProviderException("插入售后物流失败");
        }
    }
}
