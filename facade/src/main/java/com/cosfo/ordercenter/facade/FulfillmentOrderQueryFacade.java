package com.cosfo.ordercenter.facade;

import com.google.common.collect.Lists;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.req.ValidateCancelAfterSaleOrderReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FulfillmentOrderQueryFacade {


    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    public List<FulfillmentDeliveryResp> queryOrderDelivery(String orderNo) {
        QueryFulfillmentDeliveryReq deliveryReq = new QueryFulfillmentDeliveryReq();
        deliveryReq.setOrderNoList(Lists.newArrayList(orderNo));
        DubboResponse<List<FulfillmentDeliveryResp>> listDubboResponse = fulfillmentOrderQueryProvider.queryOrderDelivery(deliveryReq);
        if (listDubboResponse.isSuccess()) {
            return listDubboResponse.getData();
        }
        return Collections.emptyList();
    }

    /**
     * 验证售后单是否可以取消
     *
     * @param req 请求参数
     * @return {@link DubboResponse}<{@link Boolean}>
     */
    public Boolean validateCancelAfterSaleOrder(ValidateCancelAfterSaleOrderReq req) {
        DubboResponse<Boolean> response = fulfillmentOrderQueryProvider.validateCancelAfterSaleOrder(req);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException("验证售后单是否可以取消失败");
    }
}
