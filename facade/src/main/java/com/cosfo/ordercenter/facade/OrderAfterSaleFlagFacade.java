package com.cosfo.ordercenter.facade;

import com.cosfo.manage.client.order.aftersale.OrderAfterSaleProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class OrderAfterSaleFlagFacade {

    @DubboReference
    private OrderAfterSaleProvider orderAfterSaleProvider;

    public Boolean needSelfReviewFlag(OrderAfterSaleSelfReviewAgentReq req) {
        DubboResponse<Boolean> booleanDubboResponse = orderAfterSaleProvider.needSelfReviewFlag(req);
        if (booleanDubboResponse.isSuccess()) {
            return booleanDubboResponse.getData();
        }
        throw new ProviderException("品牌自审标识查询失败");
    }
}
