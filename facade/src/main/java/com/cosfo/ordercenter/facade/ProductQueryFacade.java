package com.cosfo.ordercenter.facade;

import com.cosfo.ordercenter.facade.dto.AllCategoryDTO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductQueryFacade {

    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;

    public Map<Long, AllCategoryDTO> querySkuCategory(List<Long> skuIds) {
        DubboResponse<List<ProductSkuDetailResp>> listDubboResponse = productsSkuQueryProvider.selectProductSkuDetailById(skuIds);

        if (!listDubboResponse.isSuccess()) {
            throw new BizException("查询商品分类失败");
        }
        List<ProductSkuDetailResp> data = listDubboResponse.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        Map<Long, AllCategoryDTO> skuCategoryMap = new HashMap<>();
        data.forEach(sku -> {
            AllCategoryDTO categoryDTO = AllCategoryDTO.builder()
                    .firstCategory(sku.getFirstCategory())
                    .firstCategoryId(sku.getFirstCategoryId())
                    .secondCategory(sku.getSecondCategory())
                    .secondCategoryId(sku.getSecondCategoryId())
                    .thirdCategory(sku.getCategoryName())
                    .thirdCategoryId(sku.getCategoryId())
                    .build();
            skuCategoryMap.put(sku.getSkuId(), categoryDTO);
        });
        return skuCategoryMap;
    }
}
