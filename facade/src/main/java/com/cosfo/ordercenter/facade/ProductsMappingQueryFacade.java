package com.cosfo.ordercenter.facade;

import com.cosfo.ordercenter.facade.converter.ProductsMappingConverter;
import com.cosfo.ordercenter.facade.dto.ProductsMappingDTO;
import com.cosfo.ordercenter.facade.input.ProductMappingQueryInput;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductsMappingQueryFacade {

    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;

    public List<ProductsMappingDTO> selectMappingList(ProductMappingQueryInput mappingQueryInput) {
        ProductMappingQueryReq productMappingQueryReq = ProductsMappingConverter.converterTOQueryReq(mappingQueryInput);
        DubboResponse<List<ProductsMappingResp>> response = productsMappingQueryProvider.selectMappingList(productMappingQueryReq);
        if (response.isSuccess()) {
            return ProductsMappingConverter.converterToDTOList(response.getData());
        }
        log.error("查询商品映射关系失败,{}", response);
        throw new ProviderException("查询商品映射关系失败");
    }
}
