package com.cosfo.ordercenter.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterCommandProvider;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyBySpecifyWarehouseReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderOccupyResDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderReleaseResDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SaleInventoryCenterCommandFacade {


    @DubboReference
    private SaleInventoryCenterCommandProvider saleInventoryCenterCommandProvider;

    public OrderReleaseResDTO orderReleaseBySpecifySku(OrderReleaseBySpecifySkuReqDTO orderReleaseBySpecifySkuReqDTO) {
        DubboResponse<OrderReleaseResDTO> response = saleInventoryCenterCommandProvider.orderReleaseBySpecifySku(orderReleaseBySpecifySkuReqDTO);
        if (response.isSuccess()) {
            return response.getData();
        }
        log.error("订单释放指定sku失败,{}", JSON.toJSONString(response));
        throw new ProviderException("订单释放指定sku失败");
    }

    public OrderOccupyResDTO orderOccupy(OrderOccupyReqDTO orderOccupyReqDTO) {
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCenterCommandProvider.orderOccupy(orderOccupyReqDTO);
        if (response.isSuccess()) {
            return response.getData();
        }
        log.error("订单占用库存失败,{}", JSON.toJSONString(response));
        throw new ProviderException("订单占用库存失败，" + response.getMsg());
    }

    /**
     * 订单占用，指定单仓库，整单满足
     * orderOccupyBySpecifyWarehouseAndSku
     *
     * @param orderOccupyBySpecifyWarehouseReqDTO
     * @return
     */
    public OrderOccupyResDTO orderOccupyBySpecifyWarehouseAndSku(
            OrderOccupyBySpecifyWarehouseReqDTO orderOccupyBySpecifyWarehouseReqDTO) {
        DubboResponse<OrderOccupyResDTO> response = saleInventoryCenterCommandProvider.orderOccupyBySpecifyWarehouseAndSku(orderOccupyBySpecifyWarehouseReqDTO);
        if (response.isSuccess()) {
            return response.getData();
        }
        log.error("订单占用库存失败,{}", JSON.toJSONString(response));
        throw new ProviderException("订单占用库存失败，" + response.getMsg());
    }
}
