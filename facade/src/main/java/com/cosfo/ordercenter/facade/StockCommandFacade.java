package com.cosfo.ordercenter.facade;

import com.cofso.item.client.enums.StockRecordType;
import com.cofso.item.client.provider.StockProvider;
import com.cosfo.ordercenter.facade.input.OrderAfterSaleInput;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockCommandFacade {

    @DubboReference
    private StockProvider stockProvider;

    /**
     * 处理本地库存
     * @param tenantId 用户信息
     * @param recordType 库存变动类型
     * @param itemId itemId
     * @param addAmount 增加数量
     * @param recordNo 记录编号
     * @reture
     */
    public Boolean increaseSelfStock(Long tenantId, StockRecordType recordType, Long itemId, Integer addAmount, String recordNo) {
        DubboResponse<Boolean> response = stockProvider.increaseSelfStock(tenantId, recordType, itemId, addAmount, recordNo);
        if (!response.isSuccess()) {
            log.error("增加本地库存失败, itemId:{}, addAmount:{}, recordNo:{}", itemId, addAmount, recordNo);
            throw new ProviderException("增加本地库存失败");
        }
        return response.getData();
    }



}
