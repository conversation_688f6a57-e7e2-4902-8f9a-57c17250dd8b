package com.cosfo.ordercenter.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.facade.dto.MerchantAddressResultDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 14:38
 * @Description:
 */
@Service
@Slf4j
public class StoreQueryFacade {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;
    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;

    /**
     * 查询正常的门店地址
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    public MerchantAddressResultResp queryDefaultAddress(Long storeId, Long tenantId) {
        if (Objects.isNull(storeId)) {
            throw new ParamsException("门店Id不能为空");
        }
        if (Objects.isNull(tenantId)) {
            throw new ParamsException("租户Id不能为空");
        }
        return selectByStoreId(storeId, tenantId);
    }

    public MerchantAddressResultResp selectByStoreId(Long storeId, Long tenantId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setStoreId(storeId);
        merchantAddressQueryReq.setTenantId(tenantId);
        merchantAddressQueryReq.setDefaultFlag(DeliveryConstant.DEFAULT_STATUS);
        merchantAddressQueryReq.setStatus(DeliveryConstant.STORE_NORMAL_STATUS);
        DubboResponse<List<MerchantAddressResultResp>> resp = merchantAddressQueryProvider.getMerchantAddressList(merchantAddressQueryReq);
        if (resp.isSuccess()) {
            List<MerchantAddressResultResp> merchantAddressList = resp.getData();
            if (CollectionUtil.isNotEmpty(merchantAddressList)) {
                return merchantAddressList.get(0);
            }
        }
        throw new BizException("门店地址信息不存在");
    }


    /**
     * 查询门店名称
     * @param storeId
     * @return
     */
    public String queryStoreName(Long storeId) {
        if(storeId == null){
            return null;
        }
        try {
            MerchantStoreResultResp storeResultResp = RpcResponseUtil.handler(merchantStoreQueryProvider.getMerchantStoreById(storeId));
            if(storeResultResp != null){
                return storeResultResp.getStoreName();
            }
        } catch (Exception e) {
            log.error("查询门店信息异常，storeId={}", storeId, e);
        }
        return null;
    }


    public Map<Long, MerchantStoreResultResp> queryStoreByIds(List<Long> storeIdList) {
        if(CollectionUtils.isEmpty(storeIdList)){
            return Collections.emptyMap();
        }

        List<MerchantStoreResultResp> storeResultResps = RpcResponseUtil.handler(merchantStoreQueryProvider.getMerchantStoreByIds(storeIdList));
        if(CollectionUtils.isEmpty(storeResultResps)){
            return Collections.emptyMap();
        }

        return storeResultResps.stream().collect(Collectors.toMap(MerchantStoreResultResp::getId, Function.identity(), (v1, v2) -> v1));
    }
}
