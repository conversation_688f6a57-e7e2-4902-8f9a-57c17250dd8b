package com.cosfo.ordercenter.facade;

import com.cosfo.manage.client.tenant.TenantProvider;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class TenantQueryFacade {

    @DubboReference
    private TenantProvider tenantProvider;

    /**
     * 查询门店是否开启订单审核，开启订单审核需要更新订单状态为待审核
     * @param storeId
     * @return true-开启 false-关闭
     */
    public Boolean getOrderAuditSwitch(Long storeId) {
        DubboResponse<Boolean> response = tenantProvider.getOrderAuditSwitch(storeId);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException("查询门店是否开启订单审核失败");
    }

    /**
     * 查询门店是否开启售后审核开关，具体类型见flowRuleAuditBizType
     * @see com.cosfo.manage.client.enums.FlowRuleAuditBizTypeEnum
     * @param storeId
     * @param flowRuleAuditBizType
     * @return
     */
    public Boolean getAfterSaleAuditSwitch(Long storeId, Integer flowRuleAuditBizType) {
        DubboResponse<Boolean> response = tenantProvider.getAfterSaleAuditSwitch(storeId, flowRuleAuditBizType);
        if (response.isSuccess()) {
            return response.getData();
        }
        throw new ProviderException("查询门店是否开启售后审核开关失败");
    }
}
