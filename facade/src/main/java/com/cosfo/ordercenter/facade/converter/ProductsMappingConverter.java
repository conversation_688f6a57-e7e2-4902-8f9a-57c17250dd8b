package com.cosfo.ordercenter.facade.converter;

import com.cosfo.ordercenter.facade.dto.ProductsMappingDTO;
import com.cosfo.ordercenter.facade.input.ProductMappingQueryInput;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProductsMappingConverter {

    private ProductsMappingConverter() {
    }

    public static ProductMappingQueryReq converterTOQueryReq(ProductMappingQueryInput input) {
        if (input == null) {
            return null;
        }
        ProductMappingQueryReq productMappingQueryReq = new ProductMappingQueryReq();
        productMappingQueryReq.setTenantId(input.getTenantId());
        productMappingQueryReq.setSpuIds(input.getSpuIds());
        productMappingQueryReq.setSkuIds(input.getSkuIds());
        productMappingQueryReq.setSkuList(input.getSkuList());
        productMappingQueryReq.setSpuList(input.getSpuList());
        productMappingQueryReq.setAgentSkuIds(input.getAgentSkuIds());
        productMappingQueryReq.setAgentTenantId(input.getAgentTenantId());
        return productMappingQueryReq;
    }

    public static List<ProductsMappingDTO> converterToDTOList(List<ProductsMappingResp> respList) {

        if (respList == null) {
            return Collections.emptyList();
        }
        List<ProductsMappingDTO> productsMappingDTOList = new ArrayList<>();
        for (ProductsMappingResp productsMappingResp : respList) {
            productsMappingDTOList.add(toProductsMappingDTO(productsMappingResp));
        }
        return productsMappingDTOList;
    }

    public static ProductsMappingDTO toProductsMappingDTO(ProductsMappingResp productsMappingResp) {
        if (productsMappingResp == null) {
            return null;
        }
        ProductsMappingDTO productsMappingDTO = new ProductsMappingDTO();
        productsMappingDTO.setId(productsMappingResp.getId());
        productsMappingDTO.setSku(productsMappingResp.getSku());
        productsMappingDTO.setSkuId(productsMappingResp.getSkuId());
        productsMappingDTO.setAgentSkuId(productsMappingResp.getAgentSkuId());
        productsMappingDTO.setSpu(productsMappingResp.getSpu());
        productsMappingDTO.setSpuId(productsMappingResp.getSpuId());
        productsMappingDTO.setAgentSpuId(productsMappingResp.getAgentSpuId());
        productsMappingDTO.setTenantId(productsMappingResp.getTenantId());
        productsMappingDTO.setAgentTenantId(productsMappingResp.getAgentTenantId());
        return productsMappingDTO;
    }
}
