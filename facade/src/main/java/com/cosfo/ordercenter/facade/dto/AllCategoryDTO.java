package com.cosfo.ordercenter.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: monna.chen
 * @Date: 2023/9/13 14:29
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllCategoryDTO {
    /**
     * 一级类目
     **/
    private String firstCategory;
    private Long firstCategoryId;
    /**
     * 二级类目
     **/
    private String secondCategory;
    private Long secondCategoryId;
    /**
     * 三级类目
     **/
    private String thirdCategory;
    private Long thirdCategoryId;
}
