package com.cosfo.ordercenter.facade.input;

import lombok.Data;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 订单释放DTO，指定SKU
 */
@Data
public class OrderReleaseBySpecifySkuInput implements Serializable {

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 仓库租户限定，选填，用于限定调度仓库
     * 不传-调度鲜沐仓+Saas仓
     * 鲜沐-调度鲜沐仓
     * Saas-调度鲜沐仓+Saas仓
     */
    private Long warehouseTenantId;

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单子编号，分段调度库存场景使用（省心送）
     */
    private String orderSubNo;

    /**
     * 订单类别
     * @see net.summerfarm.wms.instore.enums.SaleStockChangeTypeEnum
     *
     * Saas销售场景
     * 冻结库存{@link net.summerfarm.wms.instore.enums.SaleStockChangeTypeEnum#PLACE_ORDER}
     * 解冻库存{@link net.summerfarm.wms.instore.enums.SaleStockChangeTypeEnum#CANCEL_ORDER}
     *
     */
    private String orderType;

    /**
     * 操作单号
     */
    private String operatorNo;

    /**
     * 幂等单号
     */
    private String idempotentNo;

    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * poi
     */
    private String poi;

    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 配送地址id
     */
    private Long contactId;

    /**
     * 商户id 租户为1（鲜沐时必传）
     */
    private Long merchantId;

    /**
     * SourceEnum
     * 订单来源 租户为1（鲜沐时必传）
     * 不同的订单类型返回不同的截单时间
     * net.summerfarm.wnc.client.enums.SourceEnum
     * net.xianmu.common.enums.base.wnc.OrderSourceEnum
     */
    private Integer source;

    /**
     * 是否加单 true 加单 false不加单
     */
    private Boolean addOrderFlag;

    /**
     * 订单释放明细
     */
    private List<OrderReleaseSkuDetailReqDTO> orderReleaseSkuDetailReqDTOS;

    /**
     * 操作人名称
     */
    private String operatorName;

}
