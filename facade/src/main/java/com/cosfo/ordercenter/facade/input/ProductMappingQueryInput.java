package com.cosfo.ordercenter.facade.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/** 入参不能全部为空 **/
@Data
public class ProductMappingQueryInput implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 品牌方租户Id
     */
    private Long tenantId;
    /**
     * spuIds （saas）
     **/
    private List<Long> spuIds;
    /**
     * skuIds （saas）
     */
    private List<Long> skuIds;
    /** 外部sku编码 **/
    private List<String> skuList;
    /** 外部SPU编码 **/
    private List<String> spuList;

    /** 外部SKU ID **/
    private List<Long> agentSkuIds;
    /** 代理租户ID **/
    private Long agentTenantId;

}
