package com.cosfo.ordercenter.infrastructure.converter.aftersale;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAddCommand;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.github.pagehelper.PageInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderAfterSaleConverter {
    private OrderAfterSaleConverter() {
    }


    public static List<OrderAfterSale> convertToOrderAfterSaleList(List<OrderAfterSaleAddCommand> orderAfterSales) {

        if (orderAfterSales == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSale> orderAfterSaleList = new ArrayList<>();
        for (OrderAfterSaleAddCommand orderAfterSaleAddCommand : orderAfterSales) {
            orderAfterSaleList.add(toOrderAfterSale(orderAfterSaleAddCommand));
        }
        return orderAfterSaleList;
    }

    public static List<OrderAfterSale> convertToOrderAfterSales(List<OrderAfterSaleEntity> afterSaleEntities) {


        if (afterSaleEntities == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSale> orderAfterSaleList = new ArrayList<>();
        for (OrderAfterSaleEntity orderAfterSaleEntity : afterSaleEntities) {
            orderAfterSaleList.add(convertToOrderAfterSale(orderAfterSaleEntity));
        }
        return orderAfterSaleList;
    }

    public static OrderAfterSale toOrderAfterSale(OrderAfterSaleAddCommand orderAfterSaleAddCommand) {
        if (orderAfterSaleAddCommand == null) {
            return null;
        }
        OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(orderAfterSaleAddCommand.getId());
        orderAfterSale.setTenantId(orderAfterSaleAddCommand.getTenantId());
        orderAfterSale.setOrderId(orderAfterSaleAddCommand.getOrderId());
        orderAfterSale.setOrderItemId(orderAfterSaleAddCommand.getOrderItemId());
        orderAfterSale.setStoreId(orderAfterSaleAddCommand.getStoreId());
        orderAfterSale.setAccountId(orderAfterSaleAddCommand.getAccountId());
        orderAfterSale.setAfterSaleOrderNo(orderAfterSaleAddCommand.getAfterSaleOrderNo());
        orderAfterSale.setAmount(orderAfterSaleAddCommand.getAmount());
        orderAfterSale.setAfterSaleType(orderAfterSaleAddCommand.getAfterSaleType());
        orderAfterSale.setServiceType(orderAfterSaleAddCommand.getServiceType());
        orderAfterSale.setApplyPrice(orderAfterSaleAddCommand.getApplyPrice());
        orderAfterSale.setTotalPrice(orderAfterSaleAddCommand.getTotalPrice());
        orderAfterSale.setDeliveryFee(orderAfterSaleAddCommand.getDeliveryFee());
        orderAfterSale.setReason(orderAfterSaleAddCommand.getReason());
        orderAfterSale.setUserRemark(orderAfterSaleAddCommand.getUserRemark());
        orderAfterSale.setProofPicture(orderAfterSaleAddCommand.getProofPicture());
        orderAfterSale.setStatus(orderAfterSaleAddCommand.getStatus());
        orderAfterSale.setHandleRemark(orderAfterSaleAddCommand.getHandleRemark());
        orderAfterSale.setOperatorName(orderAfterSaleAddCommand.getOperatorName());
        orderAfterSale.setCreateTime(orderAfterSaleAddCommand.getCreateTime());
        orderAfterSale.setUpdateTime(orderAfterSaleAddCommand.getUpdateTime());
        orderAfterSale.setFinishedTime(orderAfterSaleAddCommand.getFinishedTime());
        orderAfterSale.setHandleTime(orderAfterSaleAddCommand.getHandleTime());
        orderAfterSale.setRecycleTime(orderAfterSaleAddCommand.getRecycleTime());
        orderAfterSale.setRecycleDetails(orderAfterSaleAddCommand.getRecycleDetails());
        orderAfterSale.setResponsibilityType(orderAfterSaleAddCommand.getResponsibilityType());
        orderAfterSale.setStoreNo(orderAfterSaleAddCommand.getStoreNo());
        orderAfterSale.setWarehouseType(orderAfterSaleAddCommand.getWarehouseType());
        orderAfterSale.setAutoFinishedTime(orderAfterSaleAddCommand.getAutoFinishedTime());
        orderAfterSale.setApplyQuantity(orderAfterSaleAddCommand.getApplyQuantity());
        orderAfterSale.setAdminRemark(orderAfterSaleAddCommand.getAdminRemark());
        orderAfterSale.setAdminRemarkTime(orderAfterSaleAddCommand.getAdminRemarkTime());
        orderAfterSale.setSecondHandleRemark(orderAfterSaleAddCommand.getSecondHandleRemark());
        orderAfterSale.setReturnAddressId(orderAfterSaleAddCommand.getReturnAddressId());
        orderAfterSale.setReturnWarehouseNo(orderAfterSaleAddCommand.getReturnWarehouseNo());
        orderAfterSale.setCustomerAfterSaleOrderNo(orderAfterSaleAddCommand.getCustomerAfterSaleOrderNo());
        orderAfterSale.setServiceProviderAuditTime(orderAfterSaleAddCommand.getServiceProviderAuditTime());
        orderAfterSale.setRecyclePicture(orderAfterSaleAddCommand.getRecyclePicture());
        orderAfterSale.setRecycleQuantityDetail(orderAfterSaleAddCommand.getRecycleQuantityDetail());
        return orderAfterSale;
    }

    public static List<OrderAfterSaleEntity> convertToOrderAfterSaleEntityList(List<OrderAfterSale> orderAfterSales) {

        if (orderAfterSales == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleEntity> orderAfterSaleEntityList = new ArrayList<>();
        for (OrderAfterSale orderAfterSale : orderAfterSales) {
            orderAfterSaleEntityList.add(toOrderAfterSaleEntity(orderAfterSale));
        }
        return orderAfterSaleEntityList;
    }

    public static OrderAfterSaleEntity toOrderAfterSaleEntity(OrderAfterSale orderAfterSale) {
        if (orderAfterSale == null) {
            return null;
        }
        OrderAfterSaleEntity orderAfterSaleEntity = new OrderAfterSaleEntity();
        orderAfterSaleEntity.setId(orderAfterSale.getId());
        orderAfterSaleEntity.setTenantId(orderAfterSale.getTenantId());
        orderAfterSaleEntity.setOrderId(orderAfterSale.getOrderId());
        orderAfterSaleEntity.setOrderItemId(orderAfterSale.getOrderItemId());
        orderAfterSaleEntity.setStoreId(orderAfterSale.getStoreId());
        orderAfterSaleEntity.setAccountId(orderAfterSale.getAccountId());
        orderAfterSaleEntity.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
        orderAfterSaleEntity.setAmount(orderAfterSale.getAmount());
        orderAfterSaleEntity.setAfterSaleType(orderAfterSale.getAfterSaleType());
        orderAfterSaleEntity.setServiceType(orderAfterSale.getServiceType());
        orderAfterSaleEntity.setApplyPrice(orderAfterSale.getApplyPrice());
        orderAfterSaleEntity.setTotalPrice(orderAfterSale.getTotalPrice());
        orderAfterSaleEntity.setDeliveryFee(orderAfterSale.getDeliveryFee());
        orderAfterSaleEntity.setReason(orderAfterSale.getReason());
        orderAfterSaleEntity.setUserRemark(orderAfterSale.getUserRemark());
        orderAfterSaleEntity.setProofPicture(orderAfterSale.getProofPicture());
        orderAfterSaleEntity.setStatus(orderAfterSale.getStatus());
        orderAfterSaleEntity.setHandleRemark(orderAfterSale.getHandleRemark());
        orderAfterSaleEntity.setOperatorName(orderAfterSale.getOperatorName());
        orderAfterSaleEntity.setCreateTime(orderAfterSale.getCreateTime());
        orderAfterSaleEntity.setUpdateTime(orderAfterSale.getUpdateTime());
        orderAfterSaleEntity.setFinishedTime(orderAfterSale.getFinishedTime());
        orderAfterSaleEntity.setHandleTime(orderAfterSale.getHandleTime());
        orderAfterSaleEntity.setRecycleTime(orderAfterSale.getRecycleTime());
        orderAfterSaleEntity.setRecycleDetails(orderAfterSale.getRecycleDetails());
        orderAfterSaleEntity.setResponsibilityType(orderAfterSale.getResponsibilityType());
        orderAfterSaleEntity.setStoreNo(orderAfterSale.getStoreNo());
        orderAfterSaleEntity.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleEntity.setAutoFinishedTime(orderAfterSale.getAutoFinishedTime());
        orderAfterSaleEntity.setApplyQuantity(orderAfterSale.getApplyQuantity());
        orderAfterSaleEntity.setAdminRemark(orderAfterSale.getAdminRemark());
        orderAfterSaleEntity.setAdminRemarkTime(orderAfterSale.getAdminRemarkTime());
        orderAfterSaleEntity.setSecondHandleRemark(orderAfterSale.getSecondHandleRemark());
        orderAfterSaleEntity.setReturnAddressId(orderAfterSale.getReturnAddressId());
        orderAfterSaleEntity.setReturnWarehouseNo(orderAfterSale.getReturnWarehouseNo());
        orderAfterSaleEntity.setCustomerAfterSaleOrderNo(orderAfterSale.getCustomerAfterSaleOrderNo());
        orderAfterSaleEntity.setServiceProviderAuditTime(orderAfterSale.getServiceProviderAuditTime());
        orderAfterSaleEntity.setRecyclePicture(orderAfterSale.getRecyclePicture());
        orderAfterSaleEntity.setRecycleQuantityDetail(orderAfterSale.getRecycleQuantityDetail());
        return orderAfterSaleEntity;
    }

    public static PageInfo<OrderAfterSaleEntity> converterToEntityPage(Page<OrderAfterSale> afterSalePage) {
        PageInfo<OrderAfterSaleEntity> pageInfo = new PageInfo<>();
        pageInfo.setList(convertToOrderAfterSaleEntityList(afterSalePage.getRecords()));
        pageInfo.setTotal(afterSalePage.getTotal());
        pageInfo.setPageNum((int) afterSalePage.getCurrent());
        pageInfo.setPageSize((int) afterSalePage.getSize());
        return pageInfo;
    }

    public static OrderAfterSale convertToOrderAfterSale(OrderAfterSaleEntity afterSaleEntity) {

        if (afterSaleEntity == null) {
            return null;
        }
        OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setRefundReceipt(afterSaleEntity.getRefundReceipt());
        orderAfterSale.setId(afterSaleEntity.getId());
        orderAfterSale.setTenantId(afterSaleEntity.getTenantId());
        orderAfterSale.setOrderId(afterSaleEntity.getOrderId());
        orderAfterSale.setOrderItemId(afterSaleEntity.getOrderItemId());
        orderAfterSale.setStoreId(afterSaleEntity.getStoreId());
        orderAfterSale.setAccountId(afterSaleEntity.getAccountId());
        orderAfterSale.setAfterSaleOrderNo(afterSaleEntity.getAfterSaleOrderNo());
        orderAfterSale.setAmount(afterSaleEntity.getAmount());
        orderAfterSale.setAfterSaleType(afterSaleEntity.getAfterSaleType());
        orderAfterSale.setServiceType(afterSaleEntity.getServiceType());
        orderAfterSale.setApplyPrice(afterSaleEntity.getApplyPrice());
        orderAfterSale.setTotalPrice(afterSaleEntity.getTotalPrice());
        orderAfterSale.setDeliveryFee(afterSaleEntity.getDeliveryFee());
        orderAfterSale.setReason(afterSaleEntity.getReason());
        orderAfterSale.setUserRemark(afterSaleEntity.getUserRemark());
        orderAfterSale.setProofPicture(afterSaleEntity.getProofPicture());
        orderAfterSale.setStatus(afterSaleEntity.getStatus());
        orderAfterSale.setHandleRemark(afterSaleEntity.getHandleRemark());
        orderAfterSale.setOperatorName(afterSaleEntity.getOperatorName());
        orderAfterSale.setCreateTime(afterSaleEntity.getCreateTime());
        orderAfterSale.setUpdateTime(afterSaleEntity.getUpdateTime());
        orderAfterSale.setFinishedTime(afterSaleEntity.getFinishedTime());
        orderAfterSale.setHandleTime(afterSaleEntity.getHandleTime());
        orderAfterSale.setRecycleTime(afterSaleEntity.getRecycleTime());
        orderAfterSale.setRecycleDetails(afterSaleEntity.getRecycleDetails());
        orderAfterSale.setResponsibilityType(afterSaleEntity.getResponsibilityType());
        orderAfterSale.setStoreNo(afterSaleEntity.getStoreNo());
        orderAfterSale.setWarehouseType(afterSaleEntity.getWarehouseType());
        orderAfterSale.setAutoFinishedTime(afterSaleEntity.getAutoFinishedTime());
        orderAfterSale.setApplyQuantity(afterSaleEntity.getApplyQuantity());
        orderAfterSale.setAdminRemark(afterSaleEntity.getAdminRemark());
        orderAfterSale.setAdminRemarkTime(afterSaleEntity.getAdminRemarkTime());
        orderAfterSale.setSecondHandleRemark(afterSaleEntity.getSecondHandleRemark());
        orderAfterSale.setReturnAddressId(afterSaleEntity.getReturnAddressId());
        orderAfterSale.setReturnWarehouseNo(afterSaleEntity.getReturnWarehouseNo());
        orderAfterSale.setCustomerAfterSaleOrderNo(afterSaleEntity.getCustomerAfterSaleOrderNo());
        orderAfterSale.setServiceProviderAuditTime(afterSaleEntity.getServiceProviderAuditTime());
        orderAfterSale.setRecyclePicture(afterSaleEntity.getRecyclePicture());
        orderAfterSale.setRecycleQuantityDetail(afterSaleEntity.getRecycleQuantityDetail());
        return orderAfterSale;
    }
}
