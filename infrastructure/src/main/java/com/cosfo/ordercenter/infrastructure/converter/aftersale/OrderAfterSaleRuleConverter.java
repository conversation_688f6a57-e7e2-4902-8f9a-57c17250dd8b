package com.cosfo.ordercenter.infrastructure.converter.aftersale;

import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleRule;

/**
 * <AUTHOR>
 */
public class OrderAfterSaleRuleConverter {

    private OrderAfterSaleRuleConverter() {
    }

    public static OrderAfterSaleRuleCommandParam toOrderAfterSaleRuleCommandParam(OrderAfterSaleRule orderAfterSaleRule) {


        if (orderAfterSaleRule == null) {
            return null;
        }
        OrderAfterSaleRuleCommandParam orderAfterSaleRuleCommandParam = new OrderAfterSaleRuleCommandParam();
        orderAfterSaleRuleCommandParam.setId(orderAfterSaleRule.getId());
        orderAfterSaleRuleCommandParam.setTenantId(orderAfterSaleRule.getTenantId());
        orderAfterSaleRuleCommandParam.setDeliveryType(orderAfterSaleRule.getDeliveryType());
        orderAfterSaleRuleCommandParam.setApplyEndTime(orderAfterSaleRule.getApplyEndTime());
        orderAfterSaleRuleCommandParam.setAutoFinishedTime(orderAfterSaleRule.getAutoFinishedTime());
        orderAfterSaleRuleCommandParam.setDealType(orderAfterSaleRule.getDealType());
        orderAfterSaleRuleCommandParam.setCreateTime(orderAfterSaleRule.getCreateTime());
        orderAfterSaleRuleCommandParam.setUpdateTime(orderAfterSaleRule.getUpdateTime());
        orderAfterSaleRuleCommandParam.setType(orderAfterSaleRule.getType());
        orderAfterSaleRuleCommandParam.setDefaultFlag(orderAfterSaleRule.getDefaultFlag());
        orderAfterSaleRuleCommandParam.setRule(orderAfterSaleRule.getRule());
        return orderAfterSaleRuleCommandParam;
    }


}
