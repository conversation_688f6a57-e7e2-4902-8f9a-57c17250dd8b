package com.cosfo.ordercenter.infrastructure.converter.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.MerchantDeliveryFeeRuleEntity;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryFeeRule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MerchantDeliveryFeeRuleConverter {
    private MerchantDeliveryFeeRuleConverter() {
    }

    public static List<MerchantDeliveryFeeRuleEntity> convertFeeRuleEntity(List<MerchantDeliveryFeeRule> deliveryFeeRules) {
        if (deliveryFeeRules == null) {
            return Collections.emptyList();
        }
        List<MerchantDeliveryFeeRuleEntity> merchantDeliveryFeeRuleEntityList = new ArrayList<>();
        for (MerchantDeliveryFeeRule merchantDeliveryFeeRule : deliveryFeeRules) {
            merchantDeliveryFeeRuleEntityList.add(toMerchantDeliveryFeeRuleEntity(merchantDeliveryFeeRule));
        }
        return merchantDeliveryFeeRuleEntityList;
    }

    public static MerchantDeliveryFeeRuleEntity toMerchantDeliveryFeeRuleEntity(MerchantDeliveryFeeRule merchantDeliveryFeeRule) {
        if (merchantDeliveryFeeRule == null) {
            return null;
        }
        MerchantDeliveryFeeRuleEntity merchantDeliveryFeeRuleEntity = new MerchantDeliveryFeeRuleEntity();
        merchantDeliveryFeeRuleEntity.setId(merchantDeliveryFeeRule.getId());
        merchantDeliveryFeeRuleEntity.setTenantId(merchantDeliveryFeeRule.getTenantId());
        merchantDeliveryFeeRuleEntity.setType(merchantDeliveryFeeRule.getType());
        merchantDeliveryFeeRuleEntity.setDeliveryFee(merchantDeliveryFeeRule.getDeliveryFee());
        merchantDeliveryFeeRuleEntity.setFreeDeliveryPrice(merchantDeliveryFeeRule.getFreeDeliveryPrice());
        merchantDeliveryFeeRuleEntity.setCreateTime(merchantDeliveryFeeRule.getCreateTime());
        merchantDeliveryFeeRuleEntity.setUpdateTime(merchantDeliveryFeeRule.getUpdateTime());
        merchantDeliveryFeeRuleEntity.setRuleType(merchantDeliveryFeeRule.getRuleType());
        merchantDeliveryFeeRuleEntity.setPriceType(merchantDeliveryFeeRule.getPriceType());
        merchantDeliveryFeeRuleEntity.setRelateNumber(merchantDeliveryFeeRule.getRelateNumber());
        merchantDeliveryFeeRuleEntity.setDefaultType(merchantDeliveryFeeRule.getDefaultType());
        merchantDeliveryFeeRuleEntity.setFreeDeliveryType(merchantDeliveryFeeRule.getFreeDeliveryType());
        merchantDeliveryFeeRuleEntity.setFreeDeliveryQuantity(merchantDeliveryFeeRule.getFreeDeliveryQuantity());
        merchantDeliveryFeeRuleEntity.setPriority(merchantDeliveryFeeRule.getPriority());
        merchantDeliveryFeeRuleEntity.setHitItemIds(merchantDeliveryFeeRule.getHitItemIds());
        merchantDeliveryFeeRuleEntity.setHitAreas(merchantDeliveryFeeRule.getHitAreas());
        merchantDeliveryFeeRuleEntity.setIncludeNewFlag(merchantDeliveryFeeRule.getIncludeNewFlag());
        return merchantDeliveryFeeRuleEntity;
    }
}
