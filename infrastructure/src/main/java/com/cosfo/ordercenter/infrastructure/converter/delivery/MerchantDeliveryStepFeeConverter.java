package com.cosfo.ordercenter.infrastructure.converter.delivery;

import com.cosfo.ordercenter.client.resp.delivery.DeliveryFeeCalRuleDTO;
import com.cosfo.ordercenter.domain.delivery.entity.MerchantDeliveryStepFeeEntity;
import com.cosfo.ordercenter.infrastructure.model.delivery.DeliveryFeeCalRule;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryStepFee;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MerchantDeliveryStepFeeConverter {
    private MerchantDeliveryStepFeeConverter() {
    }

    public static List<MerchantDeliveryStepFeeEntity> convertToEntityList(List<MerchantDeliveryStepFee> stepFeeList) {

        if (stepFeeList == null) {
            return Collections.emptyList();
        }
        List<MerchantDeliveryStepFeeEntity> merchantDeliveryStepFeeEntityList = new ArrayList<>();
        for (MerchantDeliveryStepFee merchantDeliveryStepFee : stepFeeList) {
            merchantDeliveryStepFeeEntityList.add(toMerchantDeliveryStepFeeEntity(merchantDeliveryStepFee));
        }
        return merchantDeliveryStepFeeEntityList;
    }

    public static MerchantDeliveryStepFeeEntity toMerchantDeliveryStepFeeEntity(MerchantDeliveryStepFee merchantDeliveryStepFee) {
        if (merchantDeliveryStepFee == null) {
            return null;
        }
        MerchantDeliveryStepFeeEntity merchantDeliveryStepFeeEntity = new MerchantDeliveryStepFeeEntity();
        merchantDeliveryStepFeeEntity.setId(merchantDeliveryStepFee.getId());
        merchantDeliveryStepFeeEntity.setTenantId(merchantDeliveryStepFee.getTenantId());
        merchantDeliveryStepFeeEntity.setRuleId(merchantDeliveryStepFee.getRuleId());
        merchantDeliveryStepFeeEntity.setFeeRule(merchantDeliveryStepFee.getFeeRule());
        merchantDeliveryStepFeeEntity.setStepThreshold(merchantDeliveryStepFee.getStepThreshold());
        merchantDeliveryStepFeeEntity.setDeliveryFee(merchantDeliveryStepFee.getDeliveryFee());
        merchantDeliveryStepFeeEntity.setCreateTime(merchantDeliveryStepFee.getCreateTime());
        merchantDeliveryStepFeeEntity.setUpdateTime(merchantDeliveryStepFee.getUpdateTime());
        return merchantDeliveryStepFeeEntity;
    }


    public static DeliveryFeeCalRuleDTO toDeliveryFeeCalRuleDTO(DeliveryFeeCalRule deliveryfeeCalRule){

        if (deliveryfeeCalRule == null) {
            return null;
        }
        DeliveryFeeCalRuleDTO deliveryFeeCalRuleDTO = new DeliveryFeeCalRuleDTO();
        deliveryFeeCalRuleDTO.setStartThreshold(deliveryfeeCalRule.getStartThreshold());
        deliveryFeeCalRuleDTO.setStartDeliveryFee(deliveryfeeCalRule.getStartDeliveryFee());
        deliveryFeeCalRuleDTO.setStepFactor(deliveryfeeCalRule.getStepFactor());
        deliveryFeeCalRuleDTO.setStepFee(deliveryfeeCalRule.getStepFee());
        return deliveryFeeCalRuleDTO;
    }

}
