package com.cosfo.ordercenter.infrastructure.converter.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;
import com.cosfo.ordercenter.infrastructure.model.delivery.OrderDeliveryFeeSnapshot;

/**
 * <AUTHOR>
 */
public class OrderDeliveryFeeSnapshotConverter {

    private OrderDeliveryFeeSnapshotConverter() {
    }

    public static OrderDeliveryFeeSnapshot convertToSnapshot(OrderDeliveryFeeSnapshotEntity snapshotEntity) {

        if (snapshotEntity == null) {
            return null;
        }
        OrderDeliveryFeeSnapshot orderDeliveryFeeSnapshot = new OrderDeliveryFeeSnapshot();
        orderDeliveryFeeSnapshot.setId(snapshotEntity.getId());
        orderDeliveryFeeSnapshot.setTenantId(snapshotEntity.getTenantId());
        orderDeliveryFeeSnapshot.setOrderId(snapshotEntity.getOrderId());
        orderDeliveryFeeSnapshot.setOrderDeliveryFee(snapshotEntity.getOrderDeliveryFee());
        orderDeliveryFeeSnapshot.setOrderInfo(snapshotEntity.getOrderInfo());
        orderDeliveryFeeSnapshot.setRuleInfo(snapshotEntity.getRuleInfo());
        orderDeliveryFeeSnapshot.setHitRuleFee(snapshotEntity.getHitRuleFee());
        orderDeliveryFeeSnapshot.setRemark(snapshotEntity.getRemark());
        orderDeliveryFeeSnapshot.setCreateTime(snapshotEntity.getCreateTime());
        orderDeliveryFeeSnapshot.setUpdateTime(snapshotEntity.getUpdateTime());
        orderDeliveryFeeSnapshot.setScene(snapshotEntity.getScene());
        orderDeliveryFeeSnapshot.setEffectiveFlag(snapshotEntity.getEffectiveFlag());
        return orderDeliveryFeeSnapshot;
    }
}
