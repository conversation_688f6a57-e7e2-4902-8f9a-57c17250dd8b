package com.cosfo.ordercenter.infrastructure.converter.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.TenantDeliveryFeeAreaEntity;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeArea;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TenantDeliveryFeeAreaConverter {
    private TenantDeliveryFeeAreaConverter() {
    }

    public static TenantDeliveryFeeAreaEntity toTenantDeliveryFeeAreaEntity(TenantDeliveryFeeArea tenantDeliveryFeeArea) {
        if (tenantDeliveryFeeArea == null) {
            return null;
        }
        TenantDeliveryFeeAreaEntity tenantDeliveryFeeAreaEntity = new TenantDeliveryFeeAreaEntity();
        tenantDeliveryFeeAreaEntity.setId(tenantDeliveryFeeArea.getId());
        tenantDeliveryFeeAreaEntity.setTenantId(tenantDeliveryFeeArea.getTenantId());
        tenantDeliveryFeeAreaEntity.setRuleId(tenantDeliveryFeeArea.getRuleId());
        tenantDeliveryFeeAreaEntity.setProvince(tenantDeliveryFeeArea.getProvince());
        tenantDeliveryFeeAreaEntity.setCity(tenantDeliveryFeeArea.getCity());
        tenantDeliveryFeeAreaEntity.setArea(tenantDeliveryFeeArea.getArea());
        tenantDeliveryFeeAreaEntity.setDefaultPrice(tenantDeliveryFeeArea.getDefaultPrice());
        tenantDeliveryFeeAreaEntity.setRule(tenantDeliveryFeeArea.getRule());
        tenantDeliveryFeeAreaEntity.setCreateTime(tenantDeliveryFeeArea.getCreateTime());
        tenantDeliveryFeeAreaEntity.setUpdateTime(tenantDeliveryFeeArea.getUpdateTime());
        tenantDeliveryFeeAreaEntity.setGroupId(tenantDeliveryFeeArea.getGroupId());
        return tenantDeliveryFeeAreaEntity;
    }

    public List<TenantDeliveryFeeAreaEntity> convertToEntityList(List<TenantDeliveryFeeArea> list) {

        if (list == null) {
            return Collections.emptyList();
        }
        List<TenantDeliveryFeeAreaEntity> tenantDeliveryFeeAreaEntityList = new ArrayList<>();
        for (TenantDeliveryFeeArea tenantDeliveryFeeArea : list) {
            tenantDeliveryFeeAreaEntityList.add(toTenantDeliveryFeeAreaEntity(tenantDeliveryFeeArea));
        }
        return tenantDeliveryFeeAreaEntityList;
    }
}
