package com.cosfo.ordercenter.infrastructure.converter.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.TenantDeliveryFeeRuleEntity;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TenantDeliveryFeeRuleConverter {
    private TenantDeliveryFeeRuleConverter() {
    }

    public static List<TenantDeliveryFeeRuleEntity> convertToRuleEntity(List<TenantDeliveryFeeRule> tenantDeliveryFeeRules) {

        if (tenantDeliveryFeeRules == null) {
            return Collections.emptyList();
        }
        List<TenantDeliveryFeeRuleEntity> tenantDeliveryFeeRuleEntityList = new ArrayList<>();
        for (TenantDeliveryFeeRule tenantDeliveryFeeRule : tenantDeliveryFeeRules) {
            tenantDeliveryFeeRuleEntityList.add(toTenantDeliveryFeeRuleEntity(tenantDeliveryFeeRule));
        }
        return tenantDeliveryFeeRuleEntityList;
    }

    public static TenantDeliveryFeeRuleEntity toTenantDeliveryFeeRuleEntity(TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        if (tenantDeliveryFeeRule == null) {
            return null;
        }
        TenantDeliveryFeeRuleEntity tenantDeliveryFeeRuleEntity = new TenantDeliveryFeeRuleEntity();
        tenantDeliveryFeeRuleEntity.setId(tenantDeliveryFeeRule.getId());
        tenantDeliveryFeeRuleEntity.setTenantId(tenantDeliveryFeeRule.getTenantId());
        tenantDeliveryFeeRuleEntity.setType(tenantDeliveryFeeRule.getType());
        tenantDeliveryFeeRuleEntity.setDefaultPrice(tenantDeliveryFeeRule.getDefaultPrice());
        tenantDeliveryFeeRuleEntity.setFreeNumber(tenantDeliveryFeeRule.getFreeNumber());
        tenantDeliveryFeeRuleEntity.setFreeType(tenantDeliveryFeeRule.getFreeType());
        tenantDeliveryFeeRuleEntity.setOperator(tenantDeliveryFeeRule.getOperator());
        tenantDeliveryFeeRuleEntity.setCreateTime(tenantDeliveryFeeRule.getCreateTime());
        tenantDeliveryFeeRuleEntity.setUpdateTime(tenantDeliveryFeeRule.getUpdateTime());
        return tenantDeliveryFeeRuleEntity;
    }
}
