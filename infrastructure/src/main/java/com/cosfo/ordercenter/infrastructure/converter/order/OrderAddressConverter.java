package com.cosfo.ordercenter.infrastructure.converter.order;

import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderAddressConverter {

    private OrderAddressConverter() {
    }

    public static OrderAddress convertToOrderAddress(OrderAddressCommandParam commandParam) {

        if (commandParam == null) {
            return null;
        }
        OrderAddress orderAddress = new OrderAddress();
        orderAddress.setId(commandParam.getId());
        orderAddress.setTenantId(commandParam.getTenantId());
        orderAddress.setOrderId(commandParam.getOrderId());
        orderAddress.setContactName(commandParam.getContactName());
        orderAddress.setContactPhone(commandParam.getContactPhone());
        orderAddress.setProvince(commandParam.getProvince());
        orderAddress.setCity(commandParam.getCity());
        orderAddress.setArea(commandParam.getArea());
        orderAddress.setAddress(commandParam.getAddress());
        orderAddress.setCreateTime(commandParam.getCreateTime());
        orderAddress.setUpdateTime(commandParam.getUpdateTime());
        orderAddress.setPoiNote(commandParam.getPoiNote());
        return orderAddress;
    }

    public static List<OrderAddressEntity> convertToOrderAddressEntityList(List<OrderAddress> orderAddressList) {


        if (orderAddressList == null) {
            return Collections.emptyList();
        }
        List<OrderAddressEntity> orderAddressEntityList = new ArrayList<>();
        for (OrderAddress orderAddress : orderAddressList) {
            orderAddressEntityList.add(toOrderAddressEntity(orderAddress));
        }
        return orderAddressEntityList;
    }

    public static OrderAddressEntity toOrderAddressEntity(OrderAddress orderAddress) {
        if (orderAddress == null) {
            return null;
        }
        OrderAddressEntity orderAddressEntity = new OrderAddressEntity();
        orderAddressEntity.setId(orderAddress.getId());
        orderAddressEntity.setTenantId(orderAddress.getTenantId());
        orderAddressEntity.setOrderId(orderAddress.getOrderId());
        orderAddressEntity.setContactName(orderAddress.getContactName());
        orderAddressEntity.setContactPhone(orderAddress.getContactPhone());
        orderAddressEntity.setProvince(orderAddress.getProvince());
        orderAddressEntity.setCity(orderAddress.getCity());
        orderAddressEntity.setArea(orderAddress.getArea());
        orderAddressEntity.setAddress(orderAddress.getAddress());
        orderAddressEntity.setCreateTime(orderAddress.getCreateTime());
        orderAddressEntity.setUpdateTime(orderAddress.getUpdateTime());
        orderAddressEntity.setPoiNote(orderAddress.getPoiNote());
        return orderAddressEntity;
    }
}
