package com.cosfo.ordercenter.infrastructure.converter.order;

import com.cosfo.ordercenter.domain.order.param.command.OrderCombineSnapshotAddParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderCombineSnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderCombineSnapshotConverter {
    private OrderCombineSnapshotConverter() {
    }

    public static List<OrderCombineSnapshot> convertToOrderCombineSnapshotList(List<OrderCombineSnapshotAddParam> snapshotList) {

        if (snapshotList == null) {
            return Collections.emptyList();
        }
        List<OrderCombineSnapshot> orderCombineSnapshotList = new ArrayList<>();
        for (OrderCombineSnapshotAddParam orderCombineSnapshotAddParam : snapshotList) {
            orderCombineSnapshotList.add(toOrderCombineSnapshot(orderCombineSnapshotAddParam));
        }
        return orderCombineSnapshotList;
    }

    public static OrderCombineSnapshot toOrderCombineSnapshot(OrderCombineSnapshotAddParam orderCombineSnapshotAddParam) {
        if (orderCombineSnapshotAddParam == null) {
            return null;
        }
        OrderCombineSnapshot orderCombineSnapshot = new OrderCombineSnapshot();
        orderCombineSnapshot.setId(orderCombineSnapshotAddParam.getId());
        orderCombineSnapshot.setTenantId(orderCombineSnapshotAddParam.getTenantId());
        orderCombineSnapshot.setCombineOrderId(orderCombineSnapshotAddParam.getCombineOrderId());
        orderCombineSnapshot.setCombineItemId(orderCombineSnapshotAddParam.getCombineItemId());
        orderCombineSnapshot.setItemId(orderCombineSnapshotAddParam.getItemId());
        orderCombineSnapshot.setQuantity(orderCombineSnapshotAddParam.getQuantity());
        orderCombineSnapshot.setOriginalPrice(orderCombineSnapshotAddParam.getOriginalPrice());
        orderCombineSnapshot.setOrderItemId(orderCombineSnapshotAddParam.getOrderItemId());
        orderCombineSnapshot.setCreateTime(orderCombineSnapshotAddParam.getCreateTime());
        orderCombineSnapshot.setUpdateTime(orderCombineSnapshotAddParam.getUpdateTime());
        return orderCombineSnapshot;
    }
}
