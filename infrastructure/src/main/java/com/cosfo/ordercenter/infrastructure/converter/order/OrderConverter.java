package com.cosfo.ordercenter.infrastructure.converter.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderCommandParam;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.github.pagehelper.PageInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderConverter {

    private OrderConverter() {
    }

    public static PageInfo<OrderEntity> convertToPageInfo(Page<Order> pageInfo) {
        if (pageInfo == null) {
            return null;
        }
        PageInfo<OrderEntity> result = new PageInfo<>();
        result.setList(convertToOrderEntityList(pageInfo.getRecords()));
        result.setTotal(pageInfo.getTotal());
        result.setPages((int) pageInfo.getPages());
        result.setPageNum((int) pageInfo.getCurrent());
        result.setPageSize((int) pageInfo.getSize());
        result.setSize((int) pageInfo.getSize());
        return result;
    }

    public static OrderEntity convertToOrderEntity(Order order) {

        if (order == null) {
            return null;
        }
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setId(order.getId());
        orderEntity.setTenantId(order.getTenantId());
        orderEntity.setStoreId(order.getStoreId());
        orderEntity.setAccountId(order.getAccountId());
        orderEntity.setSupplierTenantId(order.getSupplierTenantId());
        orderEntity.setOrderNo(order.getOrderNo());
        orderEntity.setWarehouseType(order.getWarehouseType());
        orderEntity.setPayablePrice(order.getPayablePrice());
        orderEntity.setDeliveryFee(order.getDeliveryFee());
        orderEntity.setTotalPrice(order.getTotalPrice());
        orderEntity.setStatus(order.getStatus());
        orderEntity.setPayType(order.getPayType());
        orderEntity.setOnlinePayChannel(order.getOnlinePayChannel());
        orderEntity.setPayTime(order.getPayTime());
        orderEntity.setDeliveryTime(order.getDeliveryTime());
        orderEntity.setFinishedTime(order.getFinishedTime());
        orderEntity.setCreateTime(order.getCreateTime());
        orderEntity.setUpdateTime(order.getUpdateTime());
        orderEntity.setRemark(order.getRemark());
        orderEntity.setApplyEndTime(order.getApplyEndTime());
        orderEntity.setAutoFinishedTime(order.getAutoFinishedTime());
        orderEntity.setWarehouseNo(order.getWarehouseNo());
        orderEntity.setCombineOrderId(order.getCombineOrderId());
        orderEntity.setOrderType(order.getOrderType());
        orderEntity.setBeginDeliveryTime(order.getBeginDeliveryTime());
        orderEntity.setProfitSharingFinishTime(order.getProfitSharingFinishTime());
        orderEntity.setOrderVersion(order.getOrderVersion());
        orderEntity.setOrderSource(order.getOrderSource());
        orderEntity.setCustomerOrderId(order.getCustomerOrderId());
        orderEntity.setFulfillmentNo(order.getFulfillmentNo());
        orderEntity.setPlanOrderNo(order.getPlanOrderNo());
        orderEntity.setFulfillmentType(order.getFulfillmentType());
        return orderEntity;
    }

    public static List<OrderEntity> convertToOrderEntityList(List<Order> orderList) {

        if (orderList == null) {
            return Collections.emptyList();
        }
        List<OrderEntity> orderEntityList = new ArrayList<>();
        for (Order order : orderList) {
            orderEntityList.add(convertToOrderEntity(order));
        }
        return orderEntityList;
    }

    public static List<Order> convertToOrderList(List<OrderCommandParam> orderCommandParams) {

        if (orderCommandParams == null) {
            return Collections.emptyList();
        }
        List<Order> orderList = new ArrayList<>();
        for (OrderCommandParam orderCommandParam : orderCommandParams) {
            orderList.add(toOrder(orderCommandParam));
        }
        return orderList;
    }

    public static Order toOrder(OrderCommandParam orderCommandParam) {
        if (orderCommandParam == null) {
            return null;
        }
        Order order = new Order();
        order.setId(orderCommandParam.getId());
        order.setTenantId(orderCommandParam.getTenantId());
        order.setStoreId(orderCommandParam.getStoreId());
        order.setAccountId(orderCommandParam.getAccountId());
        order.setSupplierTenantId(orderCommandParam.getSupplierTenantId());
        order.setOrderNo(orderCommandParam.getOrderNo());
        order.setWarehouseType(orderCommandParam.getWarehouseType());
        order.setPayablePrice(orderCommandParam.getPayablePrice());
        order.setDeliveryFee(orderCommandParam.getDeliveryFee());
        order.setTotalPrice(orderCommandParam.getTotalPrice());
        order.setStatus(orderCommandParam.getStatus());
        order.setPayType(orderCommandParam.getPayType());
        order.setOnlinePayChannel(orderCommandParam.getOnlinePayChannel());
        order.setPayTime(orderCommandParam.getPayTime());
        order.setDeliveryTime(orderCommandParam.getDeliveryTime());
        order.setFinishedTime(orderCommandParam.getFinishedTime());
        order.setCreateTime(orderCommandParam.getCreateTime());
        order.setUpdateTime(orderCommandParam.getUpdateTime());
        order.setRemark(orderCommandParam.getRemark());
        order.setApplyEndTime(orderCommandParam.getApplyEndTime());
        order.setAutoFinishedTime(orderCommandParam.getAutoFinishedTime());
        order.setWarehouseNo(orderCommandParam.getWarehouseNo());
        order.setCombineOrderId(orderCommandParam.getCombineOrderId());
        order.setOrderType(orderCommandParam.getOrderType());
        order.setBeginDeliveryTime(orderCommandParam.getBeginDeliveryTime());
        order.setProfitSharingFinishTime(orderCommandParam.getProfitSharingFinishTime());
        order.setOrderVersion(orderCommandParam.getOrderVersion());
        order.setOrderSource(orderCommandParam.getOrderSource());
        order.setCustomerOrderId(orderCommandParam.getCustomerOrderId());
        order.setFulfillmentNo(orderCommandParam.getFulfillmentNo());
        order.setPlanOrderNo(orderCommandParam.getPlanOrderNo());
        return order;
    }

    public static Order convertToOrder(OrderEntity orderEntity) {

        if (orderEntity == null) {
            return null;
        }
        Order order = new Order();
        order.setId(orderEntity.getId());
        order.setTenantId(orderEntity.getTenantId());
        order.setStoreId(orderEntity.getStoreId());
        order.setAccountId(orderEntity.getAccountId());
        order.setSupplierTenantId(orderEntity.getSupplierTenantId());
        order.setOrderNo(orderEntity.getOrderNo());
        order.setWarehouseType(orderEntity.getWarehouseType());
        order.setPayablePrice(orderEntity.getPayablePrice());
        order.setDeliveryFee(orderEntity.getDeliveryFee());
        order.setTotalPrice(orderEntity.getTotalPrice());
        order.setStatus(orderEntity.getStatus());
        order.setPayType(orderEntity.getPayType());
        order.setOnlinePayChannel(orderEntity.getOnlinePayChannel());
        order.setPayTime(orderEntity.getPayTime());
        order.setDeliveryTime(orderEntity.getDeliveryTime());
        order.setFinishedTime(orderEntity.getFinishedTime());
        order.setCreateTime(orderEntity.getCreateTime());
        order.setUpdateTime(orderEntity.getUpdateTime());
        order.setRemark(orderEntity.getRemark());
        order.setApplyEndTime(orderEntity.getApplyEndTime());
        order.setAutoFinishedTime(orderEntity.getAutoFinishedTime());
        order.setWarehouseNo(orderEntity.getWarehouseNo());
        order.setCombineOrderId(orderEntity.getCombineOrderId());
        order.setOrderType(orderEntity.getOrderType());
        order.setBeginDeliveryTime(orderEntity.getBeginDeliveryTime());
        order.setProfitSharingFinishTime(orderEntity.getProfitSharingFinishTime());
        order.setOrderVersion(orderEntity.getOrderVersion());
        order.setOrderSource(orderEntity.getOrderSource());
        order.setCustomerOrderId(orderEntity.getCustomerOrderId());
        order.setFulfillmentNo(orderEntity.getFulfillmentNo());
        order.setPlanOrderNo(orderEntity.getPlanOrderNo());
        order.setFulfillmentType(orderEntity.getFulfillmentType());
        return order;
    }
}
