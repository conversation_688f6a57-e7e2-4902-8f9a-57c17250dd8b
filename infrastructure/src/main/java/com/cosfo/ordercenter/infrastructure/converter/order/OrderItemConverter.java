package com.cosfo.ordercenter.infrastructure.converter.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemCommandParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemWithSnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderItemConverter {
    private OrderItemConverter() {
    }

    public static List<OrderItemEntity> convertToEntityList(List<OrderItem> orderItemList) {

        if (orderItemList == null) {
            return Collections.emptyList();
        }
        List<OrderItemEntity> orderItemEntityList = new ArrayList<>();
        for (OrderItem orderItem : orderItemList) {
            orderItemEntityList.add(toOrderItemEntity(orderItem));
        }
        return orderItemEntityList;
    }

    public static OrderItemEntity toOrderItemEntity(OrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }
        OrderItemEntity orderItemEntity = new OrderItemEntity();
        orderItemEntity.setId(orderItem.getId());
        orderItemEntity.setTenantId(orderItem.getTenantId());
        orderItemEntity.setOrderId(orderItem.getOrderId());
        orderItemEntity.setItemId(orderItem.getItemId());
        orderItemEntity.setAmount(orderItem.getAmount());
        orderItemEntity.setPayablePrice(orderItem.getPayablePrice());
        orderItemEntity.setTotalPrice(orderItem.getTotalPrice());
        orderItemEntity.setStoreNo(orderItem.getStoreNo());
        orderItemEntity.setStatus(orderItem.getStatus());
        orderItemEntity.setCreateTime(orderItem.getCreateTime());
        orderItemEntity.setUpdateTime(orderItem.getUpdateTime());
        orderItemEntity.setAfterSaleExpiryTime(orderItem.getAfterSaleExpiryTime());
        orderItemEntity.setOrderType(orderItem.getOrderType());
        orderItemEntity.setDeliveryQuantity(orderItem.getDeliveryQuantity());
        return orderItemEntity;
    }


    public static List<OrderItemWithSnapshotEntity> convertToOrderItemWithSnapshotEntityList(List<OrderItemWithSnapshot> orderItemWithSnapshots) {

        if (orderItemWithSnapshots == null) {
            return Collections.emptyList();
        }
        List<OrderItemWithSnapshotEntity> orderItemWithSnapshotEntityList = new ArrayList<>();
        for (OrderItemWithSnapshot orderItemWithSnapshot : orderItemWithSnapshots) {
            orderItemWithSnapshotEntityList.add(toOrderItemWithSnapshotEntity(orderItemWithSnapshot));
        }
        return orderItemWithSnapshotEntityList;
    }

    public static OrderItemWithSnapshotEntity toOrderItemWithSnapshotEntity(OrderItemWithSnapshot orderItemWithSnapshot) {
        if (orderItemWithSnapshot == null) {
            return null;
        }
        OrderItemWithSnapshotEntity orderItemWithSnapshotEntity = new OrderItemWithSnapshotEntity();
        orderItemWithSnapshotEntity.setOrderItemId(orderItemWithSnapshot.getOrderItemId());
        orderItemWithSnapshotEntity.setTenantId(orderItemWithSnapshot.getTenantId());
        orderItemWithSnapshotEntity.setOrderId(orderItemWithSnapshot.getOrderId());
        orderItemWithSnapshotEntity.setItemId(orderItemWithSnapshot.getItemId());
        orderItemWithSnapshotEntity.setAmount(orderItemWithSnapshot.getAmount());
        orderItemWithSnapshotEntity.setPayablePrice(orderItemWithSnapshot.getPayablePrice());
        orderItemWithSnapshotEntity.setTotalPrice(orderItemWithSnapshot.getTotalPrice());
        orderItemWithSnapshotEntity.setStoreNo(orderItemWithSnapshot.getStoreNo());
        orderItemWithSnapshotEntity.setStatus(orderItemWithSnapshot.getStatus());
        orderItemWithSnapshotEntity.setCreateTime(orderItemWithSnapshot.getCreateTime());
        orderItemWithSnapshotEntity.setUpdateTime(orderItemWithSnapshot.getUpdateTime());
        orderItemWithSnapshotEntity.setAfterSaleExpiryTime(orderItemWithSnapshot.getAfterSaleExpiryTime());
        orderItemWithSnapshotEntity.setOrderType(orderItemWithSnapshot.getOrderType());
        orderItemWithSnapshotEntity.setDeliveryQuantity(orderItemWithSnapshot.getDeliveryQuantity());
        orderItemWithSnapshotEntity.setSupplierTenantId(orderItemWithSnapshot.getSupplierTenantId());
        orderItemWithSnapshotEntity.setTitle(orderItemWithSnapshot.getTitle());
        orderItemWithSnapshotEntity.setMainPicture(orderItemWithSnapshot.getMainPicture());
        orderItemWithSnapshotEntity.setSpecification(orderItemWithSnapshot.getSpecification());
        orderItemWithSnapshotEntity.setWarehouseType(orderItemWithSnapshot.getWarehouseType());
        orderItemWithSnapshotEntity.setDeliveryType(orderItemWithSnapshot.getDeliveryType());
        orderItemWithSnapshotEntity.setGoodsType(orderItemWithSnapshot.getGoodsType());
        orderItemWithSnapshotEntity.setSpecificationUnit(orderItemWithSnapshot.getSpecificationUnit());
        orderItemWithSnapshotEntity.setAfterSaleUnit(orderItemWithSnapshot.getAfterSaleUnit());
        orderItemWithSnapshotEntity.setSupplierName(orderItemWithSnapshot.getSupplierName());
        orderItemWithSnapshotEntity.setSupplyPrice(orderItemWithSnapshot.getSupplyPrice());
        orderItemWithSnapshotEntity.setSkuId(orderItemWithSnapshot.getSkuId());
        orderItemWithSnapshotEntity.setSupplierSkuId(orderItemWithSnapshot.getSupplierSkuId());
        return orderItemWithSnapshotEntity;
    }

    public static List<OrderItem> convertToOrderItemList(List<OrderItemCommandParam> commandParams) {

        if (commandParams == null) {
            return Collections.emptyList();
        }
        List<OrderItem> orderItemList = new ArrayList<>();
        for (OrderItemCommandParam orderItemCommandParam : commandParams) {
            orderItemList.add(toOrderItem(orderItemCommandParam));
        }
        return orderItemList;
    }

    public static OrderItem toOrderItem(OrderItemCommandParam orderItemCommandParam) {
        if (orderItemCommandParam == null) {
            return null;
        }
        OrderItem orderItem = new OrderItem();
        orderItem.setId(orderItemCommandParam.getId());
        orderItem.setTenantId(orderItemCommandParam.getTenantId());
        orderItem.setOrderId(orderItemCommandParam.getOrderId());
        orderItem.setItemId(orderItemCommandParam.getItemId());
        orderItem.setAmount(orderItemCommandParam.getAmount());
        orderItem.setPayablePrice(orderItemCommandParam.getPayablePrice());
        orderItem.setTotalPrice(orderItemCommandParam.getTotalPrice());
        orderItem.setStoreNo(orderItemCommandParam.getStoreNo());
        orderItem.setStatus(orderItemCommandParam.getStatus());
        orderItem.setCreateTime(orderItemCommandParam.getCreateTime());
        orderItem.setUpdateTime(orderItemCommandParam.getUpdateTime());
        orderItem.setAfterSaleExpiryTime(orderItemCommandParam.getAfterSaleExpiryTime());
        orderItem.setOrderType(orderItemCommandParam.getOrderType());
        orderItem.setDeliveryQuantity(orderItemCommandParam.getDeliveryQuantity());
        return orderItem;
    }

    public static List<OrderItemWithSnapshotEntity> convertToOrderItemWithSnapshotEntityList(List<OrderItem> orderItems, Map<Long, OrderItemSnapshot> snapshotMap) {


        if (orderItems == null) {
            return Collections.emptyList();
        }
        List<OrderItemWithSnapshotEntity> orderItemWithSnapshotEntityList = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItem.getId());
            orderItemWithSnapshotEntityList.add(toOrderItemWithSnapshotEntity(orderItem, orderItemSnapshot));
        }
        return orderItemWithSnapshotEntityList;
    }

    public static OrderItemWithSnapshotEntity toOrderItemWithSnapshotEntity(OrderItem orderItem,  OrderItemSnapshot orderItemSnapshot) {
        if (orderItem == null) {
            return null;
        }
        OrderItemWithSnapshotEntity orderItemWithSnapshotEntity = new OrderItemWithSnapshotEntity();
        orderItemWithSnapshotEntity.setTenantId(orderItem.getTenantId());
        orderItemWithSnapshotEntity.setOrderId(orderItem.getOrderId());
        orderItemWithSnapshotEntity.setItemId(orderItem.getItemId());
        orderItemWithSnapshotEntity.setAmount(orderItem.getAmount());
        orderItemWithSnapshotEntity.setPayablePrice(orderItem.getPayablePrice());
        orderItemWithSnapshotEntity.setTotalPrice(orderItem.getTotalPrice());
        orderItemWithSnapshotEntity.setStoreNo(orderItem.getStoreNo());
        orderItemWithSnapshotEntity.setStatus(orderItem.getStatus());
        orderItemWithSnapshotEntity.setCreateTime(orderItem.getCreateTime());
        orderItemWithSnapshotEntity.setUpdateTime(orderItem.getUpdateTime());
        orderItemWithSnapshotEntity.setAfterSaleExpiryTime(orderItem.getAfterSaleExpiryTime());
        orderItemWithSnapshotEntity.setOrderType(orderItem.getOrderType());
        orderItemWithSnapshotEntity.setDeliveryQuantity(orderItem.getDeliveryQuantity());
        orderItemWithSnapshotEntity.setOrderItemId(orderItem.getId());
// Not mapped TO fields:
// orderItemId
// supplierTenantId
// title
// mainPicture
// specification
// warehouseType
// deliveryType
// goodsType
// specificationUnit
// afterSaleUnit
// supplierName
// supplyPrice
// skuId
// supplySkuId
// Not mapped FROM fields:
// id
        if (orderItemSnapshot != null) {
            orderItemWithSnapshotEntity.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
            orderItemWithSnapshotEntity.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
            orderItemWithSnapshotEntity.setSupplierName(orderItemSnapshot.getSupplierName());
            orderItemWithSnapshotEntity.setTitle(orderItemSnapshot.getTitle());
            orderItemWithSnapshotEntity.setMainPicture(orderItemSnapshot.getMainPicture());
            orderItemWithSnapshotEntity.setSpecification(orderItemSnapshot.getSpecification());
            orderItemWithSnapshotEntity.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
            orderItemWithSnapshotEntity.setWarehouseType(orderItemSnapshot.getWarehouseType());
            orderItemWithSnapshotEntity.setGoodsType(orderItemSnapshot.getGoodsType());
            orderItemWithSnapshotEntity.setDeliveryType(orderItemSnapshot.getDeliveryType());
            orderItemWithSnapshotEntity.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
            orderItemWithSnapshotEntity.setAfterSaleRule(orderItemSnapshot.getAfterSaleRule());
            orderItemWithSnapshotEntity.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
            orderItemWithSnapshotEntity.setSkuId(orderItemSnapshot.getSkuId());
            orderItemWithSnapshotEntity.setMaxAfterSaleAmount(orderItemSnapshot.getMaxAfterSaleAmount());
        }
        return orderItemWithSnapshotEntity;
    }

}
