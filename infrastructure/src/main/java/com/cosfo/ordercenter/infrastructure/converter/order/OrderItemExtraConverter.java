package com.cosfo.ordercenter.infrastructure.converter.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemExtraEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemExtra;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderItemExtraConverter {
    private OrderItemExtraConverter() {
    }

    public static List<OrderItemExtraEntity> convertToEntityList(List<OrderItemExtra> orderItemExtraList) {

        if (orderItemExtraList == null) {
            return Collections.emptyList();
        }
        List<OrderItemExtraEntity> orderItemExtraEntityList = new ArrayList<>();
        for (OrderItemExtra orderItemExtra : orderItemExtraList) {
            orderItemExtraEntityList.add(toOrderItemExtraEntity(orderItemExtra));
        }
        return orderItemExtraEntityList;
    }

    public static OrderItemExtraEntity toOrderItemExtraEntity(OrderItemExtra orderItemExtra) {
        if (orderItemExtra == null) {
            return null;
        }
        OrderItemExtraEntity orderItemExtraEntity = new OrderItemExtraEntity();
        orderItemExtraEntity.setId(orderItemExtra.getId());
        orderItemExtraEntity.setTenantId(orderItemExtra.getTenantId());
        orderItemExtraEntity.setOrderId(orderItemExtra.getOrderId());
        orderItemExtraEntity.setOrderItemId(orderItemExtra.getOrderItemId());
        orderItemExtraEntity.setCustomerOrderItemId(orderItemExtra.getCustomerOrderItemId());
        orderItemExtraEntity.setCustomerSkuCode(orderItemExtra.getCustomerSkuCode());
        orderItemExtraEntity.setSkuCode(orderItemExtra.getSkuCode());
        orderItemExtraEntity.setCustomerSkuTitle(orderItemExtra.getCustomerSkuTitle());
        orderItemExtraEntity.setCustomerSkuSpecification(orderItemExtra.getCustomerSkuSpecification());
        orderItemExtraEntity.setCustomerSkuSpecificationUnit(orderItemExtra.getCustomerSkuSpecificationUnit());
        return orderItemExtraEntity;
    }

    public static List<OrderItemExtra> converterToItemExtraList(List<OrderItemExtraAddParam> extraAddParams) {

        if (extraAddParams == null) {
            return Collections.emptyList();
        }
        List<OrderItemExtra> orderItemExtraList = new ArrayList<>();
        for (OrderItemExtraAddParam orderItemExtraAddParam : extraAddParams) {
            orderItemExtraList.add(toOrderItemExtra(orderItemExtraAddParam));
        }
        return orderItemExtraList;
    }

    public static OrderItemExtra toOrderItemExtra(OrderItemExtraAddParam orderItemExtraAddParam) {
        if (orderItemExtraAddParam == null) {
            return null;
        }
        OrderItemExtra orderItemExtra = new OrderItemExtra();
        orderItemExtra.setId(orderItemExtraAddParam.getId());
        orderItemExtra.setTenantId(orderItemExtraAddParam.getTenantId());
        orderItemExtra.setOrderId(orderItemExtraAddParam.getOrderId());
        orderItemExtra.setOrderItemId(orderItemExtraAddParam.getOrderItemId());
        orderItemExtra.setCustomerOrderItemId(orderItemExtraAddParam.getCustomerOrderItemId());
        orderItemExtra.setCustomerSkuCode(orderItemExtraAddParam.getCustomerSkuCode());
        orderItemExtra.setSkuCode(orderItemExtraAddParam.getSkuCode());
        orderItemExtra.setCustomerSkuTitle(orderItemExtraAddParam.getCustomerSkuTitle());
        orderItemExtra.setCustomerSkuSpecification(orderItemExtraAddParam.getCustomerSkuSpecification());
        orderItemExtra.setCustomerSkuSpecificationUnit(orderItemExtraAddParam.getCustomerSkuSpecificationUnit());
        return orderItemExtra;
    }
}
