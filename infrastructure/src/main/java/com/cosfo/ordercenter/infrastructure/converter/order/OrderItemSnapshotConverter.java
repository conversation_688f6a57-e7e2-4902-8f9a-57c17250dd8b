package com.cosfo.ordercenter.infrastructure.converter.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderItemSnapshotConverter {
    private OrderItemSnapshotConverter() {
    }

    public static List<OrderItemSnapshotEntity> convertToSnapshotEntityList(List<OrderItemSnapshot> orderItemSnapshots) {

        if (orderItemSnapshots == null) {
            return Collections.emptyList();
        }
        List<OrderItemSnapshotEntity> orderItemSnapshotEntityList = new ArrayList<>();
        for (OrderItemSnapshot orderItemSnapshot : orderItemSnapshots) {
            orderItemSnapshotEntityList.add(toOrderItemSnapshotEntity(orderItemSnapshot));
        }
        return orderItemSnapshotEntityList;
    }

    public static OrderItemSnapshotEntity toOrderItemSnapshotEntity(OrderItemSnapshot orderItemSnapshot) {
        if (orderItemSnapshot == null) {
            return null;
        }
        OrderItemSnapshotEntity orderItemSnapshotEntity = new OrderItemSnapshotEntity();
        orderItemSnapshotEntity.setId(orderItemSnapshot.getId());
        orderItemSnapshotEntity.setTenantId(orderItemSnapshot.getTenantId());
        orderItemSnapshotEntity.setOrderItemId(orderItemSnapshot.getOrderItemId());
        orderItemSnapshotEntity.setSkuId(orderItemSnapshot.getSkuId());
        orderItemSnapshotEntity.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
        orderItemSnapshotEntity.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
        orderItemSnapshotEntity.setAreaItemId(orderItemSnapshot.getAreaItemId());
        orderItemSnapshotEntity.setTitle(orderItemSnapshot.getTitle());
        orderItemSnapshotEntity.setMainPicture(orderItemSnapshot.getMainPicture());
        orderItemSnapshotEntity.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
        orderItemSnapshotEntity.setSpecification(orderItemSnapshot.getSpecification());
        orderItemSnapshotEntity.setCreateTime(orderItemSnapshot.getCreateTime());
        orderItemSnapshotEntity.setUpdateTime(orderItemSnapshot.getUpdateTime());
        orderItemSnapshotEntity.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
        orderItemSnapshotEntity.setWarehouseType(orderItemSnapshot.getWarehouseType());
        orderItemSnapshotEntity.setDeliveryType(orderItemSnapshot.getDeliveryType());
        orderItemSnapshotEntity.setSupplierName(orderItemSnapshot.getSupplierName());
        orderItemSnapshotEntity.setMaxAfterSaleAmount(orderItemSnapshot.getMaxAfterSaleAmount());
        orderItemSnapshotEntity.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
        orderItemSnapshotEntity.setPricingType(orderItemSnapshot.getPricingType());
        orderItemSnapshotEntity.setPricingNumber(orderItemSnapshot.getPricingNumber());
        orderItemSnapshotEntity.setAfterSaleRule(orderItemSnapshot.getAfterSaleRule());
        orderItemSnapshotEntity.setGoodsType(orderItemSnapshot.getGoodsType());
        orderItemSnapshotEntity.setOrderId(orderItemSnapshot.getOrderId());
        orderItemSnapshotEntity.setItemCode(orderItemSnapshot.getItemCode());
        orderItemSnapshotEntity.setBuyMultiple(orderItemSnapshot.getBuyMultiple());
        orderItemSnapshotEntity.setBuyMultipleSwitch(orderItemSnapshot.getBuyMultipleSwitch());
        orderItemSnapshotEntity.setSkuCode(orderItemSnapshot.getSkuCode());
        orderItemSnapshotEntity.setCustomSkuCode(orderItemSnapshot.getCustomSkuCode());
        orderItemSnapshotEntity.setPresaleSwitch(orderItemSnapshot.getPresaleSwitch());
        orderItemSnapshotEntity.setWeight(orderItemSnapshot.getWeight());
        return orderItemSnapshotEntity;
    }

    public static List<OrderItemSnapshot> convertToSnapshotList(List<OrderItemSnapshotAddParam> snapshotAddParams) {


        if (snapshotAddParams == null) {
            return Collections.emptyList();
        }
        List<OrderItemSnapshot> orderItemSnapshotList = new ArrayList<>();
        for (OrderItemSnapshotAddParam orderItemSnapshotAddParam : snapshotAddParams) {
            orderItemSnapshotList.add(toOrderItemSnapshot(orderItemSnapshotAddParam));
        }
        return orderItemSnapshotList;
    }

    public static OrderItemSnapshot toOrderItemSnapshot(OrderItemSnapshotAddParam orderItemSnapshotAddParam) {
        if (orderItemSnapshotAddParam == null) {
            return null;
        }
        OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setId(orderItemSnapshotAddParam.getId());
        orderItemSnapshot.setTenantId(orderItemSnapshotAddParam.getTenantId());
        orderItemSnapshot.setOrderItemId(orderItemSnapshotAddParam.getOrderItemId());
        orderItemSnapshot.setSkuId(orderItemSnapshotAddParam.getSkuId());
        orderItemSnapshot.setSupplierTenantId(orderItemSnapshotAddParam.getSupplierTenantId());
        orderItemSnapshot.setSupplierSkuId(orderItemSnapshotAddParam.getSupplierSkuId());
        orderItemSnapshot.setAreaItemId(orderItemSnapshotAddParam.getAreaItemId());
        orderItemSnapshot.setTitle(orderItemSnapshotAddParam.getTitle());
        orderItemSnapshot.setMainPicture(orderItemSnapshotAddParam.getMainPicture());
        orderItemSnapshot.setSpecificationUnit(orderItemSnapshotAddParam.getSpecificationUnit());
        orderItemSnapshot.setSpecification(orderItemSnapshotAddParam.getSpecification());
        orderItemSnapshot.setCreateTime(orderItemSnapshotAddParam.getCreateTime());
        orderItemSnapshot.setUpdateTime(orderItemSnapshotAddParam.getUpdateTime());
        orderItemSnapshot.setSupplyPrice(orderItemSnapshotAddParam.getSupplyPrice());
        orderItemSnapshot.setWarehouseType(orderItemSnapshotAddParam.getWarehouseType());
        orderItemSnapshot.setDeliveryType(orderItemSnapshotAddParam.getDeliveryType());
        orderItemSnapshot.setSupplierName(orderItemSnapshotAddParam.getSupplierName());
        orderItemSnapshot.setMaxAfterSaleAmount(orderItemSnapshotAddParam.getMaxAfterSaleAmount());
        orderItemSnapshot.setAfterSaleUnit(orderItemSnapshotAddParam.getAfterSaleUnit());
        orderItemSnapshot.setPricingType(orderItemSnapshotAddParam.getPricingType());
        orderItemSnapshot.setPricingNumber(orderItemSnapshotAddParam.getPricingNumber());
        orderItemSnapshot.setAfterSaleRule(orderItemSnapshotAddParam.getAfterSaleRule());
        orderItemSnapshot.setGoodsType(orderItemSnapshotAddParam.getGoodsType());
        orderItemSnapshot.setOrderId(orderItemSnapshotAddParam.getOrderId());
        orderItemSnapshot.setItemCode(orderItemSnapshotAddParam.getItemCode());
        orderItemSnapshot.setBuyMultiple(orderItemSnapshotAddParam.getBuyMultiple());
        orderItemSnapshot.setBuyMultipleSwitch(orderItemSnapshotAddParam.getBuyMultipleSwitch());
        orderItemSnapshot.setSkuCode(orderItemSnapshotAddParam.getSkuCode());
        orderItemSnapshot.setCustomSkuCode(orderItemSnapshotAddParam.getCustomSkuCode());
        orderItemSnapshot.setPresaleSwitch(orderItemSnapshotAddParam.getPresaleSwitch());
        orderItemSnapshot.setWeight(orderItemSnapshotAddParam.getWeight());
        return orderItemSnapshot;
    }
}
