package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryWarehouseParam;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryRuleWarehouseRelation;

import java.util.Set;

/**
 * 运费规则仓库信息关联表(MerchantDeliveryRuleWarehouseRelation)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:36
 */
public interface MerchantDeliveryRuleWarehouseRelationDao extends IService<MerchantDeliveryRuleWarehouseRelation> {

    Set<Long> listRuleIds(QueryDeliveryWarehouseParam param);
}
