package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryStepFeeParam;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryStepFee;

import java.util.List;

/**
 * 阶梯运费(MerchantDeliveryStepFee)表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-14 13:41:43
 */
public interface MerchantDeliveryStepFeeDao extends IService<MerchantDeliveryStepFee> {

    /**
     * 顺序条件查询阶梯运费
     *
     * @param param
     * @return
     */
    List<MerchantDeliveryStepFee> listByParam(QueryDeliveryStepFeeParam param);

    /**
     * 倒序查询阶梯运费
     *
     * @param param
     * @return
     */
    List<MerchantDeliveryStepFee> listStepDesc(QueryDeliveryStepFeeParam param);
}
