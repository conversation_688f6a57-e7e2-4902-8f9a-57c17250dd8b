package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
public interface OrderAddressDao extends IService<OrderAddress> {

    /**
     * 查询订单地址
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderAddress> queryByOrderIds(List<Long> orderIds,Long tenantId);

    /**
     * 查询订单地址（单个）
     * @param orderId
     * @param tenantId
     * @return
     */
    OrderAddress getByOrderId(Long orderId,Long tenantId);


    /**
     * 保存地址
     */
    Long add(OrderAddress orderAddress);
}
