package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.*;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAddCommand;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleStatusUpdateCommandParam;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
public interface OrderAfterSaleDao extends IService<OrderAfterSale> {

    /**
     * 根据条件查询售后单
     *
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSale> queryListByCondition(OrderAfterSaleQueryParam orderAfterSaleQueryParam);

    /**
     * 批量插入售后单
     * @param orderAfterSales
     * @return
     */
    List<Long> batchAdd(List<OrderAfterSaleEntity> orderAfterSales);

    /**
     * 售后单查询
     * @param req
     * @return
     */
    List<OrderAfterSale> queryList(OrderAfterSaleListQueryParam req);

    /**
     * 售后单分页查询
     * @param req
     * @return
     */
    Page<OrderAfterSale> queryPage(OrderAfterSalePageQueryParam req);

    /**
     * 根据售后no查询售后单
     * @param afterSaleNo
     * @return
     */
    OrderAfterSale queryByAfterSaleNo(String afterSaleNo);

    /**
     * 统计售后单数量
     * @param orderAfterSaleCountReq
     * @return
     */
    Integer countOrderAfterSale(OrderAfterSaleCountParam orderAfterSaleCountReq);


    /**
     * 根据订单查询售后单
     * @param orderId
     * @param tenantId
     * @return
     */
    List<OrderAfterSale> queryByOrderId(Long orderId, Long tenantId);

    /**
     * 查询售后记录最近使用的退回地址id
     * @param tenantId
     * @return
     */
    Long getRecentlyUsedReturnAddressId(Long tenantId);

    List<OrderAfterSaleEntity> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleParam req);

    /**
     * 更新售后单状态
     * @param orderAfterSaleStatusUpdateReq
     * @return
     */
    boolean updateStatus(OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateReq);

    /**
     * 根据售后单no查询
     * @param orderAfterSaleNos
     * @return
     */
    List<OrderAfterSale> queryByNos(List<String> orderAfterSaleNos);


    /**
     * 根据售后单id查询
     */
    List<OrderAfterSale> queryByIds(List<Long> orderAfterSaleIds);

    /**
     * 根据订单统计售后单数量
     * @param req
     * @return
     */
    Map<Long, Integer> countOrderAfterSaleByOrderId(OrderAfterSaleCountParam req);

    /**
     * 售后单自动完结
     * @return
     */
    boolean autoFinish();


    /**
     * 更新城配仓号
     * @param afterSaleId
     * @param sourceStoreNo
     * @param storeNo
     * @return
     */
    boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo);

    /**
     * 根据入参批量查询
     * @param orderAfterSaleQueryParam
     * @return
     */
    List<OrderAfterSale> queryListByParam(OrderAfterSaleQueryParam orderAfterSaleQueryParam);
}
