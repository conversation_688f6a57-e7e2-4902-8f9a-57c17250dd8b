package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleRule;

import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/8/11 上午10:43
 */
public interface OrderAfterSaleRuleDao extends IService<OrderAfterSaleRule> {

    /**
     * 初始化售后规则时，插入记录，如果存在，则更新
     * @param orderAfterSaleRule
     * @return
     */
    Long addOrUpdate(OrderAfterSaleRuleCommandParam orderAfterSaleRule);

    /**
     * 查询租户的售后规则
     * @param tenantId
     * @return
     */
    List<OrderAfterSaleRuleEntity> queryByTenantId(Long tenantId);

    Integer updateRule(OrderAfterSaleRuleCommandParam orderAfterSaleRule);

    Integer deleteRule(Long id);
}
