package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.order.param.command.OrderCombineSnapshotAddParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderCombineSnapshot;

import java.util.List;

/**
 * <p>
 * 组合订单快照 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
public interface OrderCombineSnapshotDao extends IService<OrderCombineSnapshot> {
    /**
     * 批量保存
     * @param snapshotList
     * @return
     */
    boolean batchSave(List<OrderCombineSnapshotAddParam> snapshotList);
}
