package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.order.entity.OrderDetailEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSaleQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderSkuQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.SupplierOrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.param.query.*;
import com.cosfo.ordercenter.infrastructure.model.order.Order;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface OrderDao extends IService<Order> {

    /**
     * 计算订单金额
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    BigDecimal sumOrderTotalPrice(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款订单
     *
     * @param start
     * @param end
     * @param tenantId
     * @return
     */
    Integer countPayOrderQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);

    /**
     * 统计付款店铺
     * @param start
     * @param end
     * @param tenantId
     * @param storeIds
     * @return
     */
    Integer countPayOrderStoreQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds);


    /**
     * 计算待配送数量
     * @param tenantId
     * @return
     */
    Integer getWaitDeliveryNum(Long tenantId);

    /**
     * 批量更新订单状态
     * @param orderStatusBatchUpdateReq
     * @return
     */
    Integer batchUpdateStatus(OrderStatusBatchUpdateParam orderStatusBatchUpdateReq);

    /**
     * 查询sku维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleQuantity(List<Long> skuIds,
                                                      Long tenantId,
                                                      LocalDateTime startTime,
                                                      LocalDateTime endTime);


    /**
     * 查询sku，仓库维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithStoreNoQuantity(List<Long> skuIds,
                                                           Long tenantId,
                                                           LocalDateTime startTime,
                                                           LocalDateTime endTime);

    /**
     * 查询sku、城市维度销量
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithCityQuantity(List<Long> skuIds,
                                                        Long tenantId,
                                                        LocalDateTime startTime,
                                                        LocalDateTime endTime);


    /**
     * 查询订单明细列表
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderDetailEntity> queryOrderDetail(List<Long> orderIds, Long tenantId);


    /**
     * oms分页查询
     * @param orderQueryReq
     * @return
     */
    Page<Order> queryPage(OrderOmsQueryParam orderQueryReq);

    /**
     * 分页查询
     * @param orderQueryReq
     * @return
     */
    Page<Order> queryPage(OrderQueryParam orderQueryReq);


    /**
     * 订单列表
     * @param queryParam
     * @return
     */
    List<Order> queryList(OrderQueryParam queryParam);


    /**
     * 查询待配送订单
     * @param queryParam
     * @return
     */
    List<String> queryNeedDeliveryOrder(OrderDeliveryQueryParam queryParam);

    /**
     * 按订单no批量查询
     * @param orderNos
     * @return
     */
    List<Order> queryByOrderNos(List<String> orderNos);

    /**
     * 按id更新订单
     * @param orderDTO
     * @return
     */
    Boolean updateById(OrderCommandParam orderDTO);

    /**
     * 更新订单状态
     * @param updateReq
     * @return
     */
    Boolean updateStatus(OrderStatusUpdateParam updateReq);

    /**
     * 根据来源状态更新订单状态
     * @param updateParam
     * @return
     */
    Boolean updateStatusByOriginStatus(OrderStatusUpdateCommandParam updateParam);


    /**
     * 更新支付方式
     * @param orderDTO
     * @return
     */
    Boolean updatePayType(OrderCommandParam orderDTO);


    /**
     * 批量更新分账完成时间
     * @param orderList
     * @return
     */
    Boolean batchUpdateProfitSharingFinishTime(List<OrderCommandParam> orderList);

    /**
     * 查询组合包订单
     * @param combineId
     * @param tenantId
     * @return
     */
    List<Order> queryByCombineId(Long combineId, Long tenantId);

    /**
     * 批量查询组合包订单
     * @param combineIds
     * @param tenantId
     * @return
     */
    List<Order> queryByCombineIds(Set<Long> combineIds, Long tenantId);


    /**
     * 统计订单数量
     * @param orderCountReq
     * @return
     */
    Integer countOrderQuantity(OrderCountQueryParam orderCountReq);


    /**
     * 查询小于配送时间切状态是4的订单
     *
     * @param orderAutoFinishDTO
     * @return
     */
    List<Order> queryNeedAutoFinishedOrder(OrderAutoFinishQueryParam orderAutoFinishDTO);


    /**
     * 更新订单状态为完成
     * @param orderIds
     * @return
     */
    int orderFinish(List<Long> orderIds);


    /**
     * 更新配送时间
     * @param param
     * @return
     */
    boolean updateDeliveryTime(OrderDeliveryUpdateParam param);


    /**
     * 更新订单配送时间
     * @param orderNos
     * @param deliveryTime
     * @return
     */
    Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime);

    /**
     * 更新自提完成
     * @param req
     * @return
     */
    boolean selfLiftingFinish(OrderSelfLiftingFinishParam req);

    /**
     * 查询门店在某天配送时间的订单
     * @param storeId
     * @param deliveryTime
     * @return
     */
    List<Order> queryByStoreIdAndDeliveryTime(Long storeId, LocalDateTime deliveryTime, Integer warehouseType);

    /**
     * 根据外部订单号查询订单信息
     * @param orderQueryParam
     * @return
     */
    List<Order> queryListByCustomerOrderIds(OrderQueryParam orderQueryParam);

    /**
     * 查询商品周期内售卖数量
     * @param saleQuantityReq
     * @return
     */
    List<OrderItemSaleQuantityEntity> queryOrderItemSaleQuantity(ItemSaleQuantityQueryParam saleQuantityReq);

    Order updateDeliveryFee(Long orderId,BigDecimal newDeliveryFee,BigDecimal oriDeliveryFee);

    /**
     * 查询供应商订单信息
     * @param supplierOrderTotalReq
     * @return
     */
    List<SupplierOrderEntity> querySupplierOrderList(SupplierOrderTotalQueryParam supplierOrderTotalReq);

    /**
     * 更新订单的外部订单号
     * @param orderId
     * @param newCustomerOrderId
     * @return
     */
    Boolean updateOrderCustomerOrderId(Long orderId, String newCustomerOrderId);
}
