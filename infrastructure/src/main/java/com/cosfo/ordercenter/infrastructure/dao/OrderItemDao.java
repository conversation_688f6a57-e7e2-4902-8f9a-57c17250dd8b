package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusBatchUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;


import java.util.List;

/**
 * <p>
 * 订单详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
public interface OrderItemDao extends IService<OrderItem> {

    /**
     * 批量查询订单明细项
     *
     * @param orderIds
     * @param supplierIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> batchQueryOrderItemDetail(List<Long> orderIds, List<Long> supplierIds);


    /**
     * 统计sku数量
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySkuQuantity(Long tenantId, List<Long> orderIds);

    /**
     * 统计商品销售数量
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySaleQuantity(Long tenantId, List<Long> orderIds);

    /**
     * 更新配送数量
     *
     * @param orderItemId
     * @param quantity
     * @return
     */
    int updateDeliveryQuantity(Long orderItemId, Integer quantity);

    /**
     * 根据orderId查询订单明细
     *
     * @param orderId
     * @return
     */
    List<OrderItem> queryByOrderId(Long orderId);

    /**
     * 批量查询
     *
     * @param orderIds
     * @return
     */
    List<OrderItem> batchQueryByOrderIds(List<Long> orderIds);

    /**
     * 更新订单明细最后售后时间
     *
     * @param dto
     * @return
     */
    Boolean updateAfterSaleExpiryTime(OrderItemUpdateParam dto);

    /**
     * 批量更新最后可售后时间
     *
     * @param orderItemList
     * @return
     */
    Boolean batchUpdateAfterSaleExpiryTime(List<OrderItem> orderItemList);

//    /**
//     * 查询订单明细项以及快照
//     * @param orderItemId
//     * @return
//     */
//    OrderItemWithSnapshot queryDetailById(Long orderItemId);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param orderItemIds
     * @return
     */
    List<OrderItem> batchQuery(Long tenantId, List<Long> orderItemIds);

    /**
     * 批量保存
     *
     * @param orderItemList
     * @return
     */
    boolean batchSave(List<OrderItem> orderItemList);

    /**
     * 更新订单明细状态
     *
     * @param req
     * @return
     */
    Boolean updateStatus(OrderItemStatusUpdateParam req);

    /**
     * 根据 orderItemId 批量查询
     *
     * @param orderItemIds
     * @return
     */
    List<OrderItem> queryByIds(List<Long> orderItemIds);

    /**
     * 批量更新订单项
     *
     * @param updateParam
     * @return
     */
    boolean batchUpdateStatus(OrderItemStatusBatchUpdateParam updateParam);

    /**
     * 更新城配仓号
     * @param orderId
     * @param sourceStoreNo
     * @param storeNo
     * @return
     */
    boolean updateStoreNo(Long orderId, Integer sourceStoreNo, Integer storeNo);

    /**
     * 根据orderId批量查询
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> queryOrderItemVOByOrderIds(Long tenantId, List<Long> orderIds);


}
