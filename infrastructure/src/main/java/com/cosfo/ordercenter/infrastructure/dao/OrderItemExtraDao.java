package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemExtra;

import java.util.List;

/**
 * 订单子项外部系统扩展表
 *
 * <AUTHOR>
 */
public interface OrderItemExtraDao extends IService<OrderItemExtra> {

    /**
     * 单条保存
     *
     * @param orderItemExtraAddParam
     */
    void saveOrderItemExtra(OrderItemExtraAddParam orderItemExtraAddParam);

    /**
     * 批量保存
     *
     * @param orderItemExtraAddParams
     * @return
     */
    boolean batchSave(List<OrderItemExtraAddParam> orderItemExtraAddParams);


    /**
     * 批量查询
     * @param param
     * @return
     */
    List<OrderItemExtra> queryList(OrderItemExtraQueryParam param);

}
