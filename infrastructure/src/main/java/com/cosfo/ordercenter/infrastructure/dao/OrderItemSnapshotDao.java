package com.cosfo.ordercenter.infrastructure.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemSnapshotQueryParam;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
public interface OrderItemSnapshotDao extends IService<OrderItemSnapshot> {

    /**
     * 批量查询订单明细快照
     * @param queryReq
     * @return
     */
    List<OrderItemSnapshot> queryList(OrderItemSnapshotQueryParam queryReq);

    /**
     * 按orderItemIds批量查询
     * @param orderItemIds
     * @return
     */
    List<OrderItemSnapshot> queryByOrderItemIds(List<Long> orderItemIds);

    /**
     * 按orderIds批量查询
     * @param orderIds
     * @return
     */
    List<OrderItemSnapshot> queryByOrderIds(List<Long> orderIds);

    /**
     * 按找orderItemId查询
     * @param orderItemId
     * @return
     */
    OrderItemSnapshot queryByOrderItemId(Long orderItemId);

    /**
     * 批量更新 snapshot taskId
     *
     * @param orderItemIds
     * @param orderId
     * @return
     */
    Boolean batchUpdateTaskId(List<Long> orderItemIds, Long orderId);

    /**
     * 批量保存
     * @param snapshotList
     * @return
     */
    boolean batchSave(List<OrderItemSnapshotAddParam> snapshotList);

}
