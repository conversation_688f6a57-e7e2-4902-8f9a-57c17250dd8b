package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.infrastructure.dao.CombineOrderDao;
import com.cosfo.ordercenter.infrastructure.mapper.CombineOrderMapper;
import com.cosfo.ordercenter.infrastructure.model.order.CombineOrder;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 组合订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class CombineOrderDaoImpl extends ServiceImpl<CombineOrderMapper, CombineOrder> implements CombineOrderDao {

    @Override
    public Long add(Long combineItemId, Long tenantId) {
        CombineOrder combineOrder = new CombineOrder();
        combineOrder.setTenantId(tenantId);
        combineOrder.setCombineItemId(combineItemId);
        save(combineOrder);
        return combineOrder.getId();
    }
}
