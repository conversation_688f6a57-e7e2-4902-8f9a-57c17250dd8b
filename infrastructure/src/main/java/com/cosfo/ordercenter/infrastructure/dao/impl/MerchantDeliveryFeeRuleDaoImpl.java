package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.domain.delivery.param.MerchantDeliveryRuleQueryParam;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.infrastructure.mapper.MerchantDeliveryFeeRuleMapper;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryFeeRule;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 门店运费规则表(MerchantDeliveryFeeRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:36:54
 */
@Service
public class MerchantDeliveryFeeRuleDaoImpl extends ServiceImpl<MerchantDeliveryFeeRuleMapper, MerchantDeliveryFeeRule> implements MerchantDeliveryFeeRuleDao {

    @Override
    public List<MerchantDeliveryFeeRule> listByParam(MerchantDeliveryRuleQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    private LambdaQueryWrapper<MerchantDeliveryFeeRule> buildQueryWrapper(MerchantDeliveryRuleQueryParam param) {
        LambdaQueryWrapper<MerchantDeliveryFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MerchantDeliveryFeeRule::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getWarehouseType()), MerchantDeliveryFeeRule::getType, param.getWarehouseType());
        queryWrapper.eq(Objects.nonNull(param.getDefaultType()), MerchantDeliveryFeeRule::getDefaultType, param.getDefaultType());

        queryWrapper.orderByAsc(MerchantDeliveryFeeRule::getPriority);
        return queryWrapper;
    }
}
