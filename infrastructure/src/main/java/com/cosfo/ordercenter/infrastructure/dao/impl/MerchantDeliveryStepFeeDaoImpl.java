package com.cosfo.ordercenter.infrastructure.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryStepFeeParam;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryStepFeeDao;
import com.cosfo.ordercenter.infrastructure.mapper.MerchantDeliveryStepFeeMapper;
import com.cosfo.ordercenter.infrastructure.model.delivery.MerchantDeliveryStepFee;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 阶梯运费(MerchantDeliveryStepFee)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-14 13:41:43
 */
@Service
public class MerchantDeliveryStepFeeDaoImpl extends ServiceImpl<MerchantDeliveryStepFeeMapper, MerchantDeliveryStepFee> implements MerchantDeliveryStepFeeDao {

    @Override
    public List<MerchantDeliveryStepFee> listByParam(QueryDeliveryStepFeeParam param) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = buildQueryWrapper(param);
        queryWrapper.orderByAsc(MerchantDeliveryStepFee::getStepThreshold);
        return list(queryWrapper);
    }

    @Override
    public List<MerchantDeliveryStepFee> listStepDesc(QueryDeliveryStepFeeParam param) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = buildQueryWrapper(param);
        queryWrapper.orderByDesc(MerchantDeliveryStepFee::getStepThreshold);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<MerchantDeliveryStepFee> buildQueryWrapper(QueryDeliveryStepFeeParam param) {
        LambdaQueryWrapper<MerchantDeliveryStepFee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MerchantDeliveryStepFee::getTenantId, param.getTenantId());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getRuleIds()), MerchantDeliveryStepFee::getRuleId, param.getRuleIds());

        return queryWrapper;
    }
}
