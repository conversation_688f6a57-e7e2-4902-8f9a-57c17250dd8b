package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cosfo.ordercenter.common.enums.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.*;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAddCommand;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleStatusUpdateCommandParam;
import com.cosfo.ordercenter.infrastructure.converter.aftersale.OrderAfterSaleConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderAfterSaleMapper;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleCnt;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
@Service
public class OrderAfterSaleDaoImpl extends ServiceImpl<OrderAfterSaleMapper, OrderAfterSale> implements OrderAfterSaleDao {

    @Override
    public List<Long> batchAdd(List<OrderAfterSaleEntity> orderAfterSales) {
        List<OrderAfterSale> afterSaleList = OrderAfterSaleConverter.convertToOrderAfterSales(orderAfterSales);
        baseMapper.insertList(afterSaleList);
        return afterSaleList.stream().map(OrderAfterSale::getId).collect(Collectors.toList());
    }

    @Override
    public List<OrderAfterSale> queryListByCondition(OrderAfterSaleQueryParam orderAfterSaleQueryParam) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = buildQueryWrapper(orderAfterSaleQueryParam);
        return list(queryWrapper);
    }

    private LambdaQueryWrapper<OrderAfterSale> buildQueryWrapper(OrderAfterSaleQueryParam orderAfterSaleQueryParam) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(orderAfterSaleQueryParam.getTenantId()), OrderAfterSale::getTenantId, orderAfterSaleQueryParam.getTenantId());
        queryWrapper.in(CollectionUtils.isNotEmpty(orderAfterSaleQueryParam.getAfterSaleOrderNos()), OrderAfterSale::getAfterSaleOrderNo, orderAfterSaleQueryParam.getAfterSaleOrderNos());
        queryWrapper.in(CollectionUtils.isNotEmpty(orderAfterSaleQueryParam.getOrderItemIds()), OrderAfterSale::getOrderItemId, orderAfterSaleQueryParam.getOrderItemIds());
        return queryWrapper;
    }

    @Override
    public List<OrderAfterSale> queryList(OrderAfterSaleListQueryParam req) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getStatusList()), OrderAfterSale::getStatus, req.getStatusList());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getServiceType()), OrderAfterSale::getServiceType, req.getServiceType());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getOrderIds()), OrderAfterSale::getOrderId, req.getOrderIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(req.getOrderItemIds()), OrderAfterSale::getOrderItemId, req.getOrderItemIds());

        queryWrapper.eq(req.getTenantId() != null, OrderAfterSale::getTenantId, req.getTenantId());
        queryWrapper.eq(Objects.nonNull(req.getStoreId()), OrderAfterSale::getStoreId, req.getStoreId());
        queryWrapper.eq(Objects.nonNull(req.getAfterSaleType()), OrderAfterSale::getAfterSaleType, req.getAfterSaleType());

        queryWrapper.orderByDesc(OrderAfterSale::getId);
        return list(queryWrapper);
    }

    @Override
    public Page<OrderAfterSale> queryPage(OrderAfterSalePageQueryParam req) {
        MPJLambdaWrapper<OrderAfterSale> queryWrapper = getOrderAfterSaleLambdaQueryWrapper(req);
        Page<OrderAfterSale> page = page(new Page<>(req.getPageNum(), req.getPageSize()), queryWrapper);
        return page;
    }


    private static MPJLambdaWrapper<OrderAfterSale> getOrderAfterSaleLambdaQueryWrapper(OrderAfterSalePageQueryParam queryReq) {
        MPJLambdaWrapper<OrderAfterSale> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(OrderAfterSale.class);
        queryWrapper.eq(queryReq.getAfterSaleOrderNo() != null, OrderAfterSale::getAfterSaleOrderNo, queryReq.getAfterSaleOrderNo());
        queryWrapper.eq(queryReq.getAfterSaleType() != null, OrderAfterSale::getAfterSaleType, queryReq.getAfterSaleType());
        queryWrapper.between(queryReq.getStartTime() != null && queryReq.getEndTime() != null, OrderAfterSale::getCreateTime, queryReq.getStartTime(), queryReq.getEndTime());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryReq.getStatusList()), OrderAfterSale::getStatus, queryReq.getStatusList());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryReq.getServiceTypeList()), OrderAfterSale::getServiceType, queryReq.getServiceTypeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryReq.getStoreIds()), OrderAfterSale::getStoreId, queryReq.getStoreIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryReq.getAccountIds()), OrderAfterSale::getAccountId, queryReq.getAccountIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryReq.getTenantIds()), OrderAfterSale::getTenantId, queryReq.getTenantIds());
        queryWrapper.ge(ObjectUtils.isNotEmpty(queryReq.getHandleTimeBegin()),OrderAfterSale::getHandleTime,queryReq.getHandleTimeBegin());
        queryWrapper.le(ObjectUtils.isNotEmpty(queryReq.getHandleTimeEnd()),OrderAfterSale::getHandleTime,queryReq.getHandleTimeEnd());
        boolean orderJoinFlag = false;
        boolean orderItemJoinFlag = false;
        boolean orderItemSnapshotJoinFlag = false;
        if (queryReq.getTenantId() != null) {
            orderJoinFlag = true;
            queryWrapper.eq(Order::getTenantId, queryReq.getTenantId());
        }

        if (!StringUtils.isEmpty(queryReq.getOrderNo())) {
            orderJoinFlag = true;
            queryWrapper.in(Order::getOrderNo, queryReq.getOrderNo());
        }

        if (queryReq.getSupplierTenantId() != null) {
            orderJoinFlag = true;
            queryWrapper.in(Order::getSupplierTenantId, queryReq.getSupplierTenantId());
        }

        if (queryReq.getWarehouseType() != null) {
            orderJoinFlag = true;
            queryWrapper.in(Order::getWarehouseType, queryReq.getWarehouseType());
        }

        if (queryReq.getWarehouseNo() != null) {
            orderJoinFlag = true;
            queryWrapper.in(Order::getWarehouseNo, queryReq.getWarehouseNo());
        }
        if (!CollectionUtils.isEmpty(queryReq.getItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getItemId, queryReq.getItemIds());
        }

        if (!CollectionUtils.isEmpty(queryReq.getSupplierIds())) {
            orderItemSnapshotJoinFlag = true;
            queryWrapper.in(OrderItemSnapshot::getSupplierTenantId, queryReq.getSupplierIds());
        }

        if (orderJoinFlag) {
            queryWrapper.leftJoin(Order.class, Order::getId, OrderAfterSale::getOrderId);
        }
        if (orderItemJoinFlag) {
            queryWrapper.leftJoin(OrderItem.class, OrderItem::getId, OrderAfterSale::getOrderItemId);
        }
        if (orderItemSnapshotJoinFlag) {
            queryWrapper.leftJoin(OrderItemSnapshot.class, OrderItemSnapshot::getOrderItemId, OrderAfterSale::getOrderItemId);
        }
        queryWrapper.lt(queryReq.getMaxId() != null, OrderAfterSale::getId, queryReq.getMaxId());
        queryWrapper.orderByDesc(OrderAfterSale::getId);
        return queryWrapper;

    }

    @Override
    public OrderAfterSale queryByAfterSaleNo(String afterSaleNo) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAfterSale::getAfterSaleOrderNo, afterSaleNo);
        List<OrderAfterSale> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public Integer countOrderAfterSale(OrderAfterSaleCountParam orderAfterSaleCountReq) {
        if (CollectionUtils.isEmpty(orderAfterSaleCountReq.getOrderIds())
                && CollectionUtils.isEmpty(orderAfterSaleCountReq.getStatusList())
                && orderAfterSaleCountReq.getStoreId() == null
                && orderAfterSaleCountReq.getTenantId() == null) {
            throw new BizException("请检查入参");
        }
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(orderAfterSaleCountReq.getOrderIds()), OrderAfterSale::getOrderId, orderAfterSaleCountReq.getOrderIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderAfterSaleCountReq.getStatusList()), OrderAfterSale::getStatus, orderAfterSaleCountReq.getStatusList());
        queryWrapper.eq(orderAfterSaleCountReq.getTenantId() != null, OrderAfterSale::getTenantId, orderAfterSaleCountReq.getTenantId());
        queryWrapper.eq(orderAfterSaleCountReq.getStoreId() != null, OrderAfterSale::getStoreId, orderAfterSaleCountReq.getStoreId());
        return Long.valueOf(count(queryWrapper)).intValue();
    }

    @Override
    public List<OrderAfterSale> queryByOrderId(Long orderId, Long tenantId) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAfterSale::getOrderId, orderId);
        queryWrapper.eq(tenantId != null, OrderAfterSale::getTenantId, tenantId);
        queryWrapper.orderByDesc(OrderAfterSale::getId);
        return list(queryWrapper);
    }


    @Override
    public Long getRecentlyUsedReturnAddressId(Long tenantId) {
        return baseMapper.getRecentlyUsedReturnAddressId(tenantId);
    }

    @Override
    public List<OrderAfterSaleEntity> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleParam req) {
        return baseMapper.queryOrderAfterSaleForBill(req.getTenantId(), req.getStartTime(), req.getEndTime());
    }

    @Override
    public boolean updateStatus(OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateReq) {
        LambdaUpdateWrapper<OrderAfterSale> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(OrderAfterSale::getStatus, orderAfterSaleStatusUpdateReq.getTargetStatus());
        if (Objects.equals(orderAfterSaleStatusUpdateReq.getTargetStatus(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue())) {
            updateWrapper.set(OrderAfterSale::getFinishedTime, LocalDateTime.now());
        }
        updateWrapper.set(!StringUtils.isEmpty(orderAfterSaleStatusUpdateReq.getHandleRemark()), OrderAfterSale::getHandleRemark, orderAfterSaleStatusUpdateReq.getHandleRemark());
        updateWrapper.set(OrderAfterSale::getUpdateTime, LocalDateTime.now());
        updateWrapper.eq(OrderAfterSale::getId, orderAfterSaleStatusUpdateReq.getAfterSaleId());
        updateWrapper.eq(orderAfterSaleStatusUpdateReq.getSourceStatus() != null, OrderAfterSale::getStatus, orderAfterSaleStatusUpdateReq.getSourceStatus());
        return update(updateWrapper);
    }

    @Override
    public List<OrderAfterSale> queryByNos(List<String> orderAfterSaleNos) {
        if (CollectionUtils.isEmpty(orderAfterSaleNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderAfterSale::getAfterSaleOrderNo, orderAfterSaleNos);
        return list(queryWrapper);
    }

    @Override
    public List<OrderAfterSale> queryByIds(List<Long> orderAfterSaleIds) {
        if (CollectionUtils.isEmpty(orderAfterSaleIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderAfterSale::getId, orderAfterSaleIds);
        return list(queryWrapper);
    }

    @Override
    public Map<Long, Integer> countOrderAfterSaleByOrderId(OrderAfterSaleCountParam req) {

        List<OrderAfterSaleCnt> orderAfterSaleCnts = baseMapper.countOrderAfterSaleByOrderId(req);
        Map<Long, Integer> result = orderAfterSaleCnts.stream().collect(Collectors.toMap(OrderAfterSaleCnt::getOrderId, OrderAfterSaleCnt::getCnt));
        return result;
    }

    @Override
    public boolean autoFinish() {
        LambdaUpdateWrapper<OrderAfterSale> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderAfterSale::getStatus, OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        updateWrapper.le(OrderAfterSale::getAutoFinishedTime, LocalDateTime.now());

        updateWrapper.set(OrderAfterSale::getStatus, OrderAfterSaleStatusEnum.AUDITED_FAILED.getValue());
        updateWrapper.set(OrderAfterSale::getFinishedTime, LocalDateTime.now());
        return update(updateWrapper);
    }

    @Override
    public boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo) {
        LambdaUpdateWrapper<OrderAfterSale> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(storeNo != null, OrderAfterSale::getStoreNo, storeNo);

        updateWrapper.eq(OrderAfterSale::getId, afterSaleId);
        updateWrapper.eq(sourceStoreNo != null, OrderAfterSale::getStoreNo, sourceStoreNo);
        return update(updateWrapper);
    }

    @Override
    public List<OrderAfterSale> queryListByParam(OrderAfterSaleQueryParam orderAfterSaleQueryParam) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = buildQueryWrapper(orderAfterSaleQueryParam);
        queryWrapper.in(CollectionUtils.isNotEmpty(orderAfterSaleQueryParam.getCustomerAfterSaleOrderNos()), OrderAfterSale::getCustomerAfterSaleOrderNo, orderAfterSaleQueryParam.getCustomerAfterSaleOrderNos());
        return list(queryWrapper);
    }
}
