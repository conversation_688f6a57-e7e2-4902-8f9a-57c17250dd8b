package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleRuleDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderAfterSaleRuleMapper;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleRule;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 *
 * @author: xiaowk
 * @time: 2023/8/11 上午10:44
 */
@Service
public class OrderAfterSaleRuleDaoImpl extends ServiceImpl<OrderAfterSaleRuleMapper, OrderAfterSaleRule> implements OrderAfterSaleRuleDao {

    @Override
    public Long addOrUpdate(OrderAfterSaleRuleCommandParam orderAfterSaleRule) {
        List<OrderAfterSaleRuleEntity> list = baseMapper.queryByTenantId(orderAfterSaleRule.getTenantId());
        OrderAfterSaleRuleEntity existObj = list.stream().filter(e -> Objects.equals(e.getDefaultFlag(), orderAfterSaleRule.getDefaultFlag())).findFirst().orElse(null);
        if (existObj == null) {
            baseMapper.insertSelective(orderAfterSaleRule);
            return orderAfterSaleRule.getId();
        }
        orderAfterSaleRule.setId(existObj.getId());
        baseMapper.updateByPrimaryKeySelective(orderAfterSaleRule);
        return orderAfterSaleRule.getId();
    }

    @Override
    public List<OrderAfterSaleRuleEntity> queryByTenantId(Long tenantId) {
        return baseMapper.queryByTenantId(tenantId);
    }

    @Override
    public Integer updateRule(OrderAfterSaleRuleCommandParam orderAfterSaleRule) {
        if(orderAfterSaleRule == null || orderAfterSaleRule.getId() == null){
            throw new BizException("售后规则id不能为空");
        }
        return baseMapper.updateByPrimaryKeySelective(orderAfterSaleRule);
    }

    @Override
    public Integer deleteRule(Long id) {
        return baseMapper.deleteByPrimaryKey(id);
    }
}
