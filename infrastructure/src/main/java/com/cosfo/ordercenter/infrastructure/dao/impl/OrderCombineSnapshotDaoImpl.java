package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.cosfo.ordercenter.domain.order.param.command.OrderCombineSnapshotAddParam;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderCombineSnapshotConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderCombineSnapshotDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderCombineSnapshotMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderCombineSnapshot;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 组合订单快照 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Service
public class OrderCombineSnapshotDaoImpl extends ServiceImpl<OrderCombineSnapshotMapper, OrderCombineSnapshot> implements OrderCombineSnapshotDao {

    @Override
    public boolean batchSave(List<OrderCombineSnapshotAddParam> snapshotList) {
        List<OrderCombineSnapshot> orderCombineSnapshots = OrderCombineSnapshotConverter.convertToOrderCombineSnapshotList(snapshotList);
        return baseMapper.insertList(orderCombineSnapshots) == snapshotList.size();
    }
}
