package com.cosfo.ordercenter.infrastructure.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderItemExtraConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemExtraDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderItemExtraMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemExtra;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-09-22
 * @Description:
 */
@Service
public class OrderItemExtraDaoImpl extends ServiceImpl<OrderItemExtraMapper, OrderItemExtra> implements OrderItemExtraDao {

    @Override
    public void saveOrderItemExtra(OrderItemExtraAddParam orderItemExtraAddParam) {
        OrderItemExtra orderItemExtra = OrderItemExtraConverter.toOrderItemExtra(orderItemExtraAddParam);
        save(orderItemExtra);
    }

    @Override
    public boolean batchSave(List<OrderItemExtraAddParam> orderItemExtraAddParams) {
        if (CollectionUtils.isEmpty(orderItemExtraAddParams)) {
            return false;
        }
        List<OrderItemExtra> orderItemExtraEntities = OrderItemExtraConverter.converterToItemExtraList(orderItemExtraAddParams);
        return orderItemExtraAddParams.size() == baseMapper.batchSave(orderItemExtraEntities);
    }

    @Override
    public List<OrderItemExtra> queryList(OrderItemExtraQueryParam param) {
        LambdaQueryWrapper<OrderItemExtra> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), OrderItemExtra::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getOrderId()), OrderItemExtra::getOrderId, param.getOrderId());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getCustomerOrderItemIdList()), OrderItemExtra::getCustomerOrderItemId, param.getCustomerOrderItemIdList());
        return list(queryWrapper);
    }
}
