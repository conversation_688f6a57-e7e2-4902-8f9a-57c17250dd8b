package com.cosfo.ordercenter.infrastructure.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.ordercenter.infrastructure.dao.TenantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.infrastructure.mapper.TenantDeliveryFeeRuleMapper;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 11:25
 * @Description:
 */
@Service
@Slf4j
public class TenantDeliveryFeeRuleDaoImpl extends ServiceImpl<TenantDeliveryFeeRuleMapper, TenantDeliveryFeeRule> implements TenantDeliveryFeeRuleDao {


    @Override
    public TenantDeliveryFeeRule selectByTenantId(Long tenantId) {
        LambdaQueryWrapper<TenantDeliveryFeeRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantDeliveryFeeRule::getTenantId, tenantId);
        List<TenantDeliveryFeeRule> list = list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }
}
