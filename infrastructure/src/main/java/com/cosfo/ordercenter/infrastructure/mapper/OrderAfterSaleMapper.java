package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleCountParam;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleCnt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
@Mapper
public interface OrderAfterSaleMapper extends BaseMapper<OrderAfterSale> {

    /**
     * 查询售后记录最近使用的退回地址id
     * @param tenantId
     * @return
     */
    Long getRecentlyUsedReturnAddressId(@Param("tenantId") Long tenantId);

    List<OrderAfterSaleEntity> queryOrderAfterSaleForBill(@Param("tenantId") Long tenantId,
                                                          @Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime);


    /**
     * 统计售后单数量
     * @param req
     * @return
     */
    List<OrderAfterSaleCnt> countOrderAfterSaleByOrderId(@Param("req") OrderAfterSaleCountParam req);


    /**
     * 批量插入售后单
     * @param list
     */
//    int insertList(@Param("list")List<OrderAfterSale> list);


    int insertList(@Param("list")List<OrderAfterSale> list);


    /**
     * 统计配送前退款的处理中的售后单数量
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> getAllRefundDealNoByTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


}
