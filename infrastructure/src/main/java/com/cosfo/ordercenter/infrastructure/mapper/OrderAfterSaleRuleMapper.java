package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleRule;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OrderAfterSaleRuleMapper extends BaseMapper<OrderAfterSaleRule> {
    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);
    int deleteByPrimaryKeys(List<Long> list);

    /**
     * 新增售后规则
     *
     * @param record
     * @return
     */
    int insert(OrderAfterSaleRuleCommandParam record);


    int batchInsert(List<OrderAfterSaleRuleCommandParam> list);


    /**
     * 新增售后规则
     *
     * @param record
     * @return
     */
    int insertSelective(OrderAfterSaleRuleCommandParam record);

    /**
     * 根据Id查询售后规则
     *
     * @param id
     * @return
     */
    OrderAfterSaleRuleEntity selectByPrimaryKey(Long id);

    /**
     * 更新售后规则
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(OrderAfterSaleRuleCommandParam record);

    /**
     * 更新售后规则
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(OrderAfterSaleRuleCommandParam record);

    List<OrderAfterSaleRuleEntity> queryByTenantId(Long tenantId);

    /**
     * 查询售后规则
     *
     * @return
     */
    List<OrderAfterSaleRuleEntity> listAll();
}