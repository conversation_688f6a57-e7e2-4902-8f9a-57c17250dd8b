package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderCombineSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组合订单快照 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-10
 */
@Mapper
public interface OrderCombineSnapshotMapper extends BaseMapper<OrderCombineSnapshot> {

    int insertList(@Param("list")List<OrderCombineSnapshot> list);



}
