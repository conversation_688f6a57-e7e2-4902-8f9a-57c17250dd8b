package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemExtra;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单子项外部系统扩展表
 * <AUTHOR>
 */
@Mapper
public interface OrderItemExtraMapper extends BaseMapper<OrderItemExtra> {


    /**
     * 批量
     *
     * @param orderItemExtraList
     * @return
     */
    int batchSave(@Param("itemList") List<OrderItemExtra> orderItemExtraList);

}
