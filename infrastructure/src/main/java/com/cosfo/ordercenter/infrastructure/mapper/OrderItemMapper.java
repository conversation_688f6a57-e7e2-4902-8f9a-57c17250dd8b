package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * 查询订单明细和快照
     *
     * @param orderIds
     * @param supplierIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> batchQueryOrderItemDetail(@Param("orderIds") List<Long> orderIds, @Param("supplierIds") List<Long> supplierIds);


    /**
     * 查询销售sku数量
     * （sku维度去重）
     * 入参set
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySkuQuantity(@Param("tenantId") Long tenantId, @Param("orderIds") List<Long> orderIds);

    /**
     * 查询销售数量
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    Integer querySaleQuantity(@Param("tenantId") Long tenantId, @Param("orderIds") List<Long> orderIds);

    /**
     * 更新配送数量
     *
     * @param orderItemId
     * @param quantity
     * @return
     */
    int updateDeliveryQuantity(@Param("orderItemId") Long orderItemId, @Param("quantity") Integer quantity);

    /**
     * 批量
     *
     * @param orderItemList
     * @return
     */
    int batchSave(@Param("itemList") List<OrderItem> orderItemList);

    /**
     * 查询订单项详情
     *
     * @param tenantId
     * @param orderIds
     * @return
     */
    List<OrderItemWithSnapshotEntity> queryOrderItemVOByOrderIds(@Param("tenantId") Long tenantId,
                                                           @Param("orderIds") List<Long> orderIds);
}
