package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface OrderItemSnapshotMapper extends BaseMapper<OrderItemSnapshot> {

    /**
     * 批量保存
     * @param snapshotList
     * @return
     */
    int batchSave(@Param("snapshotList") List<OrderItemSnapshot> snapshotList);

}
