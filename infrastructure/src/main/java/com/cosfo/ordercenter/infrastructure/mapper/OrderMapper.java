package com.cosfo.ordercenter.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.ordercenter.domain.order.entity.OrderDetailEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSaleQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderSkuQuantityEntity;
import com.cosfo.ordercenter.domain.order.entity.SupplierOrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderStatusBatchUpdateParam;
import com.cosfo.ordercenter.domain.order.param.query.SupplierOrderTotalQueryParam;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    BigDecimal sumOrderTotalPrice(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("tenantId") Long tenantId,
                                  @Param("storeIds") List<Long> storeIds);


    /**
     * 时间段内支付订单数
     *
     * @param startTime
     * @param endTime
     * @param tenantId
     * @return
     */
    Integer countPayOrderQuantity(@Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime,
                                  @Param("tenantId") Long tenantId,
                                  @Param("storeIds") List<Long> storeIds);


    /**
     * 计算支付店铺数
     *
     * @param startTime
     * @param endTime
     * @param tenantId
     * @param storeIds
     * @return
     */
    Integer countPayOrderStoreQuantity(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("tenantId") Long tenantId,
                                       @Param("storeIds") List<Long> storeIds);


    /**
     * 待配送订单数量
     *
     * @param tenantId
     * @return
     */
    Integer countWaitDeliveryQuantity(@Param("tenantId") Long tenantId);

    /**
     * 批量更新订单状态
     *
     * @param batchUpdateReq
     * @return
     */
    Integer batchUpdateStatus(@Param("req") OrderStatusBatchUpdateParam batchUpdateReq);


    /**
     * 批量更新订单状态
     *
     * @param orderIds
     * @param originStatusList
     * @param updateStatus
     * @return
     */
    Integer batchUpdateStatusByOrderIds(@Param("orderIds") List<Long> orderIds,
                                        @Param("originStatusList") List<Integer> originStatusList,
                                        @Param("updateStatus") Integer updateStatus);

    /**
     * 查询sku维度销量
     *
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleQuantity(@Param("skuIds") List<Long> skuIds,
                                                      @Param("tenantId") Long tenantId,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);


    /**
     * 查询sku，仓库维度销量
     *
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithStoreNoQuantity(@Param("skuIds") List<Long> skuIds,
                                                           @Param("tenantId") Long tenantId,
                                                           @Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询sku、城市维度销量
     *
     * @param skuIds
     * @param tenantId
     * @param startTime
     * @param endTime
     * @return
     */
    List<OrderSkuQuantityEntity> querySkuSaleWithCityQuantity(@Param("skuIds") List<Long> skuIds,
                                                        @Param("tenantId") Long tenantId,
                                                        @Param("startTime") LocalDateTime startTime,
                                                        @Param("endTime") LocalDateTime endTime);


    /**
     * 获取订单明细列表
     *
     * @param orderIds
     * @param tenantId
     * @return
     */
    List<OrderDetailEntity> queryOrderDetail(@Param("orderIds") List<Long> orderIds, @Param("tenantId") Long tenantId);

    /**
     * 更正订单配送时间（由于各种因素未如期配送，改为新的配送时间）
     *
     * @param orderNoList
     * @param deliveryTime
     * @return
     */
    Integer updateOrderDeliveryTime(@Param("orderNoList") List<String> orderNoList, @Param("deliveryTime") LocalDateTime deliveryTime);


    /**
     * 统计商品售卖数量
     * @param startTime
     * @param endTime
     * @param tenantId
     * @param storeId
     * @param itemIds
     * @return
     */
    List<OrderItemSaleQuantityEntity> queryOrderItemSaleQuantity(@Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime,
                                                                 @Param("tenantId") Long tenantId,
                                                                 @Param("storeId") Long storeId,
                                                                 @Param("itemIds") List<Long> itemIds,
                                                                 @Param("afterSaleStatus") List<Integer> afterSaleStatusList,
                                                                 @Param("orderStatus") List<Integer> orderStatusList);

    List<SupplierOrderEntity> querySupplierOrderList(@Param("req") SupplierOrderTotalQueryParam supplierOrderTotalReq);


    /**
     * 统计已支付后状态为10-待出库，未收到创建履约单成功回告消息(履约单号字段为空) 的订单号
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> getWaitFulfillmentOrderNoByTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 更新订单的外部系统订单号
     * @param orderId
     * @param newCustomerOrderId
     * @return
     */
    Integer updateOrderCustomerOrderId(@Param("orderId") Long orderId, @Param("newCustomerOrderId") String newCustomerOrderId);

    /**
     * 查询订单状态为指定状态的订单数量
     * @param tenantId
     * @param statusList
     * @return
     */
    Integer countByStatusList(@Param("tenantId") Long tenantId, @Param("statusList") List<Integer> statusList);

}
