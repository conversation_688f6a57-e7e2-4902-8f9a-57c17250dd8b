package com.cosfo.ordercenter.infrastructure.model.delivery;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: xiaowk
 * @time: 2024/11/7 下午4:27
 */
@Data
public class DeliveryFeeCalRule implements Serializable {
    private static final long serialVersionUID = 5728452816431472827L;
    /**
     * 起步计算门槛
     */
    private BigDecimal startThreshold;

    /**
     * 起步计算运费，满足起步门槛
     */
    private BigDecimal startDeliveryFee;

    /**
     * 阶梯计算因子，默认为1，件数每加1，运费加step_fee
     */
    private BigDecimal stepFactor;

    /**
     * 阶梯费
     */
    private BigDecimal stepFee;

}
