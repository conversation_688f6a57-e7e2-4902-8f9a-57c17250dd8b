package com.cosfo.ordercenter.infrastructure.model.delivery;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 运费规则仓库信息关联表(MerchantDeliveryRuleWarehouseRelation)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 15:34:36
 */
@Data
@TableName("merchant_delivery_rule_warehouse_relation")
public class MerchantDeliveryRuleWarehouseRelation implements Serializable {
    private static final long serialVersionUID = 554120813750870334L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 门店运费规则id
     */
    private Long ruleId;
    /**
     * 仓库编号
     */
    private Integer warehouseNo;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

}

