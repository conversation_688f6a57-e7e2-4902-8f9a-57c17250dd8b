package com.cosfo.ordercenter.infrastructure.model.delivery;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * tenant_delivery_fee_area
 *
 * <AUTHOR>
@Data
@TableName("tenant_delivery_fee_area")
public class TenantDeliveryFeeArea implements Serializable {
    /**
     * 主键Id
     */
    private Long id;

    /**
     * 品牌方Id
     */
    private Long tenantId;

    /**
     * 默认运费规则Id
     */
    private Long ruleId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String area;

    /**
     * 运费金额
     */
    private BigDecimal defaultPrice;

    /**
     * 规则[{"categoryId":1,"type":0,"free_price":10}]
     */
    private String rule;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分组Id
     */
    private Integer groupId;

    private static final long serialVersionUID = 1L;
}