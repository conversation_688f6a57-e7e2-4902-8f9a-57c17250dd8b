package com.cosfo.ordercenter.infrastructure.model.order;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderDetail implements Serializable {
    /**
     * 订单Id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 门店ID
     */
    private Long storeId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 支付类型
     */
    private Integer payType;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 订单完成时间
     */
    private LocalDateTime finishedTime;
    /**
     * 商品名称
     */
    private String title;
    /**
     * 商品项itemId
     */
    private Long itemId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    /**
     * 数量
     */
    private Integer amount;
    /**
     * 单价
     */
    private BigDecimal skuPrice;
    /**
     * 商品总价
     */
    private BigDecimal skuTotalPrice;
    /**
     * 运费
     */
    private BigDecimal deliveryFee;
    /**
     * 订单金额
     */
    private BigDecimal orderPrice;
    /**
     * 订单项Id
     */
    private Long orderItemId;
    /**
     * 供应商租户Id
     */
    private Long supplyTenantId;
    /**
     * 供应商skuId
     */
    private Long supplySkuId;
    /**
     * 主图片
     */
    private String mainPicture;

    /**
     * 供应价
     */
    private BigDecimal supplyPrice;

    /**
     * 订单类型 0无仓 1三方仓 2自营仓
     */
    private Integer warehouseType;

    /**
     * 定价类型
     */
    private Integer pricingType;

    /**
     * 定价数值
     */
    private BigDecimal pricingNumber;

    /**
     * 商品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;

    /**
     * 货品重量
     */
    private BigDecimal weight;
}
