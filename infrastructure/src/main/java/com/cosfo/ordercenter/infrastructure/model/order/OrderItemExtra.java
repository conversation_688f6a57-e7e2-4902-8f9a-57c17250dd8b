package com.cosfo.ordercenter.infrastructure.model.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单子项外部扩展表
 * @TableName order_item_extra
 */
@TableName(value ="order_item_extra")
@Data
public class OrderItemExtra implements Serializable {
    /**
     * 主键Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单商品id
     */
    private Long orderItemId;

    /**
     * 外部系统子订单号
     */
    private String customerOrderItemId;

    /**
     * 外部系统货源skuCode
     */
    private String customerSkuCode;

    /**
     * 鲜沐sku编码
     */
    private String skuCode;

    /**
     * 外部系统货源商品标题
     */
    private String customerSkuTitle;

    /**
     * 外部系统货源商品规格
     */
    private String customerSkuSpecification;

    /**
     * 外部系统货源商品规格单位
     */
    private String customerSkuSpecificationUnit;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}