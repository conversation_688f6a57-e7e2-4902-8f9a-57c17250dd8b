package com.cosfo.ordercenter.infrastructure.repository.aftersale;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleAddCommand;
import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleStatusUpdateCommandParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleCommandRepository;
import com.cosfo.ordercenter.infrastructure.converter.aftersale.OrderAfterSaleConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAfterSaleCommandRepositoryImpl implements OrderAfterSaleCommandRepository {

    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;

    @Override
    public List<Long> batchAdd(List<OrderAfterSaleEntity> orderAfterSales) {
        return orderAfterSaleDao.batchAdd(orderAfterSales);
    }

    @Override
    public boolean updateStatus(OrderAfterSaleStatusUpdateCommandParam orderAfterSaleStatusUpdateReq) {
        return orderAfterSaleDao.updateStatus(orderAfterSaleStatusUpdateReq);
    }

    @Override
    public boolean autoFinish() {
        return orderAfterSaleDao.autoFinish();
    }

    @Override
    public boolean updateStoreNo(Long afterSaleId, Integer sourceStoreNo, Integer storeNo) {
        return orderAfterSaleDao.updateStoreNo(afterSaleId, sourceStoreNo, storeNo);
    }

    @Override
    public Long save(OrderAfterSaleEntity afterSaleEntity) {
        OrderAfterSale orderAfterSale = OrderAfterSaleConverter.convertToOrderAfterSale(afterSaleEntity);
        orderAfterSaleDao.save(orderAfterSale);
        return orderAfterSale.getId();
    }

    @Override
    public boolean updateById(OrderAfterSaleEntity orderaftersaleEntity) {
        OrderAfterSale orderAfterSale = OrderAfterSaleConverter.convertToOrderAfterSale(orderaftersaleEntity);
        return orderAfterSaleDao.updateById(orderAfterSale);
    }
}
