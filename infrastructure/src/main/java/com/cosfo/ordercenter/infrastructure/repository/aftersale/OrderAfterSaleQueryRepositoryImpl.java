package com.cosfo.ordercenter.infrastructure.repository.aftersale;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.*;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.aftersale.OrderAfterSaleConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAfterSaleQueryRepositoryImpl implements OrderAfterSaleQueryRepository {

    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Override
    public List<OrderAfterSaleEntity> queryListByCondition(OrderAfterSaleQueryParam orderAfterSaleQueryParam) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam));
    }

    @Override
    public List<OrderAfterSaleEntity> queryList(OrderAfterSaleListQueryParam req) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryList(req));
    }

    @Override
    public PageInfo<OrderAfterSaleEntity> queryPage(OrderAfterSalePageQueryParam req) {
        Page<OrderAfterSale> orderAfterSalePage = orderAfterSaleDao.queryPage(req);
        return OrderAfterSaleConverter.converterToEntityPage(orderAfterSalePage);
    }

    @Override
    public OrderAfterSaleEntity queryByAfterSaleNo(String afterSaleNo) {
        return OrderAfterSaleConverter.toOrderAfterSaleEntity(orderAfterSaleDao.queryByAfterSaleNo(afterSaleNo));
    }

    @Override
    public Integer countOrderAfterSale(OrderAfterSaleCountParam orderAfterSaleCountReq) {
        return orderAfterSaleDao.countOrderAfterSale(orderAfterSaleCountReq);
    }

    @Override
    public List<OrderAfterSaleEntity> queryByOrderId(Long orderId, Long tenantId) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryByOrderId(orderId, tenantId));
    }

    @Override
    public Long getRecentlyUsedReturnAddressId(Long tenantId) {
        return orderAfterSaleDao.getRecentlyUsedReturnAddressId(tenantId);
    }

    @Override
    public List<OrderAfterSaleEntity> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleParam req) {
        return orderAfterSaleDao.queryOrderAfterSaleForBill(req);
    }

    @Override
    public List<OrderAfterSaleEntity> queryByNos(List<String> orderAfterSaleNos) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryByNos(orderAfterSaleNos));
    }

    @Override
    public OrderAfterSaleEntity queryByNo(String orderAfterSaleNo) {
        LambdaQueryWrapper<OrderAfterSale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderAfterSale::getAfterSaleOrderNo, orderAfterSaleNo);
        return OrderAfterSaleConverter.toOrderAfterSaleEntity(orderAfterSaleDao.getOne(queryWrapper));
    }

    @Override
    public List<OrderAfterSaleEntity> queryByIds(List<Long> orderAfterSaleIds) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryByIds(orderAfterSaleIds));
    }

    @Override
    public Map<Long, Integer> countOrderAfterSaleByOrderId(OrderAfterSaleCountParam req) {
        return orderAfterSaleDao.countOrderAfterSaleByOrderId(req);
    }

    @Override
    public List<OrderAfterSaleEntity> queryListByParam(OrderAfterSaleQueryParam orderAfterSaleQueryParam) {
        return OrderAfterSaleConverter.convertToOrderAfterSaleEntityList(orderAfterSaleDao.queryListByParam(orderAfterSaleQueryParam));
    }

    @Override
    public OrderAfterSaleEntity queryById(Long id) {
        return  OrderAfterSaleConverter.toOrderAfterSaleEntity(orderAfterSaleDao.getById(id));
    }
}
