package com.cosfo.ordercenter.infrastructure.repository.aftersale;

import com.cosfo.ordercenter.domain.aftersale.param.command.OrderAfterSaleRuleCommandParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleRuleCommandRepository;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleRuleDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAfterSaleRuleCommandRepositoryImpl implements OrderAfterSaleRuleCommandRepository {

    @Resource
    private OrderAfterSaleRuleDao orderAfterSaleRuleDao;

    @Override
    public Long addOrUpdate(OrderAfterSaleRuleCommandParam orderAfterSaleRule) {
        return orderAfterSaleRuleDao.addOrUpdate(orderAfterSaleRule);
    }

    @Override
    public Integer updateRule(OrderAfterSaleRuleCommandParam orderAfterSaleRule) {
        return orderAfterSaleRuleDao.updateRule(orderAfterSaleRule);
    }

    @Override
    public Integer deleteRule(Long id) {
        return orderAfterSaleRuleDao.deleteRule(id);
    }
}
