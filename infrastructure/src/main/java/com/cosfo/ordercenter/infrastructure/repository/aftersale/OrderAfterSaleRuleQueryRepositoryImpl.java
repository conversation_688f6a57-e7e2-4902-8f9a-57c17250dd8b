package com.cosfo.ordercenter.infrastructure.repository.aftersale;

import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleRuleEntity;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleRuleQueryRepository;
import com.cosfo.ordercenter.infrastructure.dao.OrderAfterSaleRuleDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAfterSaleRuleQueryRepositoryImpl implements OrderAfterSaleRuleQueryRepository {

    @Resource
    private OrderAfterSaleRuleDao orderAfterSaleRuleDao;

    @Override
    public List<OrderAfterSaleRuleEntity> queryByTenantId(Long tenantId) {
        return orderAfterSaleRuleDao.queryByTenantId(tenantId);
    }
}
