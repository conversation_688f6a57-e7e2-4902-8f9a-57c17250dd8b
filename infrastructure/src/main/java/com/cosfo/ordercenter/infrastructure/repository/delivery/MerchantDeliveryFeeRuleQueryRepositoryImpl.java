package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.MerchantDeliveryFeeRuleEntity;
import com.cosfo.ordercenter.domain.delivery.param.MerchantDeliveryRuleQueryParam;
import com.cosfo.ordercenter.domain.delivery.repository.MerchantDeliveryFeeRuleQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.delivery.MerchantDeliveryFeeRuleConverter;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryFeeRuleDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MerchantDeliveryFeeRuleQueryRepositoryImpl implements MerchantDeliveryFeeRuleQueryRepository {

    @Resource
    private MerchantDeliveryFeeRuleDao merchantDeliveryFeeRuleDao;

    @Override
    public List<MerchantDeliveryFeeRuleEntity> listByParam(MerchantDeliveryRuleQueryParam param) {
        return MerchantDeliveryFeeRuleConverter.convertFeeRuleEntity(merchantDeliveryFeeRuleDao.listByParam(param));
    }
}
