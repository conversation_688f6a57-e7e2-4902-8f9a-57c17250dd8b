package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryWarehouseParam;
import com.cosfo.ordercenter.domain.delivery.repository.MerchantDeliveryRuleWarehouseRelationQueryRepository;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryRuleWarehouseRelationDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public class MerchantDeliveryRuleWarehouseRelationQueryRepositoryImpl implements MerchantDeliveryRuleWarehouseRelationQueryRepository {

    @Resource
    private MerchantDeliveryRuleWarehouseRelationDao merchantDeliveryRuleWarehouseRelationDao;

    @Override
    public Set<Long> listRuleIds(QueryDeliveryWarehouseParam param) {
        return merchantDeliveryRuleWarehouseRelationDao.listRuleIds(param);
    }
}
