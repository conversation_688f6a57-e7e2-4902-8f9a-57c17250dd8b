package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.MerchantDeliveryStepFeeEntity;
import com.cosfo.ordercenter.domain.delivery.param.QueryDeliveryStepFeeParam;
import com.cosfo.ordercenter.domain.delivery.repository.MerchantDeliveryStepFeeQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.delivery.MerchantDeliveryStepFeeConverter;
import com.cosfo.ordercenter.infrastructure.dao.MerchantDeliveryStepFeeDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MerchantDeliveryStepFeeQueryRepositoryImpl implements MerchantDeliveryStepFeeQueryRepository {

    @Resource
    private MerchantDeliveryStepFeeDao merchantDeliveryStepFeeDao;

    @Override
    public List<MerchantDeliveryStepFeeEntity> listByParam(QueryDeliveryStepFeeParam param) {
        return MerchantDeliveryStepFeeConverter.convertToEntityList(merchantDeliveryStepFeeDao.listByParam(param));
    }

    @Override
    public List<MerchantDeliveryStepFeeEntity> listStepDesc(QueryDeliveryStepFeeParam param) {
        return MerchantDeliveryStepFeeConverter.convertToEntityList(merchantDeliveryStepFeeDao.listStepDesc(param));
    }
}
