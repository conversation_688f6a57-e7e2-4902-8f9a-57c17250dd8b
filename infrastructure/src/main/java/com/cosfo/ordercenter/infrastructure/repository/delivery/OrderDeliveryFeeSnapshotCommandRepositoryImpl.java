package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;
import com.cosfo.ordercenter.domain.delivery.repository.OrderDeliveryFeeSnapshotCommandRepository;
import com.cosfo.ordercenter.infrastructure.converter.delivery.OrderDeliveryFeeSnapshotConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderDeliveryFeeSnapshotDao;
import com.cosfo.ordercenter.infrastructure.model.delivery.OrderDeliveryFeeSnapshot;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class OrderDeliveryFeeSnapshotCommandRepositoryImpl implements OrderDeliveryFeeSnapshotCommandRepository {

    @Resource
    private OrderDeliveryFeeSnapshotDao orderDeliveryFeeSnapshotDao;
    @Override
    public boolean updateEffectiveFlag(Long orderId, Integer scence) {
        return orderDeliveryFeeSnapshotDao.updateEffectiveFlag(orderId, scence);
    }

    @Override
    public boolean save(OrderDeliveryFeeSnapshotEntity snapshotEntity) {
        OrderDeliveryFeeSnapshot orderDeliveryFeeSnapshot = OrderDeliveryFeeSnapshotConverter.convertToSnapshot(snapshotEntity);
        return orderDeliveryFeeSnapshotDao.save(orderDeliveryFeeSnapshot);
    }
}
