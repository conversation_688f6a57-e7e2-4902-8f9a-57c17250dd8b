package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.TenantDeliveryFeeAreaEntity;
import com.cosfo.ordercenter.domain.delivery.repository.TenantDeliveryFeeAreaQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.delivery.TenantDeliveryFeeAreaConverter;
import com.cosfo.ordercenter.infrastructure.dao.TenantDeliveryFeeAreaDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class TenantDeliveryFeeAreaQueryRepositoryImpl implements TenantDeliveryFeeAreaQueryRepository {

    @Resource
    private TenantDeliveryFeeAreaDao tenantDeliveryFeeAreaDao;

    @Override
    public TenantDeliveryFeeAreaEntity queryAreaRule(Long tenantId, String province, String city, String area) {
        return TenantDeliveryFeeAreaConverter.toTenantDeliveryFeeAreaEntity(tenantDeliveryFeeAreaDao.queryAreaRule(tenantId, province, city, area));
    }
}
