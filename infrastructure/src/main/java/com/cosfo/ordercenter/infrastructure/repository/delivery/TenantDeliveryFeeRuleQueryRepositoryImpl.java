package com.cosfo.ordercenter.infrastructure.repository.delivery;

import com.cosfo.ordercenter.domain.delivery.entity.TenantDeliveryFeeRuleEntity;
import com.cosfo.ordercenter.domain.delivery.repository.TenantDeliveryFeeRuleQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.delivery.TenantDeliveryFeeRuleConverter;
import com.cosfo.ordercenter.infrastructure.dao.TenantDeliveryFeeRuleDao;
import com.cosfo.ordercenter.infrastructure.model.delivery.TenantDeliveryFeeRule;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class TenantDeliveryFeeRuleQueryRepositoryImpl implements TenantDeliveryFeeRuleQueryRepository {
    @Resource
    private TenantDeliveryFeeRuleDao tenantDeliveryFeeRuleDao;
    @Override
    public TenantDeliveryFeeRuleEntity selectByTenantId(Long tenantId) {
        TenantDeliveryFeeRule tenantDeliveryFeeRule = tenantDeliveryFeeRuleDao.selectByTenantId(tenantId);
        return TenantDeliveryFeeRuleConverter.toTenantDeliveryFeeRuleEntity(tenantDeliveryFeeRule);
    }
}
