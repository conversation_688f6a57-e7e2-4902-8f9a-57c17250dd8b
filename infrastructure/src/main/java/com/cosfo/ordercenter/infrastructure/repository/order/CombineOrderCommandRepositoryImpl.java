package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.repository.CombineOrderCommandRepository;
import com.cosfo.ordercenter.infrastructure.dao.CombineOrderDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class CombineOrderCommandRepositoryImpl implements CombineOrderCommandRepository {

    @Resource
    private CombineOrderDao combineOrderDao;
    @Override
    public Long add(Long combineItemId, Long tenantId) {
        return combineOrderDao.add(combineItemId, tenantId);
    }
}
