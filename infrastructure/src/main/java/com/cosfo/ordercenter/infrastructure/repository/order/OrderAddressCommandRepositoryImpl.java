package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.param.command.OrderAddressCommandParam;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressCommandRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderAddressConverter;
import com.cosfo.ordercenter.infrastructure.mapper.OrderAddressMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAddressCommandRepositoryImpl implements OrderAddressCommandRepository {

    @Resource
    private OrderAddressMapper orderAddressMapper;
    @Override
    public Long add(OrderAddressCommandParam orderAddressCommandParam) {
        OrderAddress orderAddress = OrderAddressConverter.convertToOrderAddress(orderAddressCommandParam);
        orderAddressMapper.insert(orderAddress);
        return orderAddress.getId();
    }
}
