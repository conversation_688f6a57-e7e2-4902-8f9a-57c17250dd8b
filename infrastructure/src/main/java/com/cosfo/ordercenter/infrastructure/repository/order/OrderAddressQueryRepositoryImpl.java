package com.cosfo.ordercenter.infrastructure.repository.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.ordercenter.domain.order.entity.OrderAddressEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderAddressQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderAddressConverter;
import com.cosfo.ordercenter.infrastructure.mapper.OrderAddressMapper;
import com.cosfo.ordercenter.infrastructure.model.order.OrderAddress;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class OrderAddressQueryRepositoryImpl implements OrderAddressQueryRepository {

    @Resource
    private OrderAddressMapper orderAddressMapper;

    @Override
    public List<OrderAddressEntity> queryByOrderIds(List<Long> orderIds, Long tenantId) {
        LambdaQueryWrapper<OrderAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), OrderAddress::getTenantId, tenantId);
        queryWrapper.in(!CollectionUtils.isEmpty(orderIds), OrderAddress::getOrderId, orderIds);
        return OrderAddressConverter.convertToOrderAddressEntityList(orderAddressMapper.selectList(queryWrapper));
    }

    @Override
    public OrderAddressEntity getByOrderId(Long orderId, Long tenantId) {
        LambdaQueryWrapper<OrderAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), OrderAddress::getTenantId, tenantId);
        queryWrapper.eq(Objects.nonNull(orderId), OrderAddress::getOrderId, orderId);
        return OrderAddressConverter.toOrderAddressEntity(orderAddressMapper.selectOne(queryWrapper));
    }
}
