package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.param.command.OrderCombineSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderCombineSnapshotCommandRepository;
import com.cosfo.ordercenter.infrastructure.dao.OrderCombineSnapshotDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderCombineSnapshotCommandRepositoryImpl implements OrderCombineSnapshotCommandRepository {
    @Resource
    private OrderCombineSnapshotDao orderCombineSnapshotDao;

    @Override
    public boolean batchSave(List<OrderCombineSnapshotAddParam> snapshotList) {
        return orderCombineSnapshotDao.batchSave(snapshotList);
    }
}
