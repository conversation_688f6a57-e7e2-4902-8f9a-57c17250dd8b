package com.cosfo.ordercenter.infrastructure.repository.order;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cosfo.ordercenter.client.common.FulfillmentTypeEnum;
import com.cosfo.ordercenter.common.enums.OrderStatusEnum;
import com.cosfo.ordercenter.common.enums.WarehouseTypeEnum;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.repository.OrderCommandRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderMapper;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class OrderCommandRepositoryImpl implements OrderCommandRepository {

    /**
     * 需要更新订单完成时间状态
     */
    private final List<Integer> ORDER_FINISH_STATUS = Arrays.asList(OrderStatusEnum.FINISHED.getCode(), OrderStatusEnum.CLOSED.getCode(), OrderStatusEnum.REFUNDED.getCode());



    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderDao orderDao;

    @Override
    public Integer batchUpdateStatus(OrderStatusBatchUpdateParam orderStatusBatchUpdateParam) {
        if(CollectionUtils.isEmpty(orderStatusBatchUpdateParam.getOrderIds()) && CollectionUtils.isEmpty(orderStatusBatchUpdateParam.getOrderNos())){
            throw new BizException("订单id或订单编号参数缺失");
        }
        return orderMapper.batchUpdateStatus(orderStatusBatchUpdateParam);
    }

    @Override
    public Boolean updateById(OrderCommandParam orderCommandParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, orderCommandParam.getId());
        updateWrapper.set(orderCommandParam.getFinishedTime() != null, Order::getFinishedTime, orderCommandParam.getFinishedTime());
        updateWrapper.set(orderCommandParam.getPayType() != null, Order::getPayType, orderCommandParam.getPayType());
        updateWrapper.set(orderCommandParam.getOnlinePayChannel() != null, Order::getOnlinePayChannel, orderCommandParam.getOnlinePayChannel());
        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean updateStatus(OrderStatusUpdateParam updateParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, updateParam.getOrderId());
        updateWrapper.eq(updateParam.getOriginStatus() != null, Order::getStatus, updateParam.getOriginStatus());
        updateWrapper.eq(updateParam.getTenantId() != null, Order::getTenantId, updateParam.getTenantId());

        updateWrapper.set(Order::getStatus, updateParam.getStatus());

        if (ORDER_FINISH_STATUS.contains(updateParam.getStatus())) {
            updateWrapper.setSql("finished_time = IFNULL(finished_time,now())");
        }

        if (OrderStatusEnum.DELIVERING.getCode().equals(updateParam.getStatus())) {
            updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
        }
        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateParam.getStatus())) {
            updateWrapper.set(Order::getBeginDeliveryTime, LocalDateTime.now());
            if (updateParam.getDeliveryTime() != null) {
                updateWrapper.set(Order::getDeliveryTime, updateParam.getDeliveryTime());
            }
        }
        if (OrderStatusEnum.WAIT_DELIVERY.getCode().equals(updateParam.getStatus())
                || OrderStatusEnum.WAIT_AUDIT.getCode().equals(updateParam.getStatus())
                || OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateParam.getStatus())) {
            updateWrapper.set(Order::getPayTime, LocalDateTime.now());
            updateWrapper.setSql("total_price = payable_price");
        }
        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean updateStatusByOriginStatus(OrderStatusUpdateCommandParam updateParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, updateParam.getOrderId());
        updateWrapper.in(!CollectionUtils.isEmpty(updateParam.getOriginStatusList()), Order::getStatus, updateParam.getOriginStatusList());
        updateWrapper.eq(updateParam.getTenantId() != null, Order::getTenantId, updateParam.getTenantId());

        updateWrapper.set(Order::getStatus, updateParam.getStatus());

        if (OrderStatusEnum.WAITING_DELIVERY.getCode().equals(updateParam.getStatus())) {
            updateWrapper.set(Order::getBeginDeliveryTime, LocalDateTime.now());
        }

        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean updatePayType(OrderCommandParam orderCommandParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, orderCommandParam.getId());
        updateWrapper.set(Order::getPayType, orderCommandParam.getPayType());
        updateWrapper.set(Order::getOnlinePayChannel, orderCommandParam.getOnlinePayChannel());
        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean selfLifting(OrderSelfLiftParam selfLiftParam) {
        Order order = orderMapper.selectById(selfLiftParam.getOrderId());
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, order.getId());
        updateWrapper.set(Order::getStatus, OrderStatusEnum.DELIVERING.getCode());
        // 三方仓订单 城配履约 自提更新为已完成，快递履约是待收货
        if (order.getWarehouseType() != 2 && FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(order.getFulfillmentType())) {
            updateWrapper.set(Order::getStatus, OrderStatusEnum.FINISHED.getCode());
            updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
            updateWrapper.set(Order::getFinishedTime, LocalDateTime.now());
        }
        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Boolean batchUpdateProfitSharingFinishTime(List<OrderCommandParam> orderList) {
        return orderDao.batchUpdateProfitSharingFinishTime(orderList);
    }


    @Override
    public int orderFinish(List<Long> orderIds) {
        return orderMapper.batchUpdateStatusByOrderIds(orderIds, Collections.singletonList(OrderStatusEnum.DELIVERING.getCode()), OrderStatusEnum.FINISHED.getCode());
    }

    @Override
    public boolean updateDeliveryTime(OrderDeliveryUpdateParam param) {
        if(param.getDeliveryTime() == null && param.getFulfillmentNo() == null){
            return true;
        }
        Order order = orderMapper.selectById(param.getOrderId());
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, param.getOrderId());
        updateWrapper.in(Order::getStatus, param.getOriginStatusList());
        // 只有三方仓订单需要更新配送时间
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(order.getWarehouseType())) {
            updateWrapper.set(param.getDeliveryTime() != null, Order::getDeliveryTime, param.getDeliveryTime());
        }
        updateWrapper.set(param.getFulfillmentNo() != null, Order::getFulfillmentNo, param.getFulfillmentNo());
        return orderMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime) {
        return orderMapper.updateOrderDeliveryTime(orderNos, deliveryTime);
    }

    @Override
    public boolean selfLiftingFinish(OrderSelfLiftingFinishParam selfLiftingFinishParam) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, selfLiftingFinishParam.getOrderId());

        updateWrapper.set(Order::getStatus, OrderStatusEnum.FINISHED.getCode());
        updateWrapper.set(Order::getDeliveryTime, LocalDateTime.now());
        updateWrapper.setSql("finished_time = IFNULL(finished_time,now())");
        return orderMapper.update(null, updateWrapper) > 0;
    }



    @Override
    public OrderEntity updateDeliveryFee(Long orderId, BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee) {
        Order order = orderMapper.selectById(orderId);
        if (Objects.isNull(order)) {
            throw new BizException("订单不存在！");
        }
        if (!OrderStatusEnum.NO_PAYMENT.getCode().equals(order.getStatus())){
            throw new BizException("该笔订单状态异常！请刷新页面");
        }
        if (order.getDeliveryFee().compareTo(oriDeliveryFee) != 0){
            throw new BizException("该笔订单运费已刷新，请刷新页面！");
        }
        BigDecimal subFee = order.getDeliveryFee().subtract(newDeliveryFee);
        order.setDeliveryFee(newDeliveryFee);
        order.setPayablePrice(order.getPayablePrice().subtract(subFee));
        order.setUpdateTime(LocalDateTime.now());
        orderMapper.updateById(order);
        return OrderConverter.convertToOrderEntity(order);
    }



    @Override
    public Boolean updateOrderCustomerOrderId(Long orderId, String newCustomerOrderId) {
        return orderDao.updateOrderCustomerOrderId(orderId, newCustomerOrderId);
    }

    @Override
    public Long save(OrderEntity orderEntity) {
        Order order = OrderConverter.convertToOrder(orderEntity);
        orderDao.save(order);
        orderEntity.setId(order.getId());
        return order.getId();
    }

    @Override
    public boolean setDeliveryDatePresaleOrder(Long orderId, LocalDateTime deliveryTime) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, orderId);
        updateWrapper.eq(Order::getStatus, OrderStatusEnum.WAIT_DELIVERY.getCode());
        
        updateWrapper.set(Order::getStatus, OrderStatusEnum.WAITING_DELIVERY.getCode());
        updateWrapper.set(Order::getBeginDeliveryTime, LocalDateTime.now());
        updateWrapper.set(Order::getDeliveryTime, deliveryTime);

        return orderMapper.update(null, updateWrapper) > 0;
    }
}
