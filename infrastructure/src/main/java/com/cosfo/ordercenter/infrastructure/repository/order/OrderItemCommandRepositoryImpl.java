package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemCommandParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusBatchUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemStatusUpdateParam;
import com.cosfo.ordercenter.domain.order.param.command.OrderItemUpdateParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemCommandRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderItemConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemDao;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemCommandRepositoryImpl implements OrderItemCommandRepository {

    @Resource
    private OrderItemDao orderItemDao;

    @Override
    public int updateDeliveryQuantity(Long orderItemId, Integer quantity) {
        return orderItemDao.updateDeliveryQuantity(orderItemId, quantity);
    }

    @Override
    public Boolean updateAfterSaleExpiryTime(OrderItemUpdateParam dto) {
        return orderItemDao.updateAfterSaleExpiryTime(dto);
    }

    @Override
    public Boolean batchUpdateAfterSaleExpiryTime(List<OrderItemCommandParam> orderItemList) {
        List<OrderItem> orderItems = OrderItemConverter.convertToOrderItemList(orderItemList);
        return orderItemDao.batchUpdateAfterSaleExpiryTime(orderItems);
    }

    @Override
    public List<OrderItemEntity> batchSave(List<OrderItemCommandParam> orderItemList) {
        List<OrderItem> orderItems = OrderItemConverter.convertToOrderItemList(orderItemList);

        boolean flag = orderItemDao.batchSave(orderItems);
        if(!flag){
            throw new BizException("OrderItem批量保存异常");
        }
        return OrderItemConverter.convertToEntityList(orderItems);
    }

    @Override
    public Boolean updateStatus(OrderItemStatusUpdateParam req) {
        return orderItemDao.updateStatus(req);
    }

    @Override
    public boolean batchUpdateStatus(OrderItemStatusBatchUpdateParam updateParam) {
        return orderItemDao.batchUpdateStatus(updateParam);
    }

    @Override
    public boolean updateStoreNo(Long orderId, Integer sourceStoreNo, Integer storeNo) {
        return orderItemDao.updateStoreNo(orderId, sourceStoreNo, storeNo);
    }
}
