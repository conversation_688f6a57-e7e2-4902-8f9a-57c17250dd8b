package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemExtraAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemExtraCommandRepository;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemExtraDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemExtraCommandRepositoryImpl implements OrderItemExtraCommandRepository {

    @Resource
    private OrderItemExtraDao orderItemExtraDao;

    @Override
    public void saveOrderItemExtra(OrderItemExtraAddParam orderItemExtra) {
        orderItemExtraDao.saveOrderItemExtra(orderItemExtra);
    }

    @Override
    public boolean batchSave(List<OrderItemExtraAddParam> orderItemExtraList) {
        return orderItemExtraDao.batchSave(orderItemExtraList);
    }
}
