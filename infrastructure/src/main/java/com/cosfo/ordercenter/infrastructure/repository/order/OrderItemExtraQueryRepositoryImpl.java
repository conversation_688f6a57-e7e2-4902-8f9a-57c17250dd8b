package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemExtraEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemExtraQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderItemExtraConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemExtraDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemExtraQueryRepositoryImpl implements OrderItemExtraQueryRepository {

    @Resource
    private OrderItemExtraDao orderItemExtraDao;

    @Override
    public List<OrderItemExtraEntity> queryList(OrderItemExtraQueryParam param) {
        return OrderItemExtraConverter.convertToEntityList(orderItemExtraDao.queryList(param));
    }
}
