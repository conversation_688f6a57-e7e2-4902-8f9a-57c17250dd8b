package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderItemConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemDao;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemQueryRepositoryImpl implements OrderItemQueryRepository {

    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @Override
    public List<OrderItemWithSnapshotEntity> batchQueryOrderItemDetail(List<Long> orderIds, List<Long> supplierIds) {
        return orderItemDao.batchQueryOrderItemDetail(orderIds, supplierIds);
    }

    @Override
    public Integer querySkuQuantity(Long tenantId, List<Long> orderIds) {
        return orderItemDao.querySkuQuantity(tenantId, orderIds);
    }

    @Override
    public Integer querySaleQuantity(Long tenantId, List<Long> orderIds) {
        return orderItemDao.querySaleQuantity(tenantId, orderIds);
    }

    @Override
    public List<OrderItemEntity> queryByOrderId(Long orderId) {
        return OrderItemConverter.convertToEntityList(orderItemDao.queryByOrderId(orderId));
    }

    @Override
    public List<OrderItemEntity> batchQueryByOrderIds(List<Long> orderIds) {
        return OrderItemConverter.convertToEntityList(orderItemDao.batchQueryByOrderIds(orderIds));
    }

    @Override
    public List<OrderItemEntity> batchQuery(Long tenantId, List<Long> orderItemIds) {
        return OrderItemConverter.convertToEntityList(orderItemDao.batchQuery(tenantId, orderItemIds));
    }

    @Override
    public List<OrderItemEntity> queryByIds(List<Long> orderItemIds) {
        return OrderItemConverter.convertToEntityList(orderItemDao.queryByIds(orderItemIds));
    }

    @Override
    public List<OrderItemWithSnapshotEntity> queryOrderItemVOByOrderIds(Long tenantId, List<Long> orderIds) {
        return orderItemDao.queryOrderItemVOByOrderIds(tenantId, orderIds);
    }

    @Override
    public List<OrderItemWithSnapshotEntity> queryItemWithSnapshotByOrderId(Long orderId) {
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(orderId);
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        List<OrderItemSnapshot> snapshotList = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshot> snapshotMap = snapshotList.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, snapshot -> snapshot));
        return OrderItemConverter.convertToOrderItemWithSnapshotEntityList(orderItems, snapshotMap);
    }

    @Override
    public OrderItemEntity queryById(Long orderItemId) {
        return OrderItemConverter.toOrderItemEntity(orderItemDao.getById(orderItemId));
    }
}
