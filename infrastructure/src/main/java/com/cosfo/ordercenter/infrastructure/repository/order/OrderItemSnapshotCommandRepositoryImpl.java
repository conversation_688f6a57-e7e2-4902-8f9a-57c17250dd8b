package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.param.command.OrderItemSnapshotAddParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotCommandRepository;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemSnapshotDao;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemSnapshotCommandRepositoryImpl implements OrderItemSnapshotCommandRepository {

    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Override
    public Boolean batchUpdateTaskId(List<Long> orderItemIds, Long orderId) {
        return orderItemSnapshotDao.batchUpdateTaskId(orderItemIds, orderId);
    }

    @Override
    public boolean batchSave(List<OrderItemSnapshotAddParam> snapshotList) {
        return orderItemSnapshotDao.batchSave(snapshotList);
    }
}
