package com.cosfo.ordercenter.infrastructure.repository.order;

import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.query.OrderItemSnapshotQueryParam;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderItemSnapshotConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemSnapshotQueryRepositoryImpl implements OrderItemSnapshotQueryRepository {

    @Resource
    private OrderItemSnapshotDao orderitemsnapshotDao;

    @Override
    public List<OrderItemSnapshotEntity> queryList(OrderItemSnapshotQueryParam queryReq) {
        return OrderItemSnapshotConverter.convertToSnapshotEntityList(orderitemsnapshotDao.queryList(queryReq));
    }

    @Override
    public List<OrderItemSnapshotEntity> queryByOrderItemIds(List<Long> orderItemIds) {
        return OrderItemSnapshotConverter.convertToSnapshotEntityList(orderitemsnapshotDao.queryByOrderItemIds(orderItemIds));
    }

    @Override
    public List<OrderItemSnapshotEntity> queryByOrderIds(List<Long> orderIds) {
        return OrderItemSnapshotConverter.convertToSnapshotEntityList(orderitemsnapshotDao.queryByOrderIds(orderIds));
    }

    @Override
    public OrderItemSnapshotEntity queryByOrderItemId(Long orderItemId) {
        return OrderItemSnapshotConverter.toOrderItemSnapshotEntity(orderitemsnapshotDao.queryByOrderItemId(orderItemId));
    }

    @Override
    public Map<Long, OrderItemSnapshotEntity> queryOrderItemSnapshot(List<Long> orderItemIds) {
        List<OrderItemSnapshot> orderItemSnapshots = orderitemsnapshotDao.queryByOrderItemIds(orderItemIds);
        List<OrderItemSnapshotEntity> orderItemSnapshotEntities = OrderItemSnapshotConverter.convertToSnapshotEntityList(orderItemSnapshots);
        return orderItemSnapshotEntities.stream().collect(Collectors.toMap(OrderItemSnapshotEntity::getOrderItemId, item -> item));
    }
}
