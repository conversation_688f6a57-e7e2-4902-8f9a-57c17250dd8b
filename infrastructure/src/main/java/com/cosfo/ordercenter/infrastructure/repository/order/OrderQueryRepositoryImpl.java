package com.cosfo.ordercenter.infrastructure.repository.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.common.util.DateUtil;
import com.cosfo.ordercenter.common.enums.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.common.enums.OrderStatusEnum;
import com.cosfo.ordercenter.common.enums.WarehouseTypeEnum;
import com.cosfo.ordercenter.domain.order.entity.*;
import com.cosfo.ordercenter.domain.order.param.query.*;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.infrastructure.converter.order.OrderConverter;
import com.cosfo.ordercenter.infrastructure.dao.OrderDao;
import com.cosfo.ordercenter.infrastructure.mapper.OrderMapper;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItem;
import com.cosfo.ordercenter.infrastructure.model.order.OrderItemSnapshot;
import com.github.pagehelper.PageInfo;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderQueryRepositoryImpl implements OrderQueryRepository {

    /**
     * 需要计算限购数量的售后状态
     */
    private final List<Integer> SALE_LIMIT_AFTER_SALE_STATUS = Arrays.asList(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
            OrderAfterSaleStatusEnum.REFUNDING.getValue(),
            OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
            OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());

    /**
     * 需要计算限购数量的订单状态
     */
    private final List<Integer> SALE_LIMIT_ORDER_STATUS = Arrays.asList(OrderStatusEnum.NO_PAYMENT.getCode(),
            OrderStatusEnum.WAIT_DELIVERY.getCode(),
            OrderStatusEnum.DELIVERING.getCode(),
            OrderStatusEnum.FINISHED.getCode(),
            OrderStatusEnum.WAITING_DELIVERY.getCode(),
            OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode(),
            OrderStatusEnum.OUT_OF_STORAGE.getCode(),
            OrderStatusEnum.WAIT_AUDIT.getCode());

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderDao orderDao;

    @Override
    public BigDecimal sumOrderTotalPrice(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return orderMapper.sumOrderTotalPrice(start, end, tenantId, storeIds);
    }

    @Override
    public Integer countPayOrderQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return orderMapper.countPayOrderQuantity(start, end, tenantId, storeIds);
    }

    @Override
    public Integer countPayOrderStoreQuantity(LocalDateTime start, LocalDateTime end, Long tenantId, List<Long> storeIds) {
        return orderMapper.countPayOrderStoreQuantity(start, end, tenantId, storeIds);
    }

    @Override
    public Integer getWaitDeliveryNum(Long tenantId) {
        return orderMapper.countWaitDeliveryQuantity(tenantId);
    }

    @Override
    public Integer countByStatusList(Long tenantId, List<Integer> statusList) {
        return orderMapper.countByStatusList(tenantId, statusList);
    }

    @Override
    public List<OrderSkuQuantityEntity> querySkuSaleQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderMapper.querySkuSaleQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderSkuQuantityEntity> querySkuSaleWithStoreNoQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderMapper.querySkuSaleWithStoreNoQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderSkuQuantityEntity> querySkuSaleWithCityQuantity(List<Long> skuIds, Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderMapper.querySkuSaleWithCityQuantity(skuIds, tenantId, startTime, endTime);
    }

    @Override
    public List<OrderDetailEntity> queryOrderDetail(List<Long> orderIds, Long tenantId) {
        return orderMapper.queryOrderDetail(orderIds, tenantId);
    }

    @Override
    public PageInfo<OrderEntity> queryPage(OrderOmsQueryParam orderOmsQueryParam) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(orderOmsQueryParam);
        orderLambdaQueryWrapper.orderByDesc(Order::getId, Order::getCombineOrderId);
        return OrderConverter.convertToPageInfo(orderMapper.selectPage(new Page<>(orderOmsQueryParam.getPageNum(), orderOmsQueryParam.getPageSize()), orderLambdaQueryWrapper));
    }

    @Override
    public PageInfo<OrderEntity> queryPage(OrderQueryParam orderQueryParam) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(orderQueryParam);
        if(orderQueryParam.getSortOrderIdAsc() != null && orderQueryParam.getSortOrderIdAsc()){
            orderLambdaQueryWrapper.orderByAsc(Order::getId, Order::getCombineOrderId);
        } else {
            orderLambdaQueryWrapper.orderByDesc(Order::getId, Order::getCombineOrderId);
        }
        return OrderConverter.convertToPageInfo(orderMapper.selectPage(new Page<>(orderQueryParam.getPageNum(), orderQueryParam.getPageSize()), orderLambdaQueryWrapper));
    }


    private static MPJLambdaWrapper<Order> getOrderLambdaQueryWrapper(OrderQueryParam queryParam) {
        if (queryParam.getDeliveryTime() != null) {
            queryParam.setDeliveryStartTime(DateUtil.startOfDay(queryParam.getDeliveryTime()));
            queryParam.setDeliveryEndTime(DateUtil.endOfDay(queryParam.getDeliveryTime()));
        }
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(queryParam.getTenantId() != null, Order::getTenantId, queryParam.getTenantId());
        queryWrapper.eq(queryParam.getStatus() != null, Order::getStatus, queryParam.getStatus());
        queryWrapper.eq(queryParam.getWarehouseType() != null, Order::getWarehouseType, queryParam.getWarehouseType());
        queryWrapper.eq(queryParam.getOrderNo() != null, Order::getOrderNo, queryParam.getOrderNo());
        queryWrapper.eq(queryParam.getWarehouseNo() != null, Order::getWarehouseNo, queryParam.getWarehouseNo());
        queryWrapper.eq(queryParam.getPayType() != null, Order::getPayType, queryParam.getPayType());
        queryWrapper.eq(queryParam.getOrderId() != null, Order::getId, queryParam.getOrderId());
        queryWrapper.eq(queryParam.getOrderSource() != null, Order::getOrderSource, queryParam.getOrderSource());
        queryWrapper.eq(queryParam.getOrderType() != null, Order::getOrderType, queryParam.getOrderType());
        queryWrapper.eq(StringUtils.isNotBlank(queryParam.getPlanOrderNo()), Order::getPlanOrderNo, queryParam.getPlanOrderNo());
        queryWrapper.between(queryParam.getDeliveryStartTime() != null && queryParam.getDeliveryEndTime() != null, Order::getDeliveryTime, queryParam.getDeliveryStartTime(), queryParam.getDeliveryEndTime());
        queryWrapper.between(queryParam.getCreateStartTime() != null && queryParam.getCreateEndTime() != null, Order::getCreateTime, queryParam.getCreateStartTime(), queryParam.getCreateEndTime());
        queryWrapper.between(queryParam.getStartTime() != null && queryParam.getEndTime() != null, Order::getFinishedTime, queryParam.getStartTime(), queryParam.getEndTime());
        queryWrapper.ne(queryParam.getNeOrderId() != null, Order::getId, queryParam.getNeOrderId());
        queryWrapper.eq(queryParam.getSupplierTenantId() != null, Order::getSupplierTenantId, queryParam.getSupplierTenantId());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getStoreIds()), Order::getStoreId, queryParam.getStoreIds());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getStatusList()), Order::getStatus, queryParam.getStatusList());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getAccountIds()), Order::getAccountId, queryParam.getAccountIds());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getTenantIds()), Order::getTenantId, queryParam.getTenantIds());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getOrderNos()), Order::getOrderNo, queryParam.getOrderNos());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getCustomerOrderIds()), Order::getCustomerOrderId, queryParam.getCustomerOrderIds());

        if(queryParam.getTimeQueryType() != null && queryParam.getQueryStartTime() != null && queryParam.getQueryEndTime() != null) {
            if(queryParam.getTimeQueryType() == 1){
                queryWrapper.between(Order::getCreateTime, queryParam.getQueryStartTime(), queryParam.getQueryEndTime());
            }else if(queryParam.getTimeQueryType() == 2){
                queryWrapper.between(Order::getPayTime, queryParam.getQueryStartTime(), queryParam.getQueryEndTime());
            }else if(queryParam.getTimeQueryType() == 3){
                queryWrapper.between(Order::getDeliveryTime, queryParam.getQueryStartTime(), queryParam.getQueryEndTime());
            }else if(queryParam.getTimeQueryType() == 4){
                queryWrapper.between(Order::getFinishedTime, queryParam.getQueryStartTime(), queryParam.getQueryEndTime());
            }
        }

        boolean orderItemJoinFlag = false;
        boolean orderItemSnapshotJoinFlag = false;
        if (!CollectionUtils.isEmpty(queryParam.getOrderItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getId, queryParam.getOrderItemIds());
        }

        if (!CollectionUtils.isEmpty(queryParam.getItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getItemId, queryParam.getItemIds());
        }

        if (!CollectionUtils.isEmpty(queryParam.getSupplierTenantIds()) || queryParam.getSkuId() != null) {
            orderItemSnapshotJoinFlag = true;
            orderItemJoinFlag = true;
            queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getSupplierTenantIds()), OrderItemSnapshot::getSupplierTenantId, queryParam.getSupplierTenantIds());
            queryWrapper.eq(queryParam.getSkuId() != null, OrderItemSnapshot::getSkuId, queryParam.getSkuId());
        }

        if (orderItemJoinFlag) {
            queryWrapper.leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId);
        }

        if (orderItemSnapshotJoinFlag) {
            queryWrapper.leftJoin(OrderItemSnapshot.class, OrderItemSnapshot::getOrderItemId, OrderItem::getId);
        }
        queryWrapper.groupBy(Order::getId);
        return queryWrapper;
    }

    private static MPJLambdaWrapper<Order> getOrderLambdaQueryWrapper(OrderOmsQueryParam orderQueryReq) {
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(orderQueryReq.getTenantId() != null, Order::getTenantId, orderQueryReq.getTenantId());
        queryWrapper.eq(orderQueryReq.getStatus() != null, Order::getStatus, orderQueryReq.getStatus());
        queryWrapper.eq(orderQueryReq.getWarehouseType() != null, Order::getWarehouseType, orderQueryReq.getWarehouseType());
        queryWrapper.eq(orderQueryReq.getOrderNo() != null, Order::getOrderNo, orderQueryReq.getOrderNo());
        queryWrapper.eq(orderQueryReq.getWarehouseNo() != null, Order::getWarehouseNo, orderQueryReq.getWarehouseNo());
        queryWrapper.eq(orderQueryReq.getPayType() != null, Order::getPayType, orderQueryReq.getPayType());
        queryWrapper.eq(orderQueryReq.getSupplierTenantId() != null, Order::getSupplierTenantId, orderQueryReq.getSupplierTenantId());
        queryWrapper.between(orderQueryReq.getCreateStartTime() != null && orderQueryReq.getCreateEndTime() != null, Order::getCreateTime, orderQueryReq.getCreateStartTime(), orderQueryReq.getCreateEndTime());
        queryWrapper.between(orderQueryReq.getStartTime() != null && orderQueryReq.getEndTime() != null, Order::getFinishedTime, orderQueryReq.getStartTime(), orderQueryReq.getEndTime());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStoreIds()), Order::getStoreId, orderQueryReq.getStoreIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getStatusList()), Order::getStatus, orderQueryReq.getStatusList());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getAccountIds()), Order::getAccountId, orderQueryReq.getAccountIds());
        queryWrapper.in(!CollectionUtils.isEmpty(orderQueryReq.getTenantIds()), Order::getTenantId, orderQueryReq.getTenantIds());
        queryWrapper.eq(orderQueryReq.getCustomerOrderId() != null, Order::getCustomerOrderId, orderQueryReq.getCustomerOrderId());
        boolean orderItemJoinFlag = false;
        if (!CollectionUtils.isEmpty(orderQueryReq.getOrderItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getId, orderQueryReq.getOrderItemIds());
        }

        if (!CollectionUtils.isEmpty(orderQueryReq.getItemIds())) {
            orderItemJoinFlag = true;
            queryWrapper.in(OrderItem::getItemId, orderQueryReq.getItemIds());
        }
        if (orderItemJoinFlag) {
            queryWrapper.leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId);
        }
        if (orderItemJoinFlag) {
            queryWrapper.groupBy(Order::getId);
        }
        return queryWrapper;

    }

    @Override
    public List<OrderEntity> queryList(OrderQueryParam queryParam) {
        MPJLambdaWrapper<Order> orderLambdaQueryWrapper = getOrderLambdaQueryWrapper(queryParam);
        orderLambdaQueryWrapper.gt(queryParam.getMaxId() != null, Order::getId, queryParam.getMaxId());
        if (Objects.nonNull(queryParam.getBatchSize())) {
            orderLambdaQueryWrapper.last("limit " + queryParam.getBatchSize());
        }
        orderLambdaQueryWrapper.orderByAsc(Order::getId);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(orderLambdaQueryWrapper));
    }

    @Override
    public List<String> queryNeedDeliveryOrder(OrderDeliveryQueryParam queryParam) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, queryParam.getTenantId());
        queryWrapper.eq(Order::getStatus, OrderStatusEnum.WAITING_DELIVERY.getCode());
        queryWrapper.eq(Order::getWarehouseType, WarehouseTypeEnum.SELF_SUPPLY.getCode());
        queryWrapper.in(!CollectionUtils.isEmpty(queryParam.getWarehouseNoList()), Order::getWarehouseNo, queryParam.getWarehouseNoList());
        queryWrapper.le(queryParam.getPayTime() != null, Order::getPayTime, queryParam.getPayTime());
        queryWrapper.le(queryParam.getCreateTime() != null, Order::getCreateTime, queryParam.getCreateTime());
        List<Order> list = orderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(Order::getOrderNo).collect(Collectors.toList());
    }

    @Override
    public List<OrderEntity> queryByOrderNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Order::getOrderNo, orderNos);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public List<OrderEntity> queryByCombineId(Long combineId, Long tenantId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, tenantId);
        queryWrapper.eq(Order::getCombineOrderId, combineId);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public List<OrderEntity> queryByCombineIds(Set<Long> combineIds, Long tenantId) {
        if (CollectionUtils.isEmpty(combineIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, tenantId);
        queryWrapper.in(Order::getCombineOrderId, combineIds);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public Integer countOrderQuantity(OrderCountQueryParam orderCountQueryParam) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getTenantId, orderCountQueryParam.getTenantId());
        queryWrapper.in(!CollectionUtils.isEmpty(orderCountQueryParam.getStatusList()), Order::getStatus, orderCountQueryParam.getStatusList());
        queryWrapper.eq(orderCountQueryParam.getStoreId() != null, Order::getStoreId, orderCountQueryParam.getStoreId());
        return orderMapper.selectCount(queryWrapper).intValue();
    }

    @Override
    public List<OrderEntity> queryNeedAutoFinishedOrder(OrderAutoFinishQueryParam autoFinishQueryParam) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Order::getId);
        queryWrapper.le(Order::getDeliveryTime, autoFinishQueryParam.getDeliveryTimeEnd());
        queryWrapper.ge(Order::getDeliveryTime, autoFinishQueryParam.getDeliveryTimeStart());
        queryWrapper.eq(Order::getStatus, OrderStatusEnum.DELIVERING.getCode());
        queryWrapper.orderByAsc(Order::getDeliveryTime);
        queryWrapper.last("limit " + autoFinishQueryParam.getLimit());
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public List<OrderEntity> queryByStoreIdAndDeliveryTime(Long storeId, LocalDateTime deliveryTime, Integer warehouseType) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getStoreId, storeId);
        queryWrapper.eq(Order::getDeliveryTime, deliveryTime);
        queryWrapper.eq(Order::getWarehouseType, warehouseType);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public List<OrderEntity> queryListByCustomerOrderIds(OrderQueryParam orderQueryParam) {
        MPJLambdaWrapper<Order> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Order.class);
        queryWrapper.eq(orderQueryParam.getTenantId() != null, Order::getTenantId, orderQueryParam.getTenantId());
        queryWrapper.in( Order::getCustomerOrderId, orderQueryParam.getCustomerOrderIds());
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }

    @Override
    public List<OrderItemSaleQuantityEntity> queryOrderItemSaleQuantity(ItemSaleQuantityQueryParam saleQuantityQueryParam) {
        return orderMapper.queryOrderItemSaleQuantity(saleQuantityQueryParam.getStartDay(),
                saleQuantityQueryParam.getEndDay(),
                saleQuantityQueryParam.getTenantId(),
                saleQuantityQueryParam.getMerchantStoreId(),
                saleQuantityQueryParam.getItemIds(),
                SALE_LIMIT_AFTER_SALE_STATUS,
                SALE_LIMIT_ORDER_STATUS);
    }

    @Override
    public List<SupplierOrderEntity> querySupplierOrderList(SupplierOrderTotalQueryParam queryParam) {
        return orderMapper.querySupplierOrderList(queryParam);
    }

    @Override
    public OrderEntity queryById(Long orderId) {
        return OrderConverter.convertToOrderEntity(orderMapper.selectById(orderId));
    }

    @Override
    public OrderEntity queryByNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo);
        queryWrapper.last("limit 1");
        Order order = orderMapper.selectOne(queryWrapper);
        return OrderConverter.convertToOrderEntity(order);
    }

    @Override
    public List<OrderEntity> queryByIds(List<Long> ids) {

        List<Order> orders = orderMapper.selectBatchIds(ids);
        return OrderConverter.convertToOrderEntityList(orders);
    }

    @Override
    public List<OrderEntity> queryByNos(List<String> nos) {
        if (CollectionUtils.isEmpty(nos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Order::getOrderNo, nos);
        return OrderConverter.convertToOrderEntityList(orderMapper.selectList(queryWrapper));
    }
}
