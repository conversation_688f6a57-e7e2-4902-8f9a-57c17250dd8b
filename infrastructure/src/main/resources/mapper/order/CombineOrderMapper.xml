<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.infrastructure.mapper.CombineOrderMapper">
    <sql id="Base_Column_List">
        id,
        tenant_id,
        combine_item_id,
        create_time,
        update_time
    </sql>
    <resultMap id="BaseResultMap" type="com.cosfo.ordercenter.infrastructure.model.order.CombineOrder">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="combine_item_id" property="combineItemId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
</mapper>
