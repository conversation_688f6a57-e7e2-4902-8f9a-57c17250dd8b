<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.infrastructure.mapper.OrderAfterSaleMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="after_sale_order_no" jdbcType="VARCHAR" property="afterSaleOrderNo" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="after_sale_type" jdbcType="BIGINT" property="afterSaleType" />
    <result column="service_type" jdbcType="BIGINT" property="serviceType" />
    <result column="apply_price" jdbcType="DECIMAL" property="applyPrice" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="delivery_fee" jdbcType="DECIMAL" property="deliveryFee" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="user_remark" jdbcType="VARCHAR" property="userRemark" />
    <result column="proof_picture" jdbcType="VARCHAR" property="proofPicture" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="handle_remark" jdbcType="VARCHAR" property="handleRemark" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="finished_time" jdbcType="TIMESTAMP" property="finishedTime" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="recycle_time" jdbcType="TIMESTAMP" property="recycleTime" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="recycle_details" jdbcType="VARCHAR" property="recycleDetails" />
    <result column="responsibility_type" jdbcType="TINYINT" property="responsibilityType" />
    <result column="warehouse_type" jdbcType="TINYINT" property="warehouseType" />
    <result column="auto_finished_time" jdbcType="TIMESTAMP" property="autoFinishedTime" />
    <result column="apply_quantity" jdbcType="INTEGER" property="applyQuantity" />
    <result column="admin_remark" jdbcType="VARCHAR" property="adminRemark" />
    <result column="admin_remark_time" jdbcType="TIMESTAMP" property="adminRemarkTime" />
    <result column="second_handle_remark" jdbcType="VARCHAR" property="secondHandleRemark" />
    <result column="return_address_id" jdbcType="BIGINT" property="returnAddressId" />
    <result column="return_warehouse_no" jdbcType="VARCHAR" property="returnWarehouseNo" />
  </resultMap>

  <sql id="Base_Column_List">
    id, tenant_id, order_id, order_item_id, store_id, account_id, after_sale_order_no,
    amount, after_sale_type, service_type, apply_price, total_price, delivery_fee, reason,
    user_remark, proof_picture, `status`, handle_remark, operator_name, create_time,
    update_time, finished_time, handle_time, recycle_time, store_no, recycle_details,
    responsibility_type, warehouse_type, auto_finished_time, apply_quantity, admin_remark,
    admin_remark_time, second_handle_remark, return_address_id, return_warehouse_no
  </sql>



  <select id="queryBillOrderAfterSaleByStartTimeAndEndTime" resultMap="BaseResultMap">
    SELECT
      a.id, a.tenant_id, a.order_id, a.order_item_id, a.store_id, a.account_id, a.after_sale_order_no,
      a.amount, a.after_sale_type, a.service_type, a.apply_price, a.total_price, a.delivery_fee, a.reason,
      a.user_remark, a.proof_picture, a.`status`, a.handle_remark, a.operator_name, a.finished_time, a.create_time,
      a.update_time,a.responsibility_type,a.store_no,a.apply_quantity
    FROM order_after_sale a
           left join `order` o on a.order_id = o.id
    where
      a.tenant_id = #{tenantId}
      and a.status = 4
      and a.finished_time between #{startTime} and #{endTime} and o.pay_type = 2
  </select>


  <select id="getRecentlyUsedReturnAddressId" resultType="java.lang.Long">
    select return_address_id
    from order_after_sale
    where
    tenant_id = #{tenantId}
    and return_address_id is not null
    order by id desc
    limit 1
  </select>

  <select id="getAllRefundDealNoByTime" resultType="java.lang.String">
    select after_sale_order_no
    from order_after_sale
    where
    `status` = 2
    and create_time <![CDATA[>=]]> #{startTime}
    and create_time <![CDATA[<=]]> #{endTime}
    and after_sale_type = 1
    and handle_time <![CDATA[<=]]> #{endTime}
  </select>


  <select id="queryOrderAfterSaleForBill" resultMap="BaseResultMap">
    SELECT
      a.id, a.tenant_id, a.order_id, a.order_item_id, a.store_id, a.account_id, a.after_sale_order_no,
      a.amount, a.after_sale_type, a.service_type, a.apply_price, a.total_price, a.delivery_fee, a.reason,
      a.user_remark, a.proof_picture, a.`status`, a.handle_remark, a.operator_name, a.finished_time, a.create_time,
      a.update_time,a.responsibility_type,a.store_no,a.apply_quantity
    FROM order_after_sale a
           left join `order` o on a.order_id = o.id
    where
      a.tenant_id = #{tenantId}
      and a.status = 4
      and a.finished_time between #{startTime} and #{endTime} and o.pay_type = 2
  </select>


  <select id="countOrderAfterSaleByOrderId" resultType="com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSaleCnt">
    select count(1) as cnt, order_id as orderId
    from order_after_sale
    where
        tenant_id = #{req.tenantId}
    <if test="req.storeId != null">
        and store_id = #{req.storeId}
    </if>
    <if test="req.statusList !=null and req.statusList.size > 0">
      and status in
    <foreach collection="req.statusList" item="status" close=")" open="(" separator=",">
      #{status}
    </foreach>
    </if>
    <if test="req.orderIds !=null and req.orderIds.size > 0">
      and order_id in
      <foreach collection="req.orderIds" item="orderId" close=")" open="(" separator=",">
        #{orderId}
      </foreach>
    </if>
    group by order_id
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-16-->
  <insert id="insertList" useGeneratedKeys="true" keyProperty="id" >
    INSERT INTO order_after_sale(
    tenant_id,
    order_id,
    order_item_id,
    store_id,
    account_id,
    after_sale_order_no,
    amount,
    after_sale_type,
    service_type,
    apply_price,
    total_price,
    delivery_fee,
    reason,
    user_remark,
    proof_picture,
    `status`,
    handle_remark,
    operator_name,
    update_time,
    finished_time,
    handle_time,
    recycle_time,
    recycle_details,
    store_no,
    warehouse_type,
    auto_finished_time,
    apply_quantity,
    admin_remark,
    admin_remark_time,
    second_handle_remark,
    return_address_id,
    return_warehouse_no,
    customer_after_sale_order_no
    )VALUES
    <foreach collection="list" item="element" index="index" separator=",">
      (
      #{element.tenantId,jdbcType=BIGINT},
      #{element.orderId,jdbcType=BIGINT},
      #{element.orderItemId,jdbcType=BIGINT},
      #{element.storeId,jdbcType=BIGINT},
      #{element.accountId,jdbcType=BIGINT},
      #{element.afterSaleOrderNo,jdbcType=VARCHAR},
      #{element.amount,jdbcType=INTEGER},
      #{element.afterSaleType,jdbcType=BIGINT},
      #{element.serviceType,jdbcType=BIGINT},
      #{element.applyPrice,jdbcType=DECIMAL},
      #{element.totalPrice,jdbcType=DECIMAL},
      #{element.deliveryFee,jdbcType=DECIMAL},
      #{element.reason,jdbcType=VARCHAR},
      #{element.userRemark,jdbcType=VARCHAR},
      #{element.proofPicture,jdbcType=VARCHAR},
      #{element.status,jdbcType=TINYINT},
      #{element.handleRemark,jdbcType=VARCHAR},
      #{element.operatorName,jdbcType=VARCHAR},
      #{element.updateTime,jdbcType=TIMESTAMP},
      #{element.finishedTime,jdbcType=TIMESTAMP},
      #{element.handleTime,jdbcType=TIMESTAMP},
      #{element.recycleTime,jdbcType=TIMESTAMP},
      #{element.recycleDetails,jdbcType=VARCHAR},
      #{element.storeNo,jdbcType=INTEGER},
      #{element.warehouseType,jdbcType=TINYINT},
      #{element.autoFinishedTime,jdbcType=TIMESTAMP},
      #{element.applyQuantity,jdbcType=INTEGER},
      #{element.adminRemark,jdbcType=VARCHAR},
      #{element.adminRemarkTime,jdbcType=TIMESTAMP},
      #{element.secondHandleRemark,jdbcType=VARCHAR},
      #{element.returnAddressId,jdbcType=BIGINT},
      #{element.returnWarehouseNo,jdbcType=VARCHAR},
      #{element.customerAfterSaleOrderNo,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>
