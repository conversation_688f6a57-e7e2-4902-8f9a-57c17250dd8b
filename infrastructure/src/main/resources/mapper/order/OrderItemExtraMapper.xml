<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.ordercenter.infrastructure.mapper.OrderItemExtraMapper">

    <resultMap id="BaseResultMap" type="com.cosfo.ordercenter.infrastructure.model.order.OrderItemExtra">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="orderId" column="order_id" jdbcType="BIGINT"/>
        <result property="orderItemId" column="order_item_id" jdbcType="BIGINT"/>
        <result property="customerOrderItemId" column="customer_order_item_id" jdbcType="VARCHAR"/>
        <result property="customerSkuCode" column="customer_sku_code" jdbcType="VARCHAR"/>
        <result property="customerSkuTitle" column="customer_sku_title" jdbcType="VARCHAR"/>
        <result property="customerSkuSpecification" column="customer_sku_specification" jdbcType="VARCHAR"/>
        <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,order_id,
        order_item_id,customer_order_item_id,customer_sku_code,
        customer_sku_title,customer_sku_specification,sku_code
    </sql>


    <insert id="batchSave" useGeneratedKeys="true" keyProperty="id">
        insert into order_item_extra
        (
        tenant_id,
        order_id,
        order_item_id,
        customer_order_item_id,
        customer_sku_code,
        customer_sku_title,
        customer_sku_specification,
        customer_sku_specification_unit,
        sku_code
        )
        values
        <foreach collection="itemList" item="item" separator=",">
            (
            #{item.tenantId},
            #{item.orderId},
            #{item.orderItemId},
            #{item.customerOrderItemId},
            #{item.customerSkuCode},
            #{item.customerSkuTitle},
            #{item.customerSkuSpecification},
            #{item.customerSkuSpecificationUnit},
            #{item.skuCode}
            )
        </foreach>
    </insert>

</mapper>
