<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
    </parent>

    <groupId>com.cosfo</groupId>
    <artifactId>order-center</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <modules>
        <module>application-old</module>
        <module>dao-old</module>
        <module>service-old</module>
        <module>starter</module>
        <module>application</module>
        <module>common</module>
        <module>domain</module>
        <module>infrastructure</module>
        <module>facade</module>
    </modules>

    <properties>
        <revision>2.0-SNAPSHOT</revision>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <order-center-client.version>1.3.9-RELEASE</order-center-client.version>
        <xianmu-common.version>1.1.7-RELEASE</xianmu-common.version>
        <xianmu-dubbo.version>1.0.10-RELEASE</xianmu-dubbo.version>
        <xianmu-log.version>1.0.14-RELEASE</xianmu-log.version>
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.28</mysql-connector.version>
        <druid.version>1.1.20</druid.version>
        <dubbo-registry-nacos.version>2.7.15</dubbo-registry-nacos.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <xianmu-task-support.version>1.0.5</xianmu-task-support.version>
        <hutool.version>5.7.22</hutool.version>
        <ofc-client.version>1.4.1-RELEASE</ofc-client.version>
        <cosfo-manage-client.version>1.3.3-RELEASE</cosfo-manage-client.version>
        <oms-client.version>1.0.3-RELEASE</oms-client.version>
        <usercenter-client.version>1.0.2</usercenter-client.version>
        <goodscenter-client.version>1.1.3.3-RELEASE</goodscenter-client.version>
        <marketingcenter-client.version>1.0.9-RELEASE</marketingcenter-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>order-center-client</artifactId>
                <version>${order-center-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>ofc-client</artifactId>
                <version>${ofc-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-manage-client</artifactId>
                <version>${cosfo-manage-client.version}</version>
            </dependency>

          <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>summerfarm-inventory-client</artifactId>
            <version>2.0.20-RELEASE</version>
          </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>item-center-client</artifactId>
                <version>1.0.12-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>cosfo-common</artifactId>
                <version>1.0.4</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.common</groupId>
                <artifactId>xianmu-common</artifactId>
                <version>${xianmu-common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cosfo</groupId>
                <artifactId>oms-client</artifactId>
                <version>${oms-client.version}</version>
            </dependency>

            <!--    用户中心-->
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>usercenter-client</artifactId>
                <version>${usercenter-client.version}</version>
            </dependency>

            <!--    营销中心-->
            <dependency>
                <groupId>net.xianmu</groupId>
                <artifactId>marketing-center-client</artifactId>
                <version>${marketingcenter-client.version}</version>
            </dependency>

            <dependency>
                <groupId>net.summerfarm</groupId>
                <artifactId>goods-center-client</artifactId>
                <version>${goodscenter-client.version}</version>
            </dependency>


            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-dubbo-support</artifactId>
                <version>${xianmu-dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-log-support</artifactId>
                <version>${xianmu-log.version}</version>
            </dependency>

            <dependency>
                <groupId>net.xianmu.starter</groupId>
                <artifactId>xianmu-task-support</artifactId>
                <version>${xianmu-task-support.version}</version>
            </dependency>

            <!-- 数据库组件——mysql连接组件 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <!-- alibaba开源数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <!-- 注册中心 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo-registry-nacos.version}</version>
            </dependency>

            <!--  lombok  -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>


            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.200</version>
<!--                <scope>test</scope>-->
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.3.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.3.6</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.11.3</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.3.2</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>2.3.1.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.11.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <propertyFile>archetype.properties</propertyFile>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.16</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>