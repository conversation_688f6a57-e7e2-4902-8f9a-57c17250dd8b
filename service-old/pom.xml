<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cosfo</groupId>
        <artifactId>order-center</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>order-center-service-old</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>


        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>order-center-dao-old</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>ofc-client</artifactId>
        </dependency>

        <dependency>
          <groupId>net.xianmu</groupId>
          <artifactId>summerfarm-inventory-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>cosfo-manage-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>item-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cosfo</groupId>
            <artifactId>oms-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>goods-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>marketing-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
        </dependency>

        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

    </dependencies>
</project>