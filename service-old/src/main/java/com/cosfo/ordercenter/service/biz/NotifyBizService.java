package com.cosfo.ordercenter.service.biz;

import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface NotifyBizService {

    /**
     * 发送售后异常通知
     */
    void sendAfterSaleNotifyMessage(OrderAfterSale orderAfterSale);

    /**
     * 更新配送时间短信提醒
     * @param sourceOrderList
     * @param deliveryDate
     */
    void updateOrderDeliveryTimeSmsNotify(List<Order> sourceOrderList, LocalDate deliveryDate);

}
