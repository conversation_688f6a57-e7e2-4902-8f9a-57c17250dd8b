package com.cosfo.ordercenter.service.biz;

import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.service.biz.dto.OrderAfterSaleServerProviderAuditDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleBizService {

    /**
     * 校验是否需要退运费
     * @param orderAfterSaleList
     * @return
     */
    boolean validationIsNeedRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleList);

    /**
     * 售后信息校验
     * 检验数量和金额
     * @param orderAfterSaleDTO
     * @return
     */
    void orderAfterSaleDataCheck(OrderAfterSaleDTO orderAfterSaleDTO, Order order);

    /**
     * 单个订单售后信息批量校验
     *
     * @param afterSaleDTOList
     * @param order
     */
    void orderAfterSaleDataCheckForOneOrder(List<OrderAfterSaleDTO> afterSaleDTOList, Order order, List<OrderItem> itemList);

    /**
     * 审核拒绝，更新售后单状态
     * @param req
     * @param orderAfterSale
     * @return
     */
    Boolean reviewReject(OrderAfterSaleAuditReq req, OrderAfterSale orderAfterSale);

    /**
     * 缺货创建售后单
     * @param reqs
     * @param serviceType
     */
    void createAfterSaleOrderIfNeed(List<OrderAfterSaleProcessFinishReq> reqs, Integer serviceType);


    /**
     * 创建关单售后单
     * @param needCloseOrderList
     */
    void createAfterSaleOrderForClose(List<Order> needCloseOrderList);

    /**
     * 自动审核关单售后单
     * @param needCloseOrder
     */
    void autoReviewSubmissionsForClose(List<Order> needCloseOrder);

    /**
     * 校验售后申请参数，并填充售后单信息
     * @param orderAfterSaleDTOS
     * @param order
     */
    void checkDataAndBuildDto(List<OrderAfterSaleDTO> orderAfterSaleDTOS, Order order);

    /**
     * 查询可申请售后信息
     * @param orderAfterSaleEnableApplyParam
     * @return
     */
    Map<Long, OrderAfterSaleEnableDTO> queryEnableApplyByApplyParam(OrderAfterSaleEnableApplyParam orderAfterSaleEnableApplyParam);

    /**
     * 检查是否需要服务商确认
     * @param orderAfterSaleServerProviderAuditDTO
     * @return
     */
    boolean checkNeedServiceProviderAudit(OrderAfterSaleServerProviderAuditDTO orderAfterSaleServerProviderAuditDTO);

}
