package com.cosfo.ordercenter.service.biz;

import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderItem;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OrderItemInnerService {

    /**
     * 批量更新订单明细最后可售后时间
     * @param order
     * @param orderItems
     * @param ruleMap
     * @return
     */
    boolean batchUpdateExpiryTime(Order order, List<OrderItem> orderItems, Map<Long, String> ruleMap);
}
