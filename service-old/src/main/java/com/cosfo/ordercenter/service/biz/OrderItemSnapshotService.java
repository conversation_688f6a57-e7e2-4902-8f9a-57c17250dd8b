package com.cosfo.ordercenter.service.biz;

import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;

import java.util.List;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
public interface OrderItemSnapshotService {

    /**
     * 查询订单项快照
     *
     * @param orderItems
     * @return
     */
    Map<Long, OrderItemSnapshot> queryOrderItemSnapshot(List<OrderItem> orderItems);
}
