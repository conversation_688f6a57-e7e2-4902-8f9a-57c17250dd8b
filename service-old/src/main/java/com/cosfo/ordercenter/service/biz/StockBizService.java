package com.cosfo.ordercenter.service.biz;

import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;

/**
 * <AUTHOR>
 */
public interface StockBizService {

    /**
     * 释放库存
     * @return
     */
    boolean releaseStock(String orderNo, Long orderItemId, SaleStockChangeTypeEnum stockChangeTypeEnum, Integer amount, Long warehouseNo, Long storeId);


    /**
     * 锁定库存 针对三方仓订单售后
     * @return
     */
    boolean lockStockForAfterSale(OrderAfterSale orderAfterSale, Integer itemAmount);


}
