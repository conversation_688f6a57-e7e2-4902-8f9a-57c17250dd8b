package com.cosfo.ordercenter.service.biz.aftersale.executor;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.NotifyBizService;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.StockBizService;
import com.cosfo.ordercenter.service.biz.bo.OrderAfterSaleAuditBO;
import com.cosfo.ordercenter.service.constant.OrderAfterSaleConstant;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.enums.OfcOrderSourceEnum;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.ValidateCancelAfterSaleOrderReq;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExchangeAfterSaleExecutor implements OrderAfterSaleExecutor {

    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderDao orderDao;

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private StockBizService stockBizService;
    @Resource
    private NotifyBizService notifyBizService;

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    @Override
    public List<OrderAfterSaleServiceTypeEnum> serviceType() {
        return Lists.newArrayList(OrderAfterSaleServiceTypeEnum.EXCHANGE);
    }

    @Override
    public Long createOrderAfterSale(OrderAfterSaleDTO orderAfterSaleDTO) {
        OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(orderAfterSaleDTO);
        orderAfterSale.setServiceType(serviceType().get(0).getValue());
        orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        log.info("创建换货售后单, 转换后对象={}", orderAfterSaleDTO);
        orderAfterSaleDao.save(orderAfterSale);
        return orderAfterSale.getId();
    }

    @Override
    public List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleDTO> orderAfterSaleList) {
        if (CollectionUtils.isEmpty(orderAfterSaleList)) {
            return Collections.emptyList();
        }
        // 设置类型和状态
        List<OrderAfterSale> collect = orderAfterSaleList.stream().map(orderAfterSaleDTO -> {
            OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(orderAfterSaleDTO);
            orderAfterSale.setServiceType(serviceType().get(0).getValue());
            orderAfterSale.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
            return orderAfterSale;
        }).collect(Collectors.toList());
        log.info("批量创建换货售后单, result={}", JSON.toJSONString(collect));
        return orderAfterSaleDao.batchAdd(collect);
    }

    @Override
    public Boolean reviewSuccess(OrderAfterSaleAuditReq req) {
        OrderAfterSale afterSale = orderAfterSaleDao.queryByAfterSaleNo(req.getAfterSaleOrderNo());

        // 只有三方仓订单支持换货
        if (!WarehouseTypeEnum.THREE_PARTIES.getCode().equals(afterSale.getWarehouseType())) {
            throw new BizException("售后单不支持换货");
        }

        // 换货需要出库，先占库存
        // 仓库库存是否充足校验，补发单冻结库存，调RPC接口，成功更新三方处理中，失败抛异常

        if (!req.isNeedServiceProviderAudit()) {
            try {
                // TODO xwk 冻结库存
                stockBizService.lockStockForAfterSale(afterSale, req.getAmount());
            } catch (Exception e) {
                log.error("冻结库存失败，orderAfterSale={}", afterSale, e);
                throw new BizException("库存不足,请和仓库确认库存足够后再试");
            }
        }


        // 如果不需要确认，占用库存成功，变更售后单状态为【三方处理中】9，否则【待确认】12
        OrderAfterSale update = new OrderAfterSale();
        update.setId(afterSale.getId());
        update.setHandleTime(LocalDateTime.now());
        update.setHandleRemark(req.getHandleRemark());
        update.setRecycleTime(req.getRecycleTime());
        Integer status = req.isNeedServiceProviderAudit() ? OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue() : OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue();
        update.setStatus(status);
        update.setAmount(req.getAmount());
        update.setOperatorName(req.getOperatorName());
        update.setResponsibilityType(StringUtils.isNotBlank(req.getResponsibilityType()) ? NumberUtils.toInt(req.getResponsibilityType()) : null);
        orderAfterSaleDao.updateById(update);
        log.info("变更售后单:{}状态为[{}]", afterSale.getAfterSaleOrderNo(), OrderAfterSaleStatusEnum.getStatusDesc(status));

        // ofc履约中心会监听三方处理中的状态，去生成回收单和补发单，后续换货成功，mq消息通知
        // 消息1：mq回告履约单生成成功，包含城配仓编号和配送日期，saas保存信息
        // 消息2：mq消息回告配送完成，更新状态为已完成4. cosf-mall OfcAfterSaleNewListener

        return true;
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditReq req, OrderAfterSale orderAfterSale) {
        return orderAfterSaleBizService.reviewReject(req, orderAfterSale);
    }

    @Override
    public boolean cancel(OrderAfterSale orderAfterSale) {
        Order order = orderDao.getById(orderAfterSale.getOrderId());

        if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            Integer serviceType = orderAfterSale.getServiceType();
            if (OrderAfterSaleServiceTypeEnum.verifyInteraction(serviceType)) {
                ValidateCancelAfterSaleOrderReq req = new ValidateCancelAfterSaleOrderReq();
                req.setAfterSaleOrderNo(orderAfterSale.getAfterSaleOrderNo());
                req.setSource(OfcOrderSourceEnum.SAAS_AFTER_SALE);
                if(StringUtils.isNotBlank(order.getCustomerOrderId())){
                    req.setStoreId(order.getStoreId());
                }
                boolean flag = RpcResultUtil.handle(fulfillmentOrderQueryProvider.validateCancelAfterSaleOrder(req));
                if (!flag) {
                    throw new BizException("不好意思，售后单不能取消");
                }
                stockBizService.releaseStock(orderAfterSale.getAfterSaleOrderNo(), orderAfterSale.getOrderItemId(), SaleStockChangeTypeEnum.MANUAL_CLOSED, orderAfterSale.getAmount(), null, orderAfterSale.getStoreId());
            }

        }
        // 变更状态
        OrderAfterSale update = new OrderAfterSale();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
        log.info("变更售后单为取消状态, afterSaleId={}", orderAfterSale.getId());
        return orderAfterSaleDao.updateById(update);
    }

    @Override
    public boolean finish(List<OrderAfterSaleProcessFinishReq> reqs) {
        OrderAfterSaleProcessFinishReq req = reqs.get(0);
        String orderAfterSaleNo = req.getOrderAfterSaleNo();
        OrderAfterSale orderAfterSale = orderAfterSaleDao.queryByAfterSaleNo(orderAfterSaleNo);
        if (reqs.size() != 2) {
            log.error("售后单号:{}换货数据格式有误", orderAfterSaleNo);
            return false;
        }

        OrderAfterSale update = new OrderAfterSale();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
        StringBuilder recycleDetail = getRecycleDetail(reqs);
        update.setRecycleDetails(recycleDetail.toString());
        // 获取回收详细数据
        Optional<OrderAfterSaleProcessFinishReq> recycleReqOptional = reqs.stream().filter(dto ->
                Objects.nonNull(dto.getItemFinishType()) && DeliveryTypeEnum.RECYCLE.getType().equals(dto.getItemFinishType())).findFirst();
        if (recycleReqOptional.isPresent()) {
            update.setRecyclePicture(recycleReqOptional.get().getRecyclePicture());
            update.setRecycleQuantityDetail(recycleReqOptional.get().getRecycleQuantityDetail());
            update.setRecycleDetails(recycleReqOptional.get().getRecycleDetailMessage());
        }

        update.setFinishedTime(LocalDateTime.now());
        orderAfterSaleDao.updateById(update);
        log.info("售后单号:{}换货完毕，变更状态为已完成,update:{}", orderAfterSaleNo, JSON.toJSONString(update));

        boolean allNormalFlag = reqs.stream().allMatch(el -> Objects.equals(el.getState(), DeliveryStateEnum.NORMAL.getState()));
        // 发送钉钉消息提示异常售后订单
        if (!allNormalFlag) {
            orderAfterSale.setRecycleDetails(recycleDetail.toString());
            notifyBizService.sendAfterSaleNotifyMessage(orderAfterSale);
        }

        orderAfterSaleBizService.createAfterSaleOrderIfNeed(reqs, orderAfterSale.getServiceType());
        return allNormalFlag;
    }

    private static StringBuilder getRecycleDetail(List<OrderAfterSaleProcessFinishReq> reqs) {
        StringBuilder recycleDetail = new StringBuilder();
        for (OrderAfterSaleProcessFinishReq finishReq : reqs) {
            boolean isNormal = Objects.equals(finishReq.getState(), DeliveryStateEnum.NORMAL.getState());
            boolean isDelivery = Objects.equals(finishReq.getDeliveryType(), DeliveryTypeEnum.DELIVERY.getType());
            String recycleDetails = isDelivery ? OrderAfterSaleConstant.NORMAL_RECYCLE_TEMPLATE : OrderAfterSaleConstant.NORMAL_RECYCLE_TEMPLATE;

            if (isDelivery && !isNormal) {
                recycleDetails = OrderAfterSaleConstant.ABNORMAL_DELIVERY_TEMPLATE + "应配送" + finishReq.getShouldCount() + "缺货" + finishReq.getShortCount() + " ";
            } else if (!isNormal) {
                recycleDetails = OrderAfterSaleConstant.ABNORMAL_RECYCLE_TEMPLATE + finishReq.getRemark() + " ";
            }
            recycleDetail.append(recycleDetails);
        }
        return recycleDetail;
    }

    @Override
    public void serviceProviderPassSubmissions(OrderAfterSaleAuditBO orderAfterSaleAuditBO) {
        OrderAfterSale orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        try {
            stockBizService.lockStockForAfterSale(orderAfterSale, orderAfterSale.getAmount());
        } catch (Exception e) {
            log.error("换货-冻结库存失败，orderAfterSale={}", orderAfterSale, e);
            throw new BizException("库存不足,请和仓库确认库存足够后再试");
        }
        OrderAfterSale update = new OrderAfterSale();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setServiceProviderAuditTime(LocalDateTime.now());
        orderAfterSaleDao.updateById(update);

        log.info("变更售后单:{}状态为[三方处理中-9]", orderAfterSale.getAfterSaleOrderNo());
    }
}
