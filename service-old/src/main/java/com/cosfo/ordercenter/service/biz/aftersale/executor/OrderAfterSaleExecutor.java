package com.cosfo.ordercenter.service.biz.aftersale.executor;

import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.bo.OrderAfterSaleAuditBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderAfterSaleExecutor {

    List<OrderAfterSaleServiceTypeEnum> serviceType();

    /**
     * 创建售后单
     *
     * @param orderAfterSaleDTO
     * @return
     */
    Long createOrderAfterSale(OrderAfterSaleDTO orderAfterSaleDTO);

    /**
     * 批量创建售后单
     *
     * @param orderAfterSaleList
     * @return
     */
    List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleDTO> orderAfterSaleList);

    /**
     * 审核通过
     *
     * @param req
     * @return
     */
    Boolean reviewSuccess(OrderAfterSaleAuditReq req);

    /**
     * 审核拒绝
     *
     * @param req
     * @return
     */
    Boolean reviewReject(OrderAfterSaleAuditReq req, OrderAfterSale orderAfterSale);

    /**
     * 售后单取消
     *
     * @param orderAfterSale
     * @return
     */
    boolean cancel(OrderAfterSale orderAfterSale);

    /**
     * 售后单完成
     *
     * @return
     */
    boolean finish(List<OrderAfterSaleProcessFinishReq> reqs);

    /**
     * 服务商通过
     * @param orderAfterSaleAuditBO
     */
    void serviceProviderPassSubmissions(OrderAfterSaleAuditBO orderAfterSaleAuditBO);
}
