package com.cosfo.ordercenter.service.biz.aftersale.executor;

import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleExecutorContext {

    @Resource
    private List<OrderAfterSaleExecutor> orderAfterSaleExecutors;

    public OrderAfterSaleExecutor load(Integer serviceType) {
        //
        OrderAfterSaleServiceTypeEnum typeEnum = OrderAfterSaleServiceTypeEnum.from(serviceType);
        for (OrderAfterSaleExecutor orderAfterSaleExecutor : orderAfterSaleExecutors) {
            if (orderAfterSaleExecutor.serviceType().contains(typeEnum)) {
                return orderAfterSaleExecutor;
            }
        }
        throw new BizException("售后类型不存在");
    }
}
