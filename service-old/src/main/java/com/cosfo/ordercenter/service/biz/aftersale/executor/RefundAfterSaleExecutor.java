package com.cosfo.ordercenter.service.biz.aftersale.executor;

import cn.hutool.core.util.NumberUtil;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.manage.client.enums.FlowRuleAuditBizTypeEnum;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderDeliveryFeeSnapshotDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.OrderAsyncService;
import com.cosfo.ordercenter.service.biz.bo.OrderAfterSaleAuditBO;
import com.cosfo.ordercenter.service.biz.delivery.RefundDeliveryFeeService;
import com.cosfo.ordercenter.service.constant.Constant;
import com.cosfo.ordercenter.service.constant.OrderAfterSaleConstant;
import com.cosfo.ordercenter.service.converter.MerchantDeliveryConvert;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.service.util.RpcResponseUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderOperateProvider;
import net.summerfarm.ofc.client.req.RefundFreezeOrderReq;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RefundAfterSaleExecutor implements OrderAfterSaleExecutor {
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderDeliveryFeeSnapshotDao orderDeliveryFeeSnapshotDao;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private OrderAsyncService orderAsyncService;
    @Resource
    private RefundDeliveryFeeService refundDeliveryFeeService;
    @DubboReference
    private FulfillmentOrderOperateProvider fulfillmentOrderOperateProvider;
    @DubboReference
    private TenantProvider tenantProvider;

    @Override
    public List<OrderAfterSaleServiceTypeEnum> serviceType() {
        return Lists.newArrayList(REFUND, REFUND_ENTER_BILL, BALANCE);
    }

    @Override
    public Long createOrderAfterSale(OrderAfterSaleDTO orderAfterSaleDTO) {

        Long orderId = orderAfterSaleDTO.getOrderId();
        Order order = orderDao.getById(orderId);

        // 冻结履约单
        // 配送前售后冻结履约单
        if (Objects.equals(orderAfterSaleDTO.getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            if(needRefundFreezeOrder(order)) {
                RefundFreezeOrderReq req = new RefundFreezeOrderReq();
                req.setOrderNoList(Lists.newArrayList(order.getOrderNo()));
                RpcResponseUtil.handler(fulfillmentOrderOperateProvider.refundFirstFreezeFulfillment(req), "不好意思，申请失败，已经开始配送啦");
            }
        }

        boolean isCombineOrderFlag = OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType());

        Boolean needAuditFlag = true;
//        如果支付方式不是线下支付 在判断是否需要审核
        if(!PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ())){
            if (Objects.equals (orderAfterSaleDTO.getAfterSaleType (), OrderAfterSaleTypeEnum.NOT_SEND.getType ())) {
                if (Objects.equals (order.getWarehouseType (), WarehouseTypeEnum.THREE_PARTIES.getCode ())) {
                    // 如果是优选仓的商品发起未到货退款  直接返还库存以及退款
                    needAuditFlag = false;
                } else {
                    // 组合包订单退款不需要审核
                    if (isCombineOrderFlag) {
                        needAuditFlag = false;
                    } else {
                        //如果是不是三方仓判断是否需要审核
                        needAuditFlag = getNeedAuditFlag (order);
                    }
                }
            }
        }
        // 生成售后单
        orderAfterSaleDTO.setStatus(OrderAfterSaleStatusEnum.UNAUDITED.getValue());
        Integer serviceType = OrderAfterSaleServiceTypeEnum.getServiceTypeByPayType(order.getPayType(), OrderAfterSaleTypeEnum.NOT_SEND.getType());
        orderAfterSaleDTO.setServiceType(serviceType);
        // 是否需要退运费
        OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(orderAfterSaleDTO);
        if (Objects.equals(orderAfterSaleDTO.getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType()) && orderAfterSaleBizService.validationIsNeedRefundDeliveryFee(Collections.singletonList(orderAfterSale))) {
            orderAfterSale.setDeliveryFee(order.getDeliveryFee());
            orderAfterSale.setTotalPrice(orderAfterSale.getApplyPrice().add(orderAfterSale.getDeliveryFee()));
        }
        orderAfterSaleDao.save(orderAfterSale);
        Long orderAfterSaleId = orderAfterSale.getId();

        // 不需要审核则返还库存、退款
        if (!needAuditFlag) {
            // 更新售后单
            OrderAfterSale update = new OrderAfterSale();
            update.setId(orderAfterSaleId);
            update.setStatus(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue());
            update.setHandleTime(LocalDateTime.now());
            update.setHandleRemark(OrderAfterSaleConstant.SYSTEM_PASS);
            update.setOperatorName(OrderAfterSaleConstant.SYSTEM);
            orderAfterSaleDao.updateById(update);
        }
        return orderAfterSaleId;
    }


    /**
     * 是否需要调用冻结履约单接口
     * true-需要；false-不需要
     * @param order
     * @return
     */
    private boolean needRefundFreezeOrder(Order order){
        if(order == null){
            return true;
        }
        // 非预售订单
        boolean nonPresaleOrderFlag = !OrderTypeEnum.PRESALE_ORDER.getValue().equals(order.getOrderType()) || (OrderTypeEnum.PRESALE_ORDER.getValue().equals(order.getOrderType()) && order.getDeliveryTime() != null);
        // 预售订单且未设置配送日期，不需要调用冻结履约单接口
        if(!nonPresaleOrderFlag){
            log.info("orderNo={} 预售订单且未设置配送日期，不需要调用冻结履约单接口", order.getOrderNo());
            return false;
        }

        // 线下支付，三方仓订单，未设置同步履约时间，不需要调用冻结履约单接口
        boolean offlinePayFlag = PayTypeEnum.OFFLINE_PAY.getCode().equals(order.getPayType()) &&
                Objects.equals (order.getWarehouseType (), WarehouseTypeEnum.THREE_PARTIES.getCode())
                && order.getBeginDeliveryTime() == null;

        if(offlinePayFlag){
            log.info("orderNo={} 线下支付，三方仓订单，未设置同步履约时间，不需要调用冻结履约单接口", order.getOrderNo());
            return false;
        }

        return true;
    }

    private Boolean getNeedAuditFlag(Order order) {
        Boolean needAuditFlag = RpcResponseUtil.handler(tenantProvider.getAfterSaleAuditSwitch(order.getStoreId(), FlowRuleAuditBizTypeEnum.SELFWAREHOUSE_PRE_DELIVERY_AFTERSALE_AUDIT.getType()));

//        Boolean needAuditFlag;
//        boolean hasWarehouse = WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(order.getWarehouseType());
//        if (hasWarehouse) {
//            needAuditFlag = RpcResponseUtil.handler(tenantProvider.getAfterSaleApproveSwitch4haveWarehouse(order.getTenantId()));
//        } else {
//            needAuditFlag = RpcResponseUtil.handler(tenantProvider.getAfterSaleApproveSwitch4nonWarehouse(order.getTenantId()));
//        }
        return needAuditFlag;
    }

    @Override
    public List<Long> batchCreateOrderAfterSale(List<OrderAfterSaleDTO> orderAfterSaleList) {

        orderAfterSaleList.forEach(orderAfterSaleDTO -> {
            String afterSaleOrderNo = Constant.createOrderNo(Constant.NORMAL_ORDER_AFTER_SALE_CODE);
            orderAfterSaleDTO.setAfterSaleOrderNo(afterSaleOrderNo);
        });

        Long orderId = orderAfterSaleList.get(0).getOrderId();
        Order order = orderDao.getById(orderId);

        //冻结履约单 未配送
        // TODO xwk 三方仓也要冻结履约单
        if (Objects.equals(orderAfterSaleList.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            if(needRefundFreezeOrder(order)) {
                RefundFreezeOrderReq req = new RefundFreezeOrderReq();
                req.setOrderNoList(Lists.newArrayList(order.getOrderNo()));
                RpcResponseUtil.handler(fulfillmentOrderOperateProvider.refundFirstFreezeFulfillment(req), "不好意思，申请失败，已经开始配送啦");
            }
        }

        // 查询每个售后单是否需要审核
        Map<Long, Boolean> orderItemId2needAuditFlagMap = getNeedAuditFlagMap(orderAfterSaleList, order);

        Integer serviceType = OrderAfterSaleServiceTypeEnum.getServiceTypeByPayType(order.getPayType(), OrderAfterSaleTypeEnum.NOT_SEND.getType());
        List<OrderAfterSale> createList = new ArrayList<>(orderAfterSaleList.size());
        for (OrderAfterSaleDTO afterSaleDTO : orderAfterSaleList) {
            Boolean afterSaleNeedAuditFlag = orderItemId2needAuditFlagMap.get(afterSaleDTO.getOrderItemId());

            OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(afterSaleDTO);
            orderAfterSale.setStatus(afterSaleNeedAuditFlag ? OrderAfterSaleStatusEnum.UNAUDITED.getValue() : OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue());
            orderAfterSale.setHandleTime(afterSaleNeedAuditFlag ? null : LocalDateTime.now());
            orderAfterSale.setHandleRemark(afterSaleNeedAuditFlag ? null : "系统自动通过");
            orderAfterSale.setOperatorName(afterSaleNeedAuditFlag ? null : "系统");
            if (!afterSaleNeedAuditFlag) {
                orderAfterSale.setTotalPrice(orderAfterSale.getApplyPrice());
            }
            orderAfterSale.setServiceType(serviceType);
            orderAfterSale.setTenantId(order.getTenantId());
            orderAfterSale.setOrderId(orderId);
            orderAfterSale.setStoreId(order.getStoreId());
            orderAfterSale.setAccountId(order.getAccountId());
            createList.add(orderAfterSale);
        }

        // 计算退运费
        MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO = refundDeliveryFeeService.queryRefundDeliveryFee(createList, order);
        createList.get(0).setDeliveryFee(deliveryFeeSnapshotDTO.getDeliveryFee());
        createList.get(0).setTotalPrice(createList.get(0).getApplyPrice()
                .add(Optional.ofNullable(deliveryFeeSnapshotDTO.getDeliveryFee()).orElse(BigDecimal.ZERO)));

        // 保存快照
        orderDeliveryFeeSnapshotDao.save(MerchantDeliveryConvert.INSTANCE.convert2Po(deliveryFeeSnapshotDTO));

        // 生成售后单
        return orderAfterSaleDao.batchAdd(createList);
    }

    /**
     * 查询每个售后单是否需要审核
     * @param orderAfterSaleList
     * @param order
     * @return
     */
    private Map<Long, Boolean> getNeedAuditFlagMap(List<OrderAfterSaleDTO> orderAfterSaleList, Order order){
        if(PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ())) {
            return orderAfterSaleList.stream()
                    .collect(Collectors.toMap(
                            OrderAfterSaleDTO::getOrderItemId,
                            e -> true,
                            (existing, replacement) -> existing // 合并函数：保留已有的值
                    ));
        }
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderAfterSaleList.stream().map(OrderAfterSaleDTO::getOrderItemId).collect(Collectors.toList()));
        Map<Long, Integer> orderItemId2goodsTypeMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, OrderItemSnapshot::getGoodsType, (v1, v2) -> v1));

        // 订单改单发起的售后，不需要审核
        if(OrderAfterSaleReqSourceEnum.isEqualReqSource(orderAfterSaleList.get(0).getReqSource(), OrderAfterSaleReqSourceEnum.CHANGE_ORDER)){
            return orderAfterSaleList.stream().collect(Collectors.toMap(OrderAfterSaleDTO::getOrderItemId, v -> false, (v1, v2) -> v1));
        }

        boolean isCombineOrderFlag = OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType());

        if (Objects.equals(orderAfterSaleList.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            if (Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
                // 如果是优选仓的商品发起未到货退款  直接返还库存以及退款
                Boolean needAuditFlag = false;
                // 代仓自营品，是否需要审核
                Boolean agentWarehouseNeedAuditFlag = RpcResponseUtil.handler(tenantProvider.getAfterSaleAuditSwitch(order.getStoreId(), FlowRuleAuditBizTypeEnum.AGENT_WAREHOUSE_PRE_DELIVERY_AFTERSALE_AUDIT.getType()));

                return orderAfterSaleList.stream().collect(Collectors.toMap(OrderAfterSaleDTO::getOrderItemId, v -> {
                    // 区分每一个售后单是否需要审核，三方仓订单里面的代仓自营品，需要根据审核开关判断
                    if(GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(orderItemId2goodsTypeMap.get(v.getOrderItemId()))){
                        return agentWarehouseNeedAuditFlag;
                    }
                    return needAuditFlag;
                }, (v1, v2) -> v1));
            } else {
                // 组合包订单退款不需要审核
                if (isCombineOrderFlag) {
                    Boolean needAuditFlag = false;
                    return orderAfterSaleList.stream().collect(Collectors.toMap(OrderAfterSaleDTO::getOrderItemId, v -> needAuditFlag, (v1, v2) -> v1));
                } else {
                    //如果是自营仓判断是否需要审核
                    Boolean needAuditFlag = getNeedAuditFlag(order);
                    return orderAfterSaleList.stream().collect(Collectors.toMap(OrderAfterSaleDTO::getOrderItemId, v -> needAuditFlag, (v1, v2) -> v1));
                }
            }
        }

        return orderAfterSaleList.stream().collect(Collectors.toMap(OrderAfterSaleDTO::getOrderItemId, v -> true, (v1, v2) -> v1));
    }

    @Override
    public Boolean reviewSuccess(OrderAfterSaleAuditReq req) {

        OrderAfterSale afterSale = orderAfterSaleDao.queryByAfterSaleNo(req.getAfterSaleOrderNo());
        Order order = orderDao.getById(afterSale.getOrderId());

        if (Objects.equals(afterSale.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())
                && Objects.equals(afterSale, OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            throw new BizException("三方仓订单配送前退款无法进行审核");
        }

        if (OrderAfterSaleTypeEnum.DELIVERED.getType().equals(afterSale.getAfterSaleType())) {
            // 不需要服务商确认 更新售后订单状态为退款中 否则更新为待确认
            OrderAfterSale update = new OrderAfterSale();
            update.setId(afterSale.getId());
            Integer status = req.isNeedServiceProviderAudit() ? OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue() : OrderAfterSaleStatusEnum.REFUNDING.getValue();
            update.setStatus(status);
            update.setTotalPrice(req.getTotalPrice());
            update.setHandleRemark(req.getHandleRemark());
            update.setHandleTime(LocalDateTime.now());
            update.setOperatorName(req.getOperatorName());
            update.setRefundReceipt (req.getRefundReceipt());
            if (StringUtils.isNotBlank(req.getResponsibilityType())) {
                update.setResponsibilityType(NumberUtils.toInt(req.getResponsibilityType(), 0));
            }
            orderAfterSaleDao.updateById(update);
            log.info("变更售后单:{}状态为[{}]", afterSale.getAfterSaleOrderNo(), OrderAfterSaleStatusEnum.getStatusDesc(status));
            return true;
        } else if (OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(afterSale.getAfterSaleType())) {
            // 未到货退款
            // 是否退运费
            BigDecimal deliveryFee = BigDecimal.ZERO;
            if (orderAfterSaleBizService.validationIsNeedRefundDeliveryFee(Collections.singletonList(afterSale))) {
                deliveryFee = order.getDeliveryFee();
            }
            BigDecimal totalPrice = NumberUtil.add(deliveryFee, req.getTotalPrice());
            OrderAfterSale update = new OrderAfterSale();
            update.setId(afterSale.getId());
            update.setTotalPrice(totalPrice);
            update.setDeliveryFee(deliveryFee);
            update.setStatus(OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue());
            update.setHandleRemark(req.getHandleRemark());
            update.setOperatorName(req.getOperatorName());
            update.setHandleTime(LocalDateTime.now());
            update.setRefundReceipt (req.getRefundReceipt());
            return orderAfterSaleDao.updateById(update);

            // ofc履约中心binlog监听处理中状态，后续mq发消息通知saas消费者执行释放冻结库存和退款操作
        }

        return true;
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditReq req, OrderAfterSale orderAfterSale) {
        return orderAfterSaleBizService.reviewReject(req, orderAfterSale);
    }

    @Override
    public boolean cancel(OrderAfterSale orderAfterSale) {
        // 变更状态
        OrderAfterSale update = new OrderAfterSale();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
        log.info("售后单取消, afterSaleId={}", orderAfterSale.getId());
        return orderAfterSaleDao.updateById(update);
    }

    @Override
    public boolean finish(List<OrderAfterSaleProcessFinishReq> reqs) {
        // 无处理
        return true;
    }

    @Override
    public void serviceProviderPassSubmissions(OrderAfterSaleAuditBO orderAfterSaleAuditBO) {
        OrderAfterSale orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        OrderAfterSale update = new OrderAfterSale();
        update.setId(orderAfterSale.getId());
        update.setStatus(OrderAfterSaleStatusEnum.REFUNDING.getValue());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setTotalPrice(orderAfterSaleAuditBO.getTotalPrice());
        update.setServiceProviderAuditTime(LocalDateTime.now());
        orderAfterSaleDao.updateById(update);

        log.info("变更售后单:{}状态为[退款中-3]", orderAfterSale.getAfterSaleOrderNo());
    }
}
