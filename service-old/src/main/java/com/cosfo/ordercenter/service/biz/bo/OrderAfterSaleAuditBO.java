package com.cosfo.ordercenter.service.biz.bo;

import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description:
 * @author: George
 * @date: 2023-12-21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderAfterSaleAuditBO {

    /**
     * 审核说明
     */
    private String handleRemark;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 售后金额
     */
    private BigDecimal totalPrice;

    /**
     * 责任方
     */
    private Integer responsibility;

    /**
     * 售后单
     */
    private OrderAfterSale orderAfterSale;
}
