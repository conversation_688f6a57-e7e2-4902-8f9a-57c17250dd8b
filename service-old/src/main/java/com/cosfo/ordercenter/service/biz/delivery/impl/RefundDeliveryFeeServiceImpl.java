package com.cosfo.ordercenter.service.biz.delivery.impl;
import com.google.common.collect.Lists;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.OrderTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.delivery.RefundDeliveryFeeService;
import com.cosfo.ordercenter.service.biz.delivery.warehouse.executor.MerchantDeliveryWarehouseContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/8/11 10:20
 * @Description:
 */
@Service
@Slf4j
public class RefundDeliveryFeeServiceImpl implements RefundDeliveryFeeService {
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private MerchantDeliveryWarehouseContext warehouseContext;


    /**
     * 无仓、自营仓、三方仓的随仓报价，满足退款条件退全部运费
     * 三方仓&每日运费，满足退款条件计算应退运费
     *
     * @param orderAfterSaleInputs 售后信息
     * @param orderDTO             订单信息
     * @return
     */
    @Override
    public MerchantDeliveryFeeSnapshotDTO queryRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, Order orderDTO) {
        log.info("开始计算退款运费：订单ID：{}，售后商品：{}", orderDTO.getId(), JSON.toJSONString(orderAfterSaleInputs));

        if(OrderTypeEnum.PRESALE_ORDER.getValue().equals(orderDTO.getOrderType())){
            log.info("预售订单退款运费为0：订单ID：{}，售后商品：{}", orderDTO.getId(), JSON.toJSONString(orderAfterSaleInputs));
            MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO = new MerchantDeliveryFeeSnapshotDTO();
            deliveryFeeSnapshotDTO.setDeliveryFee(BigDecimal.ZERO);
            return deliveryFeeSnapshotDTO;
        }

        MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO = warehouseContext.load(orderDTO.getWarehouseType())
            .calculateRefundDeliveryFee(orderAfterSaleInputs, orderDTO);
        log.info("计算退款运费完成。快照信息：{}", JSON.toJSONString(deliveryFeeSnapshotDTO));
        return deliveryFeeSnapshotDTO;
    }


    /**
     * 满足退款条件，退全部运费
     *
     * @param orderAfterSaleInputs
     * @param deliveryFee
     * @return
     */
    @Override
    public BigDecimal calculateNormalDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, BigDecimal deliveryFee) {
        if (Objects.equals(orderAfterSaleInputs.get(0).getAfterSaleType(), OrderAfterSaleTypeEnum.NOT_SEND.getType())
            && orderAfterSaleBizService.validationIsNeedRefundDeliveryFee(orderAfterSaleInputs)) {
            return deliveryFee;
        } else {
            return null;
        }
    }

}
