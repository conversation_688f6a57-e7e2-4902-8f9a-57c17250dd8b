package com.cosfo.ordercenter.service.biz.delivery.ruletype.executor;

import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MerchantDeliveryRuleTypeContext {

    @Resource
    private List<MerchantDeliveryRuleTypeExecutor> executors;

    public MerchantDeliveryRuleTypeExecutor load(Integer ruleType) {
        MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum typeEnum = MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.getByType(ruleType);
        for (MerchantDeliveryRuleTypeExecutor ruleTypeExecutor : executors) {
            if (ruleTypeExecutor.ruleType().contains(typeEnum)) {
                return ruleTypeExecutor;
            }
        }
        throw new BizException("规则类型不存在");
    }
}
