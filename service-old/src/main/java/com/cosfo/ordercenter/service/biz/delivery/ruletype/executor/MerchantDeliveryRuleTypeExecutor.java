package com.cosfo.ordercenter.service.biz.delivery.ruletype.executor;

import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 18:34
 * @Description:
 */
public interface MerchantDeliveryRuleTypeExecutor {

    List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType();

    /**
     * 计算每日运费
     *
     * @param deliveryDTO
     * @param ruleList
     * @return
     */
    MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList);

    /**
     * 计算每日运费的应退运费
     *
     * @param orderAfterSaleInputs 售后单
     * @param deliveryRuleInfoDTOS 规则配置
     * @param orderDTO             信息配置
     * @return
     */
    MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs,
                                                                     List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS,
                                                                     Order orderDTO);
}
