package com.cosfo.ordercenter.service.biz.delivery.ruletype.executor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.ordercenter.client.common.FulfillmentTypeEnum;
import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.resp.delivery.*;
import com.cosfo.ordercenter.client.validgroup.StepTypeGroup;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.dto.DeliveryFeeItemMatchRuleDTO;
import com.cosfo.ordercenter.service.constant.DeliveryConstant;
import com.cosfo.ordercenter.service.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 14:03
 * @Description:
 */
@Slf4j
@Service
public class PerOrderExecutor implements MerchantDeliveryRuleTypeExecutor {

    @Override
    public List<MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum> ruleType() {
        return Collections.singletonList(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.SINGLE);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRuleTypeDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        // 没有需要计算的运费时，直接返回
        if (CollectionUtils.isEmpty(deliveryDTO.getOrderItemInfoDTOList())) {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                    .tenantId(deliveryDTO.getTenantId())
                    .deliveryFee(BigDecimal.ZERO)
                    .orderInfo(deliveryDTO)
                    .ruleList(ruleList)
                    .hitRuleList(Collections.emptyList())
                    .remark(DeliveryConstant.DELIVERY_NO_ITEM)
                    .build();
        }

        ValidatorUtils.validateEntity(deliveryDTO, StepTypeGroup.class);
        // 计算所有规则的运费
        WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(deliveryDTO.getOrderInfoDTO().getWarehouseType());
        deliveryDTO.getOrderInfoDTO().setWarehouseTypeEnum(warehouseTypeEnum);

        List<DeliveryItemFeeDTO> deliveryItemFeeDTOS = calculateDeliveryFee(deliveryDTO, ruleList);
        log.info("{},计算运费完成。所有命中规则的运费：{}",
                deliveryDTO.getOrderInfoDTO().getWarehouseTypeEnum().getDesc(),
                JSON.toJSONString(deliveryItemFeeDTOS));

        // 所有规则的最大运费即为最终运费
        DeliveryItemFeeDTO deliveryItemFeeDTO = deliveryItemFeeDTOS.stream()
                .max(Comparator.comparing(DeliveryItemFeeDTO::getDeliveryFee))
                .orElse(new DeliveryItemFeeDTO());
        if (Objects.isNull(deliveryItemFeeDTO.getItemId())) {
            log.error("计算运费失败！未计算出正确的规则运费：" + JSON.toJSONString(deliveryItemFeeDTO));
            throw new BizException("计算运费失败！");
        }
        log.info("{}计算运费完成,{},最终运费:{}",
                deliveryDTO.getOrderInfoDTO().getWarehouseTypeEnum().getDesc(),
                DeliveryConstant.DELIVERY_FEE_STEP,
                JSON.toJSONString(deliveryItemFeeDTO));

        return MerchantDeliveryFeeSnapshotDTO.builder()
                .tenantId(deliveryDTO.getTenantId())
                .deliveryFee(deliveryItemFeeDTO.getDeliveryFee())
                .orderInfo(deliveryDTO)
                .ruleList(ruleList)
                .hitRuleList(deliveryItemFeeDTOS)
                .remark(DeliveryConstant.DELIVERY_FEE_STEP)
                .build();
    }


    /**
     * 每个商品命中的规则及最终计算到的运费
     * <p>
     * 运费基础规则：
     * 1.例外规则 > 默认规则
     * 2.一个订单中多个商品命中规则时，取最大金额为整单运费
     * <p>
     * 运费计算规则：
     * 1.例外规则中的优先级适用于 同一仓、同一sku、同一区域，只能命中第一条规则。
     * 2.默认规则只在未命中例外规则时生效
     * 3.命中多条规则时选择最大运费
     *
     * @param deliveryTotalDTO
     * @return
     */
    private List<DeliveryItemFeeDTO> calculateDeliveryFee(DeliveryTotalDTO deliveryTotalDTO, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS) {
        // 所有下单商品匹配的运费
        List<DeliveryItemFeeDTO> deliveryItemFeeList = new ArrayList<>();

        Map<Long, DeliveryOrderItemInfoDTO> orderItemMap = deliveryTotalDTO.getOrderItemInfoDTOList().stream()
                .collect(Collectors.toMap(DeliveryOrderItemInfoDTO::getItemId, Function.identity(), (k1, k2) -> k1));

        // 运费规则id map
        Map<Long, MerchantDeliveryRuleInfoDTO> deliveryRuleMap = deliveryRuleInfoDTOS.stream()
                .collect(Collectors.toMap(MerchantDeliveryRuleInfoDTO::getRuleId, Function.identity(), (k1, k2) -> k1));

        Map<Long, List<Long>> ruleId2ItemIdsMap = new HashMap<>();

        // 所有可以匹配规则的商品
        List<Long> selectableItemIds = deliveryTotalDTO.getOrderItemInfoDTOList().stream().map(DeliveryOrderItemInfoDTO::getItemId).distinct().collect(Collectors.toList());

        // 按照规则优先级，挑选匹配的商品
        for (MerchantDeliveryRuleInfoDTO deliveryRule : deliveryRuleInfoDTOS) {
            List<Long> matchItemIds = new ArrayList<>();

            // 查看商品是否匹配规则
            for (Long itemId : selectableItemIds) {
                // 当前规则未命中
                if (!checkHitRule(itemId, deliveryTotalDTO.getStoreId(), orderItemMap.get(itemId), deliveryTotalDTO.getOrderInfoDTO(), deliveryRule)) {
                    continue;
                }

                matchItemIds.add(itemId);
            }

            // 如果商品不可以匹配当前规则的阶梯运费，去下个规则匹配
            if (!checkItemCanMatchRuleStepFee(matchItemIds, deliveryRule, orderItemMap)) {
                continue;
            }

            // 如果商品可以匹配规则的阶梯运费，选择此规则计算
            // 剩余可选择的商品去掉 已匹配规则商品
            selectableItemIds.removeAll(matchItemIds);
            // 记录规则-商品list映射
            ruleId2ItemIdsMap.put(deliveryRule.getRuleId(), matchItemIds);
        }

        // 运费计算结果
        List<DeliveryFeeItemMatchRuleDTO> matchRuleDTOS = new ArrayList<>();

        for (Map.Entry<Long, List<Long>> entry : ruleId2ItemIdsMap.entrySet()) {
            Long ruleId = entry.getKey();
            List<Long> matchItemIds = entry.getValue();

            // 匹配规则的商品 计算运费
            List<DeliveryItemFeeDTO> partList = calDeliveryFee4HitRule(matchItemIds, matchRuleDTOS, deliveryRuleMap.get(ruleId), orderItemMap);
            deliveryItemFeeList.addAll(partList);
        }

        log.info("计算运费快照信息，订单商品信息：{}, 匹配规则计算结果：{}", JSON.toJSONString(deliveryTotalDTO), JSON.toJSONString(matchRuleDTOS));

        return deliveryItemFeeList;
    }

    /**
     * 检查商品是否能匹配上规则的阶梯运费
     *
     * @param matchRuleItemIds
     * @param deliveryRuleInfoDTO
     * @param orderItemMap
     * @return
     */
    private boolean checkItemCanMatchRuleStepFee(List<Long> matchRuleItemIds, MerchantDeliveryRuleInfoDTO deliveryRuleInfoDTO, Map<Long, DeliveryOrderItemInfoDTO> orderItemMap) {
        if (CollectionUtils.isEmpty(matchRuleItemIds)) {
            return false;
        }

        if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.COUNT.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计匹配规则的商品件数
            Integer totalCount = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getItemCount)
                    .reduce(0, Integer::sum);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总件数>门槛值的，即为门槛运费
            return stepFeeDescList.stream()
                    .filter(s -> totalCount >= s.getStepThreshold().intValue())
                    .findFirst()
                    .isPresent();


        } else if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.PRICE.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计匹配规则的商品金额
            BigDecimal totalPrice = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getItemTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总金额>门槛值的，即为门槛运费
            return stepFeeDescList.stream()
                    .filter(s -> totalPrice.compareTo(s.getStepThreshold()) >= 0)
                    .findFirst()
                    .isPresent();


        } else if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.WEIGHT.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计默认规则的商品重量
            BigDecimal totalWeight = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getTotalWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总重量>门槛值的，即为门槛运费
            return stepFeeDescList.stream()
                    .filter(s -> totalWeight.compareTo(s.getStepThreshold()) >= 0)
                    .findFirst()
                    .isPresent();
        }

        return false;
    }

    /**
     * 匹配规则的商品 计算运费, 返回商品规则运费list
     *
     * @param matchRuleItemIds
     * @param matchRuleDTOS
     * @param deliveryRuleInfoDTO
     * @param orderItemMap
     * @return
     */
    private List<DeliveryItemFeeDTO> calDeliveryFee4HitRule(List<Long> matchRuleItemIds, List<DeliveryFeeItemMatchRuleDTO> matchRuleDTOS, MerchantDeliveryRuleInfoDTO deliveryRuleInfoDTO, Map<Long, DeliveryOrderItemInfoDTO> orderItemMap) {
        List<DeliveryItemFeeDTO> deliveryItemFeeList = new ArrayList<>();

        if (CollectionUtils.isEmpty(matchRuleItemIds)) {
            return deliveryItemFeeList;
        }

        // 记录商品匹配规则运费的结果信息
        DeliveryFeeItemMatchRuleDTO matchRuleDTO = new DeliveryFeeItemMatchRuleDTO();
        matchRuleDTO.setRuleId(deliveryRuleInfoDTO.getRuleId());
        matchRuleDTO.setItemIds(matchRuleItemIds);

        BigDecimal deliveryFee = BigDecimal.ZERO;
        Long ruleId = deliveryRuleInfoDTO.getRuleId();

        if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.COUNT.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计默认规则的商品件数
            Integer totalCount = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getItemCount)
                    .reduce(0, Integer::sum);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总件数>门槛值的，即为门槛运费
            DeliveryStepFeeDTO deliveryStepFeeDTO = stepFeeDescList.stream()
                    .filter(s -> totalCount >= s.getStepThreshold().intValue())
                    .findFirst()
                    .orElse(new DeliveryStepFeeDTO());

            // 计算运费
            deliveryFee = calDeliveryFee(new BigDecimal(String.valueOf(totalCount)), deliveryStepFeeDTO);

            matchRuleDTO.setTotalCount(totalCount);

        } else if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.PRICE.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计默认规则的商品金额
            BigDecimal totalPrice = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getItemTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总金额>门槛值的，即为门槛运费
            deliveryFee = stepFeeDescList.stream()
                    .filter(s -> totalPrice.compareTo(s.getStepThreshold()) >= 0)
                    .findFirst()
                    .orElse(new DeliveryStepFeeDTO())
                    .getDeliveryFee();

            matchRuleDTO.setTotalPrice(totalPrice);
        } else if (MerchantDeliveryFeeRuleEnum.DeliveryThresholdType.WEIGHT.getCode().equals(deliveryRuleInfoDTO.getDeliveryType())) {
            // 累计默认规则的商品重量
            BigDecimal totalWeight = matchRuleItemIds.stream()
                    .map(orderItemMap::get)
                    .filter(Objects::nonNull)
                    .map(DeliveryOrderItemInfoDTO::getTotalWeight)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            List<DeliveryStepFeeDTO> stepFeeDescList = deliveryRuleInfoDTO.getStepFeeDescList();
            // 门槛倒序排，最先一个满足总重量>门槛值的，即为门槛运费
            DeliveryStepFeeDTO deliveryStepFeeDTO = stepFeeDescList.stream()
                    .filter(s -> totalWeight.compareTo(s.getStepThreshold()) >= 0)
                    .findFirst()
                    .orElse(new DeliveryStepFeeDTO());

            // 计算运费
            deliveryFee = calDeliveryFee(totalWeight, deliveryStepFeeDTO);

            matchRuleDTO.setTotalWeight(totalWeight);
        }

        matchRuleDTO.setDeliveryFee(deliveryFee);
        matchRuleDTOS.add(matchRuleDTO);

        // 将默认规则匹配运费设置进结果
        for (Long itemId : matchRuleItemIds) {
            DeliveryItemFeeDTO itemFee = new DeliveryItemFeeDTO();
            itemFee.setItemId(itemId);
            itemFee.setRuleId(ruleId);
            itemFee.setDeliveryFee(deliveryFee);
            deliveryItemFeeList.add(itemFee);
        }

        return deliveryItemFeeList;
    }

    private BigDecimal calDeliveryFee(BigDecimal orderNum, DeliveryStepFeeDTO deliveryStepFeeDTO) {
        if (orderNum == null || deliveryStepFeeDTO == null) {
            return null;
        }

        // 固定运费
        if (deliveryStepFeeDTO.getCalType() == null || MerchantDeliveryFeeRuleEnum.DeliveryStepFeeCalTypeEnum.FIXED.getCode().equals(deliveryStepFeeDTO.getCalType())) {
            return deliveryStepFeeDTO.getDeliveryFee();
        }


        // 动态运费
        DeliveryFeeCalRuleDTO deliveryFeeCalRuleDTO = deliveryStepFeeDTO.getDeliveryfeeCalRule();
        if (deliveryFeeCalRuleDTO == null) {
            return null;
        }

        try {
            /**
             * 运费动态计算规则
             * 下单件数 order_num 且 order_num >= step_threshold（运费件数门槛）
             * delivery_fee = (order_num - start_threshold) <= 0 ? start_delivery_fee : ((order_num - start_threshold) * step_fee / step_factor + start_delivery_fee);
             */
            return NumberUtil.max(BigDecimal.ZERO, orderNum.subtract(deliveryFeeCalRuleDTO.getStartThreshold()))
                    .multiply(deliveryFeeCalRuleDTO.getStepFee())
                    .add(deliveryFeeCalRuleDTO.getStartDeliveryFee())
                    .setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("运费阶梯动态计算错误，orderNum={}, deliveryStepFeeDTO={}", orderNum, JSON.toJSONString(deliveryStepFeeDTO), e);
            return null;
        }

    }


    private boolean checkHitRule(Long itemId, Long storeId, DeliveryOrderItemInfoDTO deliveryOrderItemInfoDTO, DeliveryOrderInfoDTO orderInfo, MerchantDeliveryRuleInfoDTO deliveryRule) {
        // 默认规则必命中
        // 未命中则继续下一下规则
        if (MerchantDeliveryFeeRuleEnum.MerchantAddressDefaultTypeEnum.DEFAULT.getCode().equals(deliveryRule.getDefaultType())) {
            return true;
        }

        // 自营仓例外规则未关联当前订单的仓库
        if (WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderInfo.getWarehouseType())
                && Objects.isNull(deliveryRule.getWarehouseNo())) {
            return false;
        }

        // 三方优选仓-例外规则-按照履约方式
        if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(orderInfo.getWarehouseType())){
            // 匹配履约方式
            if (FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(orderInfo.getFulfillmentType())) {
                // 订单是快递履约，运费规则不是快递履约，不匹配
                if (!FulfillmentTypeEnum.EXPRESS_DELIVERY.getValue().equals(deliveryRule.getFulfillmentType())) {
                    return false;
                }

            } else {
                // 订单是城配履约，运费规则不是城配履约，不匹配
                if (!FulfillmentTypeEnum.CITY_DELIVERY.getValue().equals(deliveryRule.getFulfillmentType())) {
                    return false;
                }
            }
        }


        // 匹配区域类型 规则关联门店
        if (MerchantDeliveryFeeRuleEnum.MatchItemTypeEnum.GOODS_SOURCE.getCode().equals(deliveryRule.getMatchItemType())) {
            // 按照货源匹配
            if(deliveryRule.getHitGoodsSource() == null || !deliveryRule.getHitGoodsSource().equals(goodsSourceMap(orderInfo.getWarehouseType(), deliveryOrderItemInfoDTO.getGoodsType()))){
                return false;
            }

        } else {
            Set<Long> hitItemIds = deliveryRule.getHitItemIds();
            // 规则未关联商品
            if (!deliveryRule.getIncludeNewFlag() && (CollectionUtils.isEmpty(hitItemIds) || !hitItemIds.contains(itemId))) {
                return false;
            }
        }


        // 匹配区域类型 规则关联门店
        if (MerchantDeliveryFeeRuleEnum.MatchRegionTypeEnum.STORE.getCode().equals(deliveryRule.getMatchRegionType())) {

            Set<Long> hitStoreIds = deliveryRule.getHitStoreIds();
            if (!deliveryRule.getIncludeAllStoreFlag() && (CollectionUtils.isEmpty(hitStoreIds) || !hitStoreIds.contains(storeId))) {
                return false;
            }

        } else {

            // 规则中保存的省份没有"省""市"字样
            String storeProvince = orderInfo.getStoreProvince().replaceAll("(?<!市)省$|(?<!市)市$", "");

            // 规则未关联区域
            Set<List<String>> hitAreaList = deliveryRule.getHitAreaList();
            // 部分地方没有区
            List<String> orderArea = Arrays.asList(storeProvince, orderInfo.getStoreCity(), orderInfo.getStoreArea());
            List<String> orderCity = Arrays.asList(storeProvince, orderInfo.getStoreCity());
            if (CollectionUtils.isEmpty(hitAreaList) || (!hitAreaList.contains(orderArea) && !hitAreaList.contains(orderCity))) {
                return false;
            }

        }

        return true;
    }


    /**
     * 根据仓库类型和货品类型映射货源类型
     * @param warehouseType
     * @param goodsType
     * @return
     */
    private Integer goodsSourceMap(Integer warehouseType, Integer goodsType) {
        if(WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType)) {
            return MerchantDeliveryFeeRuleEnum.GoodsSourceEnum.SUPPLIER.getCode();
        }
        if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType)) {
            if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(goodsType)) {
                return MerchantDeliveryFeeRuleEnum.GoodsSourceEnum.AGENT.getCode();
            } else {
                return MerchantDeliveryFeeRuleEnum.GoodsSourceEnum.SUPPLIER.getCode();
            }
        }
        if (WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(warehouseType)) {
            return MerchantDeliveryFeeRuleEnum.GoodsSourceEnum.SELF.getCode();
        }
        return null;
    }


    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundThreeDailyDelivery(List<OrderAfterSale> orderAfterSaleInputs, List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS, Order orderDTO) {
        return null;
    }
}
