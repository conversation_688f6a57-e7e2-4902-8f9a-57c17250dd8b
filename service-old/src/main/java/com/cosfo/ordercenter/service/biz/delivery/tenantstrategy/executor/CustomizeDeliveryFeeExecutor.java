package com.cosfo.ordercenter.service.biz.delivery.tenantstrategy.executor;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.dao.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.dao.model.po.TenantDeliveryFeeArea;
import com.cosfo.ordercenter.dao.model.po.TenantDeliveryFeeRule;
import com.cosfo.ordercenter.service.biz.dto.*;
import com.cosfo.ordercenter.service.constant.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@Service
public class CustomizeDeliveryFeeExecutor implements DeliveryFeeStrategyExecutor {
    @Resource
    private TenantDeliveryFeeAreaDao tenantDeliveryFeeAreaDao;


    @Override
    public TenantDeliveryEnum.TypeEnum tenantStrategy() {
        return TenantDeliveryEnum.TypeEnum.CUSTOMIZE;
    }

    @Override
    public DeliveryFeeResultDTO calculateDeliveryFee(OrderDeliveryDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        // 基础运费
        BigDecimal deliveryFee;
        // 根据门店地址查询配送费规则
        // 下单地址
        OrderAddress orderAddress = orderDTO.getOrderAddress();
        // 根据一级类目分组计算金额和件数
        List<OrderItemDeliveryDTO> orderItemDTOList = orderDTO.getOrderItemDTOList();
        // 根据地址查询区域配送费规则
        TenantDeliveryFeeArea tenantDeliveryFeeArea = tenantDeliveryFeeAreaDao.queryAreaRule(orderDTO.getTenantId(), orderAddress.getProvince(), orderAddress.getCity(), orderAddress.getArea());
        if (Objects.nonNull(tenantDeliveryFeeArea)) {
            deliveryFee = tenantDeliveryFeeArea.getDefaultPrice();
            // 循环遍历分组匹配免运费规则
            List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOS = JSONObject.parseArray(tenantDeliveryFeeArea.getRule(), DeliveryFeeAreaRuleDTO.class);
            if (!CollectionUtils.isEmpty(deliveryFeeAreaRuleDTOS)) {
                for (DeliveryFeeAreaRuleDTO deliveryFeeAreaRuleDTO : deliveryFeeAreaRuleDTOS) {
                    List<Long> categoryIds = deliveryFeeAreaRuleDTO.getCategoryDTOList().stream().map(CategoryDTO::getCategoryId).collect(Collectors.toList());
                    // 价格
                    BigDecimal price = BigDecimal.ZERO;
                    // 件数
                    Integer amount = NumberConstant.ZERO;
                    for (OrderItemDeliveryDTO orderItemDTO : orderItemDTOList) {
                        if (categoryIds.contains(orderItemDTO.getFirstCategoryId())) {
                            price = NumberUtil.add(price, orderItemDTO.getCalcPartDeliveryFee());
                            amount += orderItemDTO.getAmount();
                        }
                    }

                    // 判断是否满足满减
                    // 金额判断
                    if (TenantDeliveryEnum.FreeType.MONEY.getType().equals(deliveryFeeAreaRuleDTO.getType()) && price.compareTo(deliveryFeeAreaRuleDTO.getFreeNumber()) >= NumberConstant.ZERO) {
                        deliveryFee = BigDecimal.ZERO;
                        break;
                    }

                    // 件数判断
                    if (TenantDeliveryEnum.FreeType.NUMBER.getType().equals(deliveryFeeAreaRuleDTO.getType()) && amount.compareTo(deliveryFeeAreaRuleDTO.getFreeNumber().intValue()) >= NumberConstant.ZERO) {
                        deliveryFee = BigDecimal.ZERO;
                        break;
                    }
                }
            }
            // 无匹配的运费规则
        } else {
            deliveryFee = tenantDeliveryFeeRule.getDefaultPrice();
            // 价格
            BigDecimal price = BigDecimal.ZERO;
            // 件数
            Integer amount = NumberConstant.ZERO;
            // 计算整个订单的商品金额和件数
            for (OrderItemDeliveryDTO orderItemDTO : orderItemDTOList) {
                price = NumberUtil.add(price, orderItemDTO.getCalcPartDeliveryFee());
                amount += orderItemDTO.getAmount();
            }

            // 判断是否满足满减
            // 金额判断
            if (TenantDeliveryEnum.FreeType.MONEY.getType().equals(tenantDeliveryFeeRule.getFreeType()) && price.compareTo(tenantDeliveryFeeRule.getFreeNumber()) >= NumberConstant.ZERO) {
                deliveryFee = BigDecimal.ZERO;
            }

            // 件数判断
            if (TenantDeliveryEnum.FreeType.NUMBER.getType().equals(tenantDeliveryFeeRule.getFreeType()) && amount.compareTo(tenantDeliveryFeeRule.getFreeNumber().intValue()) >= NumberConstant.ZERO) {
                deliveryFee = BigDecimal.ZERO;
            }
        }

        DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
        deliveryFeeResultDTO.setDeliveryFee(deliveryFee);
        List<Object> ruleList = new ArrayList<>();
        ruleList.add(tenantDeliveryFeeRule);
        ruleList.add(tenantDeliveryFeeArea);
        deliveryFeeResultDTO.setRule(JSON.toJSONString(ruleList));
        return deliveryFeeResultDTO;
    }

}
