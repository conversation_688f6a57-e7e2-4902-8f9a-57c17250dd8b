package com.cosfo.ordercenter.service.biz.delivery.tenantstrategy.executor;

import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 18:27
 * @Description:
 */
@Service
@Slf4j
public class DeliveryFeeStrategyContext {
    @Resource
    private List<DeliveryFeeStrategyExecutor> strategyExecutorList;

    public DeliveryFeeStrategyExecutor load(Integer type) {
        TenantDeliveryEnum.TypeEnum typeEnum = TenantDeliveryEnum.TypeEnum.getByType(type);
        for (DeliveryFeeStrategyExecutor executor : strategyExecutorList) {
            if (executor.tenantStrategy().equals(typeEnum)) {
                return executor;
            }
        }
        throw new BizException("租户策略类型不存在");
    }
}
