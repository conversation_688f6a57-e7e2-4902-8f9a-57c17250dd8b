package com.cosfo.ordercenter.service.biz.delivery.tenantstrategy.executor;

import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.dao.model.po.TenantDeliveryFeeRule;
import com.cosfo.ordercenter.service.biz.dto.DeliveryFeeResultDTO;
import com.cosfo.ordercenter.service.biz.dto.OrderDeliveryDTO;


/**
 * @author: monna.chen
 * @Date: 2023/8/22 18:20
 * @Description:
 */
public interface DeliveryFeeStrategyExecutor {

    TenantDeliveryEnum.TypeEnum tenantStrategy();

    /**
     * 计算运费
     *
     * @param orderDTO
     * @param tenantDeliveryFeeRule
     * @return
     */
    DeliveryFeeResultDTO calculateDeliveryFee(OrderDeliveryDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule);

}
