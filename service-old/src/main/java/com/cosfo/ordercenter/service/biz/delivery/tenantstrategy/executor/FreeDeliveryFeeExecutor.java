package com.cosfo.ordercenter.service.biz.delivery.tenantstrategy.executor;

import com.cosfo.ordercenter.client.common.TenantDeliveryEnum;
import com.cosfo.ordercenter.dao.model.po.TenantDeliveryFeeRule;
import com.cosfo.ordercenter.service.biz.dto.DeliveryFeeResultDTO;
import com.cosfo.ordercenter.service.biz.dto.OrderDeliveryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/16
 */
@Slf4j
@Service
public class FreeDeliveryFeeExecutor implements DeliveryFeeStrategyExecutor {

    @Override
    public TenantDeliveryEnum.TypeEnum tenantStrategy() {
        return TenantDeliveryEnum.TypeEnum.FREE;
    }

    @Override
    public DeliveryFeeResultDTO calculateDeliveryFee(OrderDeliveryDTO orderDTO, TenantDeliveryFeeRule tenantDeliveryFeeRule) {
        DeliveryFeeResultDTO deliveryFeeResultDTO = new DeliveryFeeResultDTO();
        deliveryFeeResultDTO.setDeliveryFee(BigDecimal.ZERO);
        return deliveryFeeResultDTO;
    }

}
