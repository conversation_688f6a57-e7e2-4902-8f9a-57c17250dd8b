package com.cosfo.ordercenter.service.biz.delivery.warehouse.executor;

import com.cosfo.ordercenter.client.common.MerchantDeliveryFeeRuleEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.service.biz.delivery.RefundDeliveryFeeService;
import com.cosfo.ordercenter.service.biz.delivery.ruletype.executor.MerchantDeliveryRuleTypeContext;
import com.cosfo.ordercenter.service.constant.DeliveryConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 18:49
 * @Description:
 */
@Service
public class ThreePartiesWarehouseExecutor implements MerchantDeliveryWarehouseExecutor {
    @Resource
    private MerchantDeliveryRuleTypeContext ruleTypeContext;
    @Resource
    private DeliveryFeeService deliveryFeeService;
    @Resource
    private RefundDeliveryFeeService refundDeliveryFeeService;

    @Override
    public List<WarehouseTypeEnum> warehouseType() {
        return Collections.singletonList(WarehouseTypeEnum.THREE_PARTIES);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateDeliveryFee(DeliveryTotalDTO deliveryDTO, List<MerchantDeliveryRuleInfoDTO> ruleList) {
        Integer ruleType = ruleList.get(0).getRuleType();
        return ruleTypeContext.load(ruleType).calculateRuleTypeDeliveryFee(deliveryDTO, ruleList);
    }

    @Override
    public MerchantDeliveryFeeSnapshotDTO calculateRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleInputs, Order orderDTO) {
        List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS = deliveryFeeService.queryAllDeliveryRule(MerchantDeliveryRuleQueryReq.builder()
            .tenantId(orderAfterSaleInputs.get(0).getTenantId())
            .warehouseType(orderDTO.getWarehouseType())
            .build());
        if (MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.DAILY.getCode().equals(deliveryRuleInfoDTOS.get(0).getRuleType())) {
            // 三方仓&每日运费，计算退运费
            return ruleTypeContext.load(MerchantDeliveryFeeRuleEnum.MerchantDeliveryFeeRuleTypeEnum.DAILY.getCode())
                .calculateRefundThreeDailyDelivery(orderAfterSaleInputs, deliveryRuleInfoDTOS, orderDTO);
        } else {
            return MerchantDeliveryFeeSnapshotDTO.builder()
                .scene(DeliveryConstant.SCENE_NOT_SEND_AFTER_SALE)
                .orderId(orderDTO.getId())
                .deliveryFee(refundDeliveryFeeService.calculateNormalDeliveryFee(orderAfterSaleInputs, orderDTO.getDeliveryFee()))
                .build();
        }
    }
}
