package com.cosfo.ordercenter.service.biz.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/23 11:31
 * @Description: 多个订单合并后对象
 */
@Data
public class MergeOrderInfoDTO {

    /**
     * sku_id
     */
    private Long skuId;

    /**
     * 供应方sku_id
     */
    private Long supplierSkuId;

    /**
     * 订单项ID
     */
    private List<Long> orderItemIds;

    /**
     * 订单商品项总量
     */
    private Integer itemTotalCount;

    /**
     * 订单商品项总价格
     */
    private BigDecimal itemTotalPrice;

    /**
     * 订单商品项总重量
     */
    private BigDecimal totalWeight;

}
