package com.cosfo.ordercenter.service.biz.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @description: 售后服务方
 * @author: <PERSON>
 * @date: 2023-12-21
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderAfterSaleServerProviderAuditDTO {

    /**
     * 仓库类型
     * @see com.cosfo.ordercenter.client.common.WarehouseTypeEnum
     */
    private Integer warehouseType;

    /**
     * 货品类型
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;

    /**
     * 审核标识
     * @see com.cosfo.ordercenter.client.common.AuditFlagEnum
     */
    private Integer auditStatus;

    /**
     * 责任方
     * @see com.cosfo.ordercenter.client.common.ResponsibilityTypeEnum
     */
    private Integer responsibilityType;

    /**
     * 租户Id
     */
    private Long tenantId;
}
