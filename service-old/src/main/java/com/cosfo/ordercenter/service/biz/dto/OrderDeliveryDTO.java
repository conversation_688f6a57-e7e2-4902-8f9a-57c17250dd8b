package com.cosfo.ordercenter.service.biz.dto;

import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 11:19
 * @Description:
 */
@Data
public class OrderDeliveryDTO extends Order {
    /**
     * 下单地址
     */
    private OrderAddress orderAddress;

    /**
     * 供应商租户id
     */
    private Long supplyTenantId;

    /**
     * 订单商品明细
     */
    private List<OrderItemDeliveryDTO> orderItemDTOList;

    /**
     * 订单城市
     */
    private String city;

    /**
     * 订单区域
     */
    private String area;

    /**
     * 订单备注
     */
    private String remark;

}
