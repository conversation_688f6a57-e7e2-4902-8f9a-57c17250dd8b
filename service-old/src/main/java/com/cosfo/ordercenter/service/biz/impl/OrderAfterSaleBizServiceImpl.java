package com.cosfo.ordercenter.service.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.manage.client.order.aftersale.OrderAfterSaleProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleEnableApplyReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleMutateService;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.aftersale.executor.OrderAfterSaleExecutorContext;
import com.cosfo.ordercenter.service.biz.dto.OrderAfterSaleServerProviderAuditDTO;
import com.cosfo.ordercenter.service.constant.NumberConstant;
import com.cosfo.ordercenter.service.exception.OpenApiProviderException;
import com.cosfo.ordercenter.service.exception.code.OpenApiErrorCode;
import com.cosfo.ordercenter.service.util.AssertParam;
import com.cosfo.ordercenter.service.util.RpcResponseUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAfterSaleBizServiceImpl implements OrderAfterSaleBizService {

    private final static List<Integer> AFTER_SALE_PROCESS_STATUS = Lists.newArrayList(
        OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(),
        OrderAfterSaleStatusEnum.REFUNDING.getValue(),
        OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(),
        OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
        OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(),
        OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue());

    // 不包括【已拒绝】、【已取消】
    private static final List<Integer> ALL_AFTER_SALE_STATUS =
            Arrays.asList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(), OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                    OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                    OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(), OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue()
                    );

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private OrderAfterSaleMutateService orderAfterSaleMutateService;
    @Resource
    private OrderAfterSaleExecutorContext orderAfterSaleExecutorContext;
    @DubboReference
    private OrderAfterSaleProvider orderAfterSaleProvider;

    @Override
    public boolean validationIsNeedRefundDeliveryFee(List<OrderAfterSale> orderAfterSaleList) {
        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setTenantId(orderAfterSaleList.get(0).getTenantId());
        req.setOrderIds(Collections.singletonList(orderAfterSaleList.get(0).getOrderId()));
        req.setStatusList(OrderAfterSaleStatusEnum.getAppliedStatusList());
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryList(req);
        // 如果有之前运费算上的  则不算运费
        boolean flag = orderAfterSales.stream().anyMatch(el -> Optional.ofNullable(el.getDeliveryFee())
            .orElse(BigDecimal.ZERO)
            .compareTo(BigDecimal.ZERO) > 0);
        if (flag) {
            return Boolean.FALSE;
        }
        orderAfterSales.addAll(orderAfterSaleList);
        boolean needRefundDeliveryFee = true;
        // 获取订单号
        Long orderId = orderAfterSaleList.get(0).getOrderId();
        Long tenantId = orderAfterSaleList.get(0).getTenantId();
        // 查询所有订单项
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(orderId);
        Map<Long, Integer> orderItemApplyAmountMap = orderAfterSales.stream()
            .collect(Collectors.groupingBy(OrderAfterSale::getOrderItemId, Collectors.summingInt(OrderAfterSale::getAmount)));

        // 判断是否所有订单项已退款
        for (OrderItem orderItem : orderItems) {
            Integer applyTotalAmount = orderItemApplyAmountMap.get(orderItem.getId());
            if (Objects.isNull(applyTotalAmount) || !Objects.equals(applyTotalAmount, orderItem.getAmount())) {
                needRefundDeliveryFee = false;
                break;
            }
        }
        return needRefundDeliveryFee;
    }

    @Override
    public void orderAfterSaleDataCheck(OrderAfterSaleDTO orderAfterSaleDTO, Order order) {
        // 参数基础校验
        basicCreateAfterSaleDataCheck(orderAfterSaleDTO);

        Long orderItemId = orderAfterSaleDTO.getOrderItemId();
        OrderItem orderItem = orderItemDao.getById(orderItemId);

        // 配送前售后校验
        notSendCheck(orderAfterSaleDTO, orderItem);

        // 校验数量和金额


        OrderAfterSaleEnableApplyReq enableApplyReq = new OrderAfterSaleEnableApplyReq();
        enableApplyReq.setOrderItemId(orderItemId);
        enableApplyReq.setOrderId(orderItem.getOrderId());
        enableApplyReq.setTenantId(orderItem.getTenantId());
        Map<Long, OrderAfterSaleEnableDTO> enableMap = RpcResponseUtil.handler(orderAfterSaleQueryService.queryEnableApply(enableApplyReq));
        OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableMap.get(orderItemId);
        OrderItemSnapshot orderItemSnapshotEntity = orderItemSnapshotDao.queryByOrderItemId(orderItemId);

        // 校验订单子项金额、数量信息，并返回自动完成时间
        checkOrderItem(orderAfterSaleDTO, order, orderItem, orderAfterSaleEnableDTO, orderItemSnapshotEntity);
    }

    @Override
    public void orderAfterSaleDataCheckForOneOrder(List<OrderAfterSaleDTO> afterSaleDTOList, Order order, List<OrderItem> itemList) {
        // 仅支持单个订单
        Set<Long> orderIdSet = afterSaleDTOList.stream().map(OrderAfterSaleDTO::getOrderId).collect(Collectors.toSet());
        if (orderIdSet.size() != 1) {
            throw new BizException("仅支持单个订单售后信息校验");
        }
        if (!orderIdSet.contains(order.getId())) {
            throw new BizException("订单和售后单信息不匹配");
        }

        Set<Long> orderItemIds = afterSaleDTOList.stream().map(OrderAfterSaleDTO::getOrderItemId).collect(Collectors.toSet());
        Map<Long, OrderItem> orderItemMap = itemList.stream().collect(Collectors.toMap(OrderItem::getId, orderItem -> orderItem));

        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(Lists.newArrayList(orderItemIds));
        Map<Long, OrderItemSnapshot> snapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, orderItemSnapshot -> orderItemSnapshot));

        Map<Long, OrderAfterSaleEnableDTO> enableMap = queryEnableApplyInner(itemList, orderItemSnapshots, order);

        for (OrderAfterSaleDTO afterSaleDTO : afterSaleDTOList) {
            Long orderItemId = afterSaleDTO.getOrderItemId();

            basicCreateAfterSaleDataCheck(afterSaleDTO);
            OrderItem orderItem = orderItemMap.get(orderItemId);
            // 配送前售后校验
            notSendCheck(afterSaleDTO, orderItem);

            OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableMap.get(orderItemId);
            OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItemId);
            checkOrderItem(afterSaleDTO, order, orderItem, orderAfterSaleEnableDTO, orderItemSnapshot);
//
//            boolean refundFlag = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSaleDTO.getServiceType(), afterSaleDTO.getAfterSaleType());
//            OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItemId);
//
//            Integer applyQuantity = refundFlag ? afterSaleDTO.getAmount() : afterSaleDTO.getAmount() * orderItemSnapshot.getMaxAfterSaleAmount();
//            log.info("订单项目id：{}，可售后数量为：{}，可售后金额为：{}", afterSaleDTO.getOrderItemId(), enableApplyQuantity, enableApplyPrice);
//
//            if (enableApplyQuantity < applyQuantity) {
//                throw new BizException("可售后件数不足");
//            }
//            if (enableApplyPrice.compareTo(afterSaleDTO.getApplyPrice()) < 0) {
//                throw new BizException("可售后金额不足，当前最大售后金额为" + enableApplyPrice + "元");
//            }
//
//            String reqSource = afterSaleDTO.getReqSource();
//            // 是否校验售后过期时间，只有小程序发起校验
//            boolean checkAfterSaleExpiryTimeFlag = StringUtils.isBlank(reqSource) || reqSource.equals("mall");
//
//            // 校验售后时间
//            if (checkAfterSaleExpiryTimeFlag && Objects.nonNull(orderItem.getAfterSaleExpiryTime()) && orderItem.getAfterSaleExpiryTime().compareTo(LocalDateTime.now()) < NumberConstant.ZERO) {
//                throw new BizException("可发起售后时间已过");
//            }
        }
    }

    private static void afterSaleParamCheck(OrderAfterSaleDTO afterSaleDTO) {
        AssertParam.notNull(afterSaleDTO.getOrderItemId(), "订单项Id不能为空");
        AssertParam.notNull(afterSaleDTO.getAmount(), "售后数量不能为空");
        AssertParam.notNull(afterSaleDTO.getAfterSaleType(), "售后类型不能为空");
        AssertParam.notNull(afterSaleDTO.getApplyPrice(), "申请金额不能为空");
        AssertParam.notNull(afterSaleDTO.getReason(), "售后原因不能为空");
        AssertParam.notNull(afterSaleDTO.getServiceType(), "售后类型不能为空");
        if (afterSaleDTO.getAfterSaleType().equals(OrderAfterSaleTypeEnum.DELIVERED.getType())) {
            AssertParam.notNull(afterSaleDTO.getProofPicture(), "售后凭证照片不能为空");
        }
    }


    /**
     * 校验订单子项信息，并返回自动完成时间
     * @param orderAfterSaleDTO
     * @param order
     * @param orderItem
     * @param orderAfterSaleEnableDTO
     * @param orderItemSnapshotEntity
     * @return
     */
    private void checkOrderItem(OrderAfterSaleDTO orderAfterSaleDTO, Order order, OrderItem orderItem, OrderAfterSaleEnableDTO orderAfterSaleEnableDTO, OrderItemSnapshot orderItemSnapshotEntity) {
        Integer enableApplyQuantity = orderAfterSaleEnableDTO.getEnableApplyQuantity();
        BigDecimal enableApplyPrice = orderAfterSaleEnableDTO.getEnableApplyPrice();
        boolean refundFlag = OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(orderAfterSaleDTO.getServiceType(), orderAfterSaleDTO.getAfterSaleType());
        Integer applyQuantity = refundFlag ? orderAfterSaleDTO.getAmount() : orderAfterSaleDTO.getAmount() * orderItemSnapshotEntity.getMaxAfterSaleAmount();
        log.info("订单项目id：{}，可售后数量为：{}，可售后金额为：{}", orderAfterSaleDTO.getOrderItemId(), enableApplyQuantity, enableApplyPrice);

        if (enableApplyQuantity < applyQuantity) {
            log.info("checkOrderItem可售后件数不足, orderAfterSaleDTO:{},orderAfterSaleEnableDTO:{}", JSON.toJSONString(orderAfterSaleDTO), JSON.toJSONString(orderAfterSaleEnableDTO));
            throw new BizException("可售后件数不足");
        }
        if (enableApplyPrice.compareTo(orderAfterSaleDTO.getApplyPrice()) < 0) {
            log.info("checkOrderItem可售后金额不足, orderAfterSaleDTO:{},orderAfterSaleEnableDTO:{}", JSON.toJSONString(orderAfterSaleDTO), JSON.toJSONString(orderAfterSaleEnableDTO));
            throw new BizException("可售后金额不足，当前最大售后金额为" + enableApplyPrice + "元");
        }

//        orderAfterSaleQueryService.verifyApplyPriceAndAmountWhenCreate(verifyInput);

        String reqSource = orderAfterSaleDTO.getReqSource();
        // 是否校验售后过期时间，只有小程序发起校验
        boolean checkAfterSaleExpiryTimeFlag = StringUtils.isBlank(reqSource) || reqSource.equals("mall");

        // 校验售后时间
        if (checkAfterSaleExpiryTimeFlag && Objects.nonNull(orderItem.getAfterSaleExpiryTime()) && orderItem.getAfterSaleExpiryTime().compareTo(LocalDateTime.now()) < NumberConstant.ZERO) {
            log.info("checkOrderItem可发起售后时间已过, orderAfterSaleDTO:{},orderAfterSaleEnableDTO:{}", JSON.toJSONString(orderAfterSaleDTO), JSON.toJSONString(orderAfterSaleEnableDTO));
            throw new BizException("可发起售后时间已过");
        }
//        if (null != order.getAutoFinishedTime()) {
//            return LocalDateTime.now().plusHours(order.getAutoFinishedTime() * 24);
//        } else {
//            log.info("checkOrderItem订单无自动完成时间, orderAfterSaleDTO:{}", JSON.toJSONString(orderAfterSaleDTO));
//            throw new BizException("订单无自动完成时间");
//        }
    }

    /**
     * 配送前售后校验
     * @param orderAfterSaleDTO
     * @param orderItem
     */
    private void notSendCheck(OrderAfterSaleDTO orderAfterSaleDTO, OrderItem orderItem) {
        if (orderAfterSaleDTO.getAfterSaleType().equals(OrderAfterSaleTypeEnum.NOT_SEND.getType())) {
            // todo 配送前售后支持部分数量申请
//            if (orderAfterSaleDTO.getAmount().compareTo(orderItem.getAmount()) != NumberConstant.ZERO) {
//                throw new BizException("配送前售后商品必须全部申请售后处理");
//            }
            if (!Objects.equals(orderAfterSaleDTO.getServiceType(), OrderAfterSaleServiceTypeEnum.REFUND.getValue()) && !Objects.equals(orderAfterSaleDTO.getServiceType(), OrderAfterSaleServiceTypeEnum.REFUND_ENTER_BILL.getValue()) && !Objects.equals(orderAfterSaleDTO.getServiceType(), OrderAfterSaleServiceTypeEnum.BALANCE.getValue())) {
                throw new BizException("配送前售后商品只可以发起退款");
            }
        }
    }

    /**
     * 参数基础校验
     * @param orderAfterSaleDTO
     */
    private void basicCreateAfterSaleDataCheck(OrderAfterSaleDTO orderAfterSaleDTO) {
        // 换货可能为空
        if (orderAfterSaleDTO.getApplyPrice() == null) {
            orderAfterSaleDTO.setApplyPrice(BigDecimal.ZERO);
        }
        AssertParam.notNull(orderAfterSaleDTO.getOrderItemId(), "订单项Id不能为空");
        AssertParam.notNull(orderAfterSaleDTO.getAmount(), "售后数量不能为空");
        AssertParam.notNull(orderAfterSaleDTO.getAfterSaleType(), "售后类型不能为空");
        AssertParam.notNull(orderAfterSaleDTO.getApplyPrice(), "申请金额不能为空");
        AssertParam.notNull(orderAfterSaleDTO.getReason(), "售后原因不能为空");
        AssertParam.notNull(orderAfterSaleDTO.getServiceType(), "售后类型不能为空");
        if (orderAfterSaleDTO.getAfterSaleType().equals(OrderAfterSaleTypeEnum.DELIVERED.getType())) {
            AssertParam.notNull(orderAfterSaleDTO.getProofPicture(), "售后凭证照片不能为空");
        }
    }

    @Override
    public void checkDataAndBuildDto(List<OrderAfterSaleDTO> orderAfterSaleDTOS, Order order) {
        List<Long> orderItemIdList = orderAfterSaleDTOS.stream().map(OrderAfterSaleDTO::getOrderItemId).collect(Collectors.toList());
        List<OrderItem> orderItems = orderItemDao.listByIds(orderItemIdList);
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, Function.identity(), (v1, v2) -> v1));
        for (OrderAfterSaleDTO orderAfterSaleDTO : orderAfterSaleDTOS) {
            // 售后单常规校验
            basicCreateAfterSaleDataCheck(orderAfterSaleDTO);
            notSendCheck(orderAfterSaleDTO, orderItemMap.get(orderAfterSaleDTO.getOrderItemId()));
            AssertParam.expectTrue(Objects.nonNull(orderAfterSaleDTO.getApplyPrice()) && orderAfterSaleDTO.getApplyPrice().compareTo(ZERO) >= 0, "售后单申请金额不能小于0", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
        }

        // 批量查询可售后信息
        OrderAfterSaleDTO tempDto = orderAfterSaleDTOS.get(NumberConstant.ZERO);
        OrderAfterSaleEnableApplyParam enableApplyParam = new OrderAfterSaleEnableApplyParam();
        enableApplyParam.setOrderItemIds(orderItemIdList);
        enableApplyParam.setOrderId(tempDto.getOrderId());
        enableApplyParam.setTenantId(tempDto.getTenantId());
        Map<Long, OrderAfterSaleEnableDTO> enableMap = queryEnableApplyByApplyParam(enableApplyParam);
        // 批量查询订单子项快照列表
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIdList);
        Map<Long, OrderItemSnapshot> orderItemSnapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, Function.identity(), (v1, v2) -> v1));

        for (OrderAfterSaleDTO orderAfterSaleDTO : orderAfterSaleDTOS) {
            Long orderItemId = orderAfterSaleDTO.getOrderItemId();
            OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableMap.get(orderItemId);
            OrderItem orderItem = orderItemMap.get(orderAfterSaleDTO.getOrderItemId());
            OrderItemSnapshot orderItemSnapshotEntity = orderItemSnapshotMap.get(orderItemId);
            if (Objects.isNull(orderItemSnapshotEntity)) {
                log.error("订单信息异常，异常订单子项orderItemId：{}， orderAfterSaleDTOS：{},orderItemSnapshotMap:{}", orderItemId, JSON.toJSONString(orderAfterSaleDTOS), JSON.toJSONString(orderItemSnapshotMap));
                throw new OpenApiProviderException(orderAfterSaleDTO.getCustomerAfterSaleOrderNo() + "所属订单信息异常，请稍后再试", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            }
            // 校验订单子项金额、数量信息，并返回自动完成时间
            orderAfterSaleDTO.setAutoFinishedTime(LocalDateTime.now().plusDays(order.getAutoFinishedTime()));
            checkOrderItem(orderAfterSaleDTO, order, orderItem, orderAfterSaleEnableDTO, orderItemSnapshotEntity);

        }
    }

    @Override
    public Boolean reviewReject(OrderAfterSaleAuditReq req, OrderAfterSale orderAfterSale) {
        OrderAfterSale update = new OrderAfterSale();
        update.setId(req.getOrderAfterSaleId());
        update.setStatus(OrderAfterSaleStatusEnum.AUDITED_FAILED.getValue());
        update.setOperatorName(req.getOperatorName());
        if (StringUtils.isNotBlank(req.getResponsibilityType())) {
            update.setResponsibilityType(NumberUtils.toInt(req.getResponsibilityType(), 0));
        }
        if (OrderAfterSaleStatusEnum.WAIT_REFUND.getValue().equals(orderAfterSale.getStatus())) {
            update.setSecondHandleRemark(req.getHandleRemark());
            update.setUpdateTime(LocalDateTime.now());
        } else {
            update.setHandleRemark(req.getHandleRemark());
            update.setHandleTime(LocalDateTime.now());
        }
        return orderAfterSaleDao.updateById(update);
    }

    @Override
    public void createAfterSaleOrderIfNeed(List<OrderAfterSaleProcessFinishReq> reqs, Integer serviceType) {
        // 不缺货，直接return
        if (CollectionUtils.isEmpty(reqs)) {
            return;
        }
        boolean hasShortCut = reqs.stream().anyMatch(afterSaleProcessFinishReq -> afterSaleProcessFinishReq.getShortCount() > NumberConstant.ZERO);
        if (!hasShortCut) {
            return;
        }
        List<String> afterSaleNoList = reqs.stream().map(OrderAfterSaleProcessFinishReq::getOrderAfterSaleNo).distinct().collect(Collectors.toList());
        createAfterSaleOrderConsumer.accept(reqs);

    }

    @Override
    public void createAfterSaleOrderForClose(List<Order> needCloseOrderList) {
        for (Order closeOrder : needCloseOrderList) {
            List<OrderItem> orderItems = orderItemDao.queryByOrderId(closeOrder.getId());
            OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
            afterSaleQueryReq.setOrderIds(Lists.newArrayList(closeOrder.getId()));
            afterSaleQueryReq.setStatusList(AFTER_SALE_PROCESS_STATUS);
            List<OrderAfterSaleDTO> existAfterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryList(afterSaleQueryReq));
            // 创建售后单
            Map<Long, Integer> afterSaleApplyMap = existAfterSaleDTOList.stream().collect(Collectors.groupingBy(OrderAfterSaleDTO::getOrderItemId, Collectors.summingInt(OrderAfterSaleDTO::getAmount)));

            Integer serviceType = OrderAfterSaleServiceTypeEnum.getServiceTypeByPayType(closeOrder.getPayType(), OrderAfterSaleTypeEnum.NOT_SEND.getType());

            List<OrderAfterSaleDTO> applyAfterSaleList = new ArrayList<>();
            for (OrderItem orderItem : orderItems) {
                int applyQuantity = orderItem.getAmount() - afterSaleApplyMap.getOrDefault(orderItem.getId(), 0);
                if (applyQuantity > 0) {
                    OrderAfterSaleDTO afterSaleDTO = new OrderAfterSaleDTO();
                    afterSaleDTO.setOrderItemId(orderItem.getId());
                    afterSaleDTO.setAmount(applyQuantity);
                    afterSaleDTO.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
                    afterSaleDTO.setServiceType(serviceType);
                    BigDecimal applyPrice = NumberUtil.mul(orderItem.getPayablePrice(), applyQuantity);
                    afterSaleDTO.setApplyPrice(applyPrice);
                    afterSaleDTO.setReason("关闭订单");
                    afterSaleDTO.setUserRemark("关闭订单");
                    afterSaleDTO.setOrderId(closeOrder.getId());
                    afterSaleDTO.setTenantId(closeOrder.getTenantId());
                    afterSaleDTO.setStoreId(closeOrder.getStoreId());
                    afterSaleDTO.setAccountId(closeOrder.getAccountId());
                    afterSaleDTO.setTotalPrice(applyPrice);
                    afterSaleDTO.setWarehouseType(closeOrder.getWarehouseType());
                    applyAfterSaleList.add(afterSaleDTO);
                }
            }
            orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(applyAfterSaleList);
        }
    }

    @Override
    public void autoReviewSubmissionsForClose(List<Order> needCloseOrder) {
        // 无仓&自营仓自动关闭
        for (Order closeOrder : needCloseOrder) {
            if (WarehouseTypeEnum.THREE_PARTIES.getCode().equals(closeOrder.getWarehouseType())) {
                continue;
            }
            OrderAfterSaleQueryReq afterSaleQueryReq = new OrderAfterSaleQueryReq();
            afterSaleQueryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue()));
            afterSaleQueryReq.setOrderIds(Lists.newArrayList(closeOrder.getId()));
            List<OrderAfterSaleDTO> handleAfterSaleList = RpcResultUtil.handle(orderAfterSaleQueryService.queryList(afterSaleQueryReq));

            for (OrderAfterSaleDTO afterSaleDTO : handleAfterSaleList) {
                OrderAfterSaleAuditReq auditReq = new OrderAfterSaleAuditReq();
                auditReq.setAfterSaleOrderNo(afterSaleDTO.getAfterSaleOrderNo());
                auditReq.setAmount(afterSaleDTO.getAmount());
                auditReq.setAuditStatus(AuditFlagEnum.AUDIT_SUCCESS.getStatus());
                auditReq.setHandleRemark("关闭订单");
                auditReq.setTotalPrice(afterSaleDTO.getApplyPrice());
                auditReq.setOperatorName("SYSTEM");

                orderAfterSaleExecutorContext.load(afterSaleDTO.getServiceType()).reviewSuccess(auditReq);
            }
        }
    }

    /**
     * 处理所有订单的售后生成函数
     */
    public Consumer<List<OrderAfterSaleProcessFinishReq>> createAfterSaleOrderConsumer = new Consumer<List<OrderAfterSaleProcessFinishReq>>() {
        @Override
        public void accept(List<OrderAfterSaleProcessFinishReq> list) {
            List<OrderAfterSaleDTO> addList = Lists.newArrayList();
            Map<String, List<OrderAfterSaleProcessFinishReq>> afterSaleOrderDeliveryMap = list.stream().collect(Collectors.groupingBy(OrderAfterSaleProcessFinishReq::getOrderAfterSaleNo));

            for (Map.Entry<String, List<OrderAfterSaleProcessFinishReq>> entry : afterSaleOrderDeliveryMap.entrySet()) {
                String afterSaleNo = entry.getKey();
                List<OrderAfterSaleProcessFinishReq> dtos = entry.getValue();
//                OrderAfterSale orderAfterSale = afterSaleService.selectByAfterSaleOrderNo(afterSaleNo);
                List<OrderAfterSaleDTO> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryService.queryByNos(Lists.newArrayList(afterSaleNo)));
                if (CollectionUtils.isEmpty(afterSaleDTOList)) {
                    log.error("{}未查询到该售后单:{},不生成补货单", "自动生成补货单", afterSaleNo);
                    continue;
                }
                if (CollectionUtils.isEmpty(dtos)) {
                    log.error("{}该售后单:{}回收信息为空,不生成补货单", "自动生成补货单", afterSaleNo);
                    continue;
                }

                // 非配送异常,不处理
                Optional<OrderAfterSaleProcessFinishReq> deliveryErrorOptional = dtos.stream().filter(dto -> Objects.equals(dto.getState(), DeliveryStateEnum.ABNORMAL.getState()) && (Objects.equals(dto.getDeliveryType(), DeliveryTypeEnum.DELIVERY.getType()))).findFirst();
                if (!deliveryErrorOptional.isPresent()) {
                    continue;
                }
                OrderAfterSaleDTO afterSaleDTO = afterSaleDTOList.get(0);
                addList.addAll(builderOrderAfterSaleList(afterSaleDTO, deliveryErrorOptional.get()));
            }
            // 处理待生成售后单信息
            if (CollectionUtils.isEmpty(addList)) {
                return;
            }

            for (OrderAfterSaleDTO orderAfterSaleDto : addList) {
                try {
                    RpcResultUtil.handle(orderAfterSaleMutateService.createAfterDeliveryAfterSale(orderAfterSaleDto));
                } catch (Exception e) {
                    log.error("{}自动发起售后失败,参数orderAfterSaleDto:{},message{},e", "自动生成补货单", JSON.toJSONString(orderAfterSaleDto), e.getMessage(), e);
                }
            }
        }

    };

    /**
     * 构建售后单信息
     *
     * @param orderAfterSale
     * @param afterSaleProcessFinishReq
     * @return
     */
    private List<OrderAfterSaleDTO> builderOrderAfterSaleList(OrderAfterSaleDTO orderAfterSale, OrderAfterSaleProcessFinishReq afterSaleProcessFinishReq) {
        List<OrderAfterSaleDTO> newOrderAfterSaleList = Lists.newArrayList();
        // 不缺货不管
        if (afterSaleProcessFinishReq.getShortCount() <= NumberConstant.ZERO) {
            return newOrderAfterSaleList;
        }
//        OrderItem orderItem = orderItemMapper.selectByPrimaryKey(orderAfterSale.getOrderItemId());
        OrderItem orderItem = orderItemDao.getById(orderAfterSale.getOrderItemId());
//        OrderItemDTO orderItemDTO = RpcResultUtil.handle(orderItemQueryService.queryById(orderAfterSale.getOrderItemId()));
        if (Objects.isNull(orderItem)) {
            log.error("{}该售后单:{}未找到对应orderItem信息,不生成补货单", "自动生成补货单", orderAfterSale.getAfterSaleOrderNo());
            return newOrderAfterSaleList;
        }

        // 退货数量
        Integer afterSaleAmount = afterSaleProcessFinishReq.getShortCount();
        OrderAfterSaleDTO afterSaleCreateDTO = new OrderAfterSaleDTO();
        afterSaleCreateDTO.setOrderItemId(orderAfterSale.getOrderItemId());
        afterSaleCreateDTO.setAmount(afterSaleAmount);
        afterSaleCreateDTO.setOrderId(orderItem.getOrderId());
        afterSaleCreateDTO.setTenantId(orderItem.getTenantId());
        afterSaleCreateDTO.setAfterSaleType(OrderAfterSaleTypeEnum.DELIVERED.getType());
        Integer serviceType = OrderAfterSaleServiceTypeEnum.RESEND.getValue();
        afterSaleCreateDTO.setServiceType(serviceType);
        // 不需要金额
        afterSaleCreateDTO.setApplyPrice(ZERO);
        afterSaleCreateDTO.setReason("缺货");
        afterSaleCreateDTO.setProofPicture(org.apache.commons.lang3.StringUtils.EMPTY);
        newOrderAfterSaleList.add(afterSaleCreateDTO);
        return newOrderAfterSaleList;
    }

    @Override
    public Map<Long, OrderAfterSaleEnableDTO> queryEnableApplyByApplyParam(OrderAfterSaleEnableApplyParam orderAfterSaleEnableApplyParam) {
        Long orderId = orderAfterSaleEnableApplyParam.getOrderId();
        Order order = orderDao.getById(orderId);
        if (order == null) {
            log.warn("找到不到应订单:{}", orderId);
            return Collections.emptyMap();
        }
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(orderId);
        if (CollectionUtil.isNotEmpty(orderAfterSaleEnableApplyParam.getOrderItemIds())) {
            orderItems = orderItems.stream().filter(o -> orderAfterSaleEnableApplyParam.getOrderItemIds().contains(o.getId())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(orderItems)) {
            log.warn("订单未找到订单项:{}", orderId);
            return Collections.emptyMap();
        }

        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItems.stream().map(OrderItem::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orderItemSnapshots)) {
            log.warn("订单未找到订单快照:{}", orderId);
            return Collections.emptyMap();
        }
        Map<Long, OrderItemSnapshot> snapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, s -> s));
        Map<String, Integer> itemFullfillMap = getItemFullfillMap(order);
        Map<Long, List<OrderAfterSale>> afterSaleMap = getAfterSaleMap(orderId);
        Map<Long, OrderAfterSaleEnableDTO> result = Maps.newHashMapWithExpectedSize(orderItems.size());
        for (OrderItem orderItem : orderItems) {
            String orderItemIdStr = String.valueOf(orderItem.getItemId());
            Integer orderItemAmount = orderItem.getAmount();
            OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItem.getId());
            if (!Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())
                    && OrderStatusEnum.ableApplyDeliveredAfterSale(order.getStatus())) {
                orderItemAmount = 0;
                if (itemFullfillMap.containsKey(orderItemIdStr)) {
                    orderItemAmount = itemFullfillMap.get(orderItemIdStr);
                }
            }

            OrderAfterSaleEnableDTO enableApplyDTO = new OrderAfterSaleEnableDTO();
            result.put(orderItem.getId(), enableApplyDTO);

            Integer maxAfterSaleAmount = orderItemSnapshot.getMaxAfterSaleAmount();
            enableApplyDTO.setEnableApplyAmount(orderItemAmount);
            enableApplyDTO.setEnableApplyQuantity(orderItemAmount * maxAfterSaleAmount);
            enableApplyDTO.setEnableApplyPrice(orderItem.getTotalPrice());
            // 查询出处理中、成功的售后订单
            List<OrderAfterSale> afterSales = afterSaleMap.get(orderItem.getId());
            if (CollectionUtils.isEmpty(afterSales)) {
                log.warn("订单项没有'处理中、成功'的售后单:{}", orderItem.getId());
                continue;
            }
            // 计算换货、补发成功数量 以及处理中的已到货退款
            Integer refundAmount = 0;
            Integer otherAmount = 0;
            BigDecimal appliedPrice = BigDecimal.ZERO;
            for (OrderAfterSale afterSale : afterSales) {
                Integer serviceType = afterSale.getServiceType();
                boolean exchangeAndResendFlag =
                        Objects.equals(serviceType, OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue()) ||
                                Objects.equals(serviceType, OrderAfterSaleServiceTypeEnum.RESEND.getValue());
                boolean successFlag = Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
                if (exchangeAndResendFlag && successFlag) {
                    continue;
                }
                appliedPrice = NumberUtil.add(appliedPrice, NumberUtil.sub(afterSale.getTotalPrice(), afterSale.getDeliveryFee()));
                if (OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSale.getServiceType(), afterSale.getAfterSaleType())) {
                    refundAmount += afterSale.getAmount();
                    continue;
                }
                otherAmount += afterSale.getAmount();
            }
            // 按数量 = （购买件数 - 除已到货退款外件数）* 最大售后数 - 已到货退款数
            enableApplyDTO.setEnableApplyQuantity((orderItemAmount - otherAmount) * maxAfterSaleAmount - refundAmount);
            // 按件数 = 按数量 / 最大售后数
            enableApplyDTO.setEnableApplyAmount(enableApplyDTO.getEnableApplyQuantity() / maxAfterSaleAmount);
            enableApplyDTO.setEnableApplyPrice(NumberUtil.sub(orderItem.getTotalPrice(), appliedPrice));
        }
        return result;
    }


    private Map<Long, OrderAfterSaleEnableDTO> queryEnableApplyInner(List<OrderItem> orderItems, List<OrderItemSnapshot> orderItemSnapshots, Order order) {
        Map<Long, OrderItemSnapshot> snapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, s -> s));
        Map<String, Integer> itemFullfillMap = getItemFullfillMap(order);
        Map<Long, List<OrderAfterSale>> afterSaleMap = getAfterSaleMap(order.getId());
        Map<Long, OrderAfterSaleEnableDTO> result = Maps.newHashMapWithExpectedSize(orderItems.size());
        for (OrderItem orderItem : orderItems) {
            String orderItemIdStr = String.valueOf(orderItem.getItemId());
            Integer orderItemAmount = orderItem.getAmount();
            OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItem.getId());
            if (!Objects.equals(order.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())
                    && OrderStatusEnum.ableApplyDeliveredAfterSale(order.getStatus())) {
                orderItemAmount = 0;
                if (itemFullfillMap.containsKey(orderItemIdStr)) {
                    orderItemAmount = itemFullfillMap.get(orderItemIdStr);
                }
            }

            OrderAfterSaleEnableDTO enableApplyDTO = new OrderAfterSaleEnableDTO();
            result.put(orderItem.getId(), enableApplyDTO);

            Integer maxAfterSaleAmount = orderItemSnapshot.getMaxAfterSaleAmount();
            enableApplyDTO.setEnableApplyAmount(orderItemAmount);
            enableApplyDTO.setEnableApplyQuantity(orderItemAmount * maxAfterSaleAmount);
            enableApplyDTO.setEnableApplyPrice(orderItem.getTotalPrice());
            // 查询出处理中、成功的售后订单
            List<OrderAfterSale> afterSales = afterSaleMap.get(orderItem.getId());
            if (CollectionUtils.isEmpty(afterSales)) {
                log.warn("订单项没有'处理中、成功'的售后单:{}", orderItem.getId());
                continue;
            }
            // 计算换货、补发成功数量 以及处理中的已到货退款
            Integer refundAmount = 0;
            Integer otherAmount = 0;
            BigDecimal appliedPrice = BigDecimal.ZERO;
            for (OrderAfterSale afterSale : afterSales) {
                Integer serviceType = afterSale.getServiceType();
                boolean exchangeAndResendFlag =
                        Objects.equals(serviceType, OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue()) ||
                                Objects.equals(serviceType, OrderAfterSaleServiceTypeEnum.RESEND.getValue());
                boolean successFlag = Objects.equals(afterSale.getStatus(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());
                if (exchangeAndResendFlag && successFlag) {
                    continue;
                }
                appliedPrice = NumberUtil.add(appliedPrice, NumberUtil.sub(afterSale.getTotalPrice(), afterSale.getDeliveryFee()));
                if (OrderAfterSaleServiceTypeEnum.verifyIsReceivedRefund(afterSale.getServiceType(), afterSale.getAfterSaleType())) {
                    refundAmount += afterSale.getAmount();
                    continue;
                }
                otherAmount += afterSale.getAmount();
            }
            // 按数量 = （购买件数 - 除已到货退款外件数）* 最大售后数 - 已到货退款数
            enableApplyDTO.setEnableApplyQuantity((orderItemAmount - otherAmount) * maxAfterSaleAmount - refundAmount);
            // 按件数 = 按数量 / 最大售后数
            enableApplyDTO.setEnableApplyAmount(enableApplyDTO.getEnableApplyQuantity() / maxAfterSaleAmount);
            enableApplyDTO.setEnableApplyPrice(NumberUtil.sub(orderItem.getTotalPrice(), appliedPrice));
        }
        return result;
    }


    private Map<Long, List<OrderAfterSale>> getAfterSaleMap(Long orderId) {
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setOrderIds(Lists.newArrayList(orderId));
        orderAfterSaleQueryReq.setStatusList(ALL_AFTER_SALE_STATUS);
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryList(orderAfterSaleQueryReq);
        Map<Long, List<OrderAfterSale>> afterSaleMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(orderAfterSales)) {
            afterSaleMap = orderAfterSales.stream().collect(Collectors.groupingBy(OrderAfterSale::getOrderItemId));
        }
        return afterSaleMap;
    }

    private Map<String, Integer> getItemFullfillMap(Order order) {
        QueryFulfillmentDeliveryReq deliveryReq = new QueryFulfillmentDeliveryReq();
        deliveryReq.setOrderNoList(Lists.newArrayList(order.getOrderNo()));
        List<FulfillmentDeliveryResp> deliveryList = RpcResultUtil.handle(fulfillmentOrderQueryProvider.queryOrderDelivery(deliveryReq));
        Map<String, Integer> itemFullfillMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(deliveryList)) {
            itemFullfillMap = deliveryList.stream().collect(Collectors.groupingBy(FulfillmentDeliveryResp::getItemId, Collectors.summingInt(FulfillmentDeliveryResp::getQuantity)));
        }
        return itemFullfillMap;
    }

    @Override
    public boolean checkNeedServiceProviderAudit(OrderAfterSaleServerProviderAuditDTO orderAfterSaleServerProviderAuditDTO) {
        // 代仓订单、服务商责任、审核通过、品牌方自审开启
        OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq(orderAfterSaleServerProviderAuditDTO.getTenantId());
        Boolean agentAfterSaleSelfReviewFlag = RpcResultUtil.handle(orderAfterSaleProvider.needSelfReviewFlag(req));
        Integer responsibilityType = orderAfterSaleServerProviderAuditDTO.getResponsibilityType();
        Integer auditStatus = orderAfterSaleServerProviderAuditDTO.getAuditStatus();
        Integer warehouseType = orderAfterSaleServerProviderAuditDTO.getWarehouseType();
        Integer goodsType = orderAfterSaleServerProviderAuditDTO.getGoodsType();
        return Objects.equals(responsibilityType, ResponsibilityTypeEnum.SUPPLIER.getType())
                && Objects.equals(auditStatus, AuditFlagEnum.AUDIT_SUCCESS.getFlag())
                && Objects.equals(warehouseType, WarehouseTypeEnum.THREE_PARTIES.getCode())
                && Objects.equals(goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode())
                && agentAfterSaleSelfReviewFlag;
    }
}
