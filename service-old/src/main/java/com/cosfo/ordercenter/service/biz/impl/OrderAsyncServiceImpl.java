package com.cosfo.ordercenter.service.biz.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.dao.model.dto.RefundDTO;
import com.cosfo.ordercenter.service.biz.OrderAsyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderAsyncServiceImpl implements OrderAsyncService {

    @Value("${saasmall.api-host}")
    private String apiHost;

    public static final String AFTER_SALE_REMAIN_REFUND = "/pay/refund";

    @Override
    @Async
    public void payRefund(Long id, Long orderId, Long tenantId, BigDecimal totalPrice) {
        RefundDTO refundDTO = new RefundDTO();
        refundDTO.setAfterSaleId(id);
        refundDTO.setOrderId(orderId);
        refundDTO.setTenantId(tenantId);
        refundDTO.setRefundPrice(totalPrice);
        String refundResult = HttpUtil.post(apiHost + AFTER_SALE_REMAIN_REFUND, JSON.toJSONString(refundDTO));
        log.info("支付退回响应数据 params={}", JSON.toJSONString(refundResult));
        if (refundResult == null) {
            log.info("调用退款API异常");
        }
    }
}
