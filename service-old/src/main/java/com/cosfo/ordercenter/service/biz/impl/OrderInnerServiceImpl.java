package com.cosfo.ordercenter.service.biz.impl;

import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderInnerService;
import com.cosfo.ordercenter.service.biz.OrderItemInnerService;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderInnerServiceImpl implements OrderInnerService {

    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private OrderItemInnerService orderItemInnerService;

    @Override
    public int autoFinish(OrderAutoFinishDTO autoFinishDTO) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("查询配送时间在[{}]-[{}]的待收货订单", autoFinishDTO.getDeliveryTimeStart(), autoFinishDTO.getDeliveryTimeEnd());
        // 只查id
        List<Order> orderList = orderDao.queryNeedAutoFinishedOrder(autoFinishDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("没有需要自动完成的订单");
            return 0;
        }
        List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderItem> orderItems = orderItemDao.batchQueryByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderItems)) {
            log.error("订单自动完成失败,查不到订单明细,orderIds = {}", orderIds);
            throw new ProviderException("订单明细为空");
        }
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        Map<Long, List<OrderItem>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));

        // 查询订单快照
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        if (CollectionUtils.isEmpty(orderItemSnapshots)) {
            log.error("订单自动完成失败,查不到订单明细快照,orderItemIds = {}", orderItemIds);
            throw new ProviderException("订单明细快照为空");
        }
        Map<Long, String> ruleMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, OrderItemSnapshot::getAfterSaleRule));
        List<Long> errorIds = new ArrayList<>();
        for (Order order : orderList) {
            boolean execResult = execFinish(order, orderItemMap.get(order.getId()), ruleMap);
            if (!execResult) {
                errorIds.add(order.getId());
            }
        }
        log.info("订单自动完成结束,本次执行订单={},失败订单={},spent={}", orderIds, errorIds, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return orderIds.size() - errorIds.size();
    }

    private boolean execFinish(Order order, List<OrderItem> orderItems, Map<Long, String> ruleMap) {
        try {
            transactionTemplate.execute(status -> {
                // 更新订单状态为完成
                log.info("订单{}执行自动完成", order.getId());
                if (CollectionUtils.isEmpty(orderItems)) {
                    throw new BizException("订单明细不存在");
                }
                Boolean orderUpdateResult = updateOrderFinishStatus(order.getId());
                if (!orderUpdateResult) {
                    throw new BizException("订单更新失败");
                }
                //获取订单完成时间
                Order finishOrder = orderDao.getById(order.getId());
                boolean expiryTimeUpdateResult = orderItemInnerService.batchUpdateExpiryTime(finishOrder, orderItems, ruleMap);
                if (!expiryTimeUpdateResult) {
                    throw new BizException("最后可售后时间更新失败");
                }
                return order.getId();
            });
            return true;
        } catch (Exception ex) {
            log.error("订单自动完成失败, orderId={}", order.getId(), ex);
        }
        return false;
    }

    private Boolean updateOrderFinishStatus(Long orderId) {
        OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
        updateReq.setOrderId(orderId);
        updateReq.setOriginStatus(OrderStatusEnum.DELIVERING.getCode());
        updateReq.setStatus(OrderStatusEnum.FINISHED.getCode());
        return orderDao.updateStatus(updateReq);
    }

}
