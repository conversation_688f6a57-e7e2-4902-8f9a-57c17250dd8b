package com.cosfo.ordercenter.service.biz.impl;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.resp.OrderItemAfterSaleRuleDTO;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.service.biz.OrderItemInnerService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemInnerServiceImpl implements OrderItemInnerService {
    @Resource
    private OrderItemDao orderItemDao;

    @Override
    public boolean batchUpdateExpiryTime(Order order, List<OrderItem> orderItems, Map<Long, String> ruleMap) {
        List<OrderItem> expiryTimeUpdateList = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            log.info("处理订单明细, orderItemId={}", orderItem.getId());
            // 更新订单明细最后可售后时间
            String rule = ruleMap.get(orderItem.getId());
            if (StringUtils.isEmpty(rule)) {
                throw new BizException("售后规则快照不存在");
            }
            OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = JSON.parseObject(rule, OrderItemAfterSaleRuleDTO.class);
            if (orderItemAfterSaleRuleDTO == null || orderItemAfterSaleRuleDTO.getApplyEndTime() == null) {
                throw new BizException("售后规则异常,ruleDTO=" + orderItemAfterSaleRuleDTO);
            }
            LocalDateTime afterSaleExpiryTime = order.getFinishedTime().plusHours(orderItemAfterSaleRuleDTO.getApplyEndTime());
            OrderItem update = new OrderItem();
            update.setId(orderItem.getId());
            update.setAfterSaleExpiryTime(afterSaleExpiryTime);
            expiryTimeUpdateList.add(update);
        }
        return orderItemDao.updateBatchById(expiryTimeUpdateList);
    }
}
