package com.cosfo.ordercenter.service.biz.impl;

import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderItemSnapshotService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
@Service
public class OrderItemSnapshotServiceImpl implements OrderItemSnapshotService {
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @Override
    public Map<Long, OrderItemSnapshot> queryOrderItemSnapshot(List<OrderItem> orderItems) {
        if(CollectionUtils.isEmpty(orderItems)){
            return new HashMap<>();
        }
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshot> itemSnapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, item -> item));
        return itemSnapshotMap;
    }
}
