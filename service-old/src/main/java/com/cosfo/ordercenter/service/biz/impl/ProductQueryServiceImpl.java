package com.cosfo.ordercenter.service.biz.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.service.biz.ProductQueryService;
import com.cosfo.ordercenter.service.biz.dto.AllCategoryDTO;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: monna.chen
 * @Date: 2023/9/13 14:19
 * @Description:
 */
@Service
public class ProductQueryServiceImpl implements ProductQueryService {
    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;


    @Override
    public Map<Long, AllCategoryDTO> querySkuCategory(List<Long> skuIds) {
        List<ProductSkuDetailResp> skuList = RpcResultUtil.handle(productsSkuQueryProvider.selectProductSkuDetailById(skuIds));

        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }

        Map<Long, AllCategoryDTO> skuCategoryMap = new HashMap<>();
        skuList.forEach(sku -> {
            AllCategoryDTO categoryDTO = AllCategoryDTO.builder()
                .firstCategory(sku.getFirstCategory())
                .firstCategoryId(sku.getFirstCategoryId())
                .secondCategory(sku.getSecondCategory())
                .secondCategoryId(sku.getSecondCategoryId())
                .thirdCategory(sku.getCategoryName())
                .thirdCategoryId(sku.getCategoryId())
                .build();
            skuCategoryMap.put(sku.getSkuId(), categoryDTO);
        });
        return skuCategoryMap;
    }
}
