package com.cosfo.ordercenter.service.biz.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.StockBizService;
import com.cosfo.ordercenter.service.biz.StoreService;
import com.cosfo.ordercenter.service.constant.Constant;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupyReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderOccupySkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseBySpecifySkuReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.req.OrderReleaseSkuDetailReqDTO;
import net.xianmu.inventory.client.saleinventory.dto.res.OrderReleaseResDTO;
import net.xianmu.common.exception.BizException;
import net.xianmu.inventory.client.saleinventory.SaleInventoryCenterCommandProvider;
import net.xianmu.inventory.client.saleinventory.enums.SaleStockChangeTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockBizServiceImpl implements StockBizService {

    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private StoreService storeService;

    @DubboReference
    private SaleInventoryCenterCommandProvider saleInventoryCenterCommandProvider;
    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;

    public static final Long XIANMU_TENANT_ID = 1L;


    @Override
    public boolean releaseStock(String orderNo, Long orderItemId, SaleStockChangeTypeEnum stockChangeTypeEnum, Integer amount, Long warehouseNo, Long storeId) {
        OrderItemSnapshot itemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderItemId);
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setSkuIds(Collections.singletonList(itemSnapshot.getSkuId()));
        List<ProductsMappingResp> skuMappings = RpcResultUtil.handle(productsMappingQueryProvider.selectMappingList(queryReq));
        OrderReleaseBySpecifySkuReqDTO inputDTO = new OrderReleaseBySpecifySkuReqDTO();
        inputDTO.setTenantId(itemSnapshot.getTenantId());
        inputDTO.setOrderNo(orderNo);
        inputDTO.setOperatorNo(orderNo);
        inputDTO.setIdempotentNo(orderNo);
        inputDTO.setOrderType(SaleStockChangeTypeEnum.MANUAL_CLOSED.getTypeName());
        inputDTO.setOperatorName(storeService.queryStoreName(storeId));

        OrderReleaseSkuDetailReqDTO releaseSkuDetailReqDTO = new OrderReleaseSkuDetailReqDTO();
        releaseSkuDetailReqDTO.setWarehouseNo(warehouseNo);
        releaseSkuDetailReqDTO.setSkuCode(skuMappings.get(0).getSku());
        releaseSkuDetailReqDTO.setReleaseQuantity(amount);

        inputDTO.setOrderReleaseSkuDetailReqDTOS(Lists.newArrayList(releaseSkuDetailReqDTO));
        OrderReleaseResDTO handle = RpcResultUtil.handle(saleInventoryCenterCommandProvider.orderReleaseBySpecifySku(inputDTO));
        return true;
    }

    @Override
    public boolean lockStockForAfterSale(OrderAfterSale orderAfterSale, Integer itemAmount) {
        OrderAddress orderAddress = orderAddressDao.getByOrderId(orderAfterSale.getOrderId(), orderAfterSale.getTenantId());
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderAfterSale.getOrderItemId());

        OrderOccupyReqDTO orderOccupyReqDTO = new OrderOccupyReqDTO();
        orderOccupyReqDTO.setTenantId(orderAfterSale.getTenantId());
        orderOccupyReqDTO.setWarehouseTenantId(XIANMU_TENANT_ID);
        orderOccupyReqDTO.setOrderNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setOrderType(SaleStockChangeTypeEnum.SAAS_AFTER_SALE.getTypeName());
        orderOccupyReqDTO.setOperatorNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setIdempotentNo(orderAfterSale.getAfterSaleOrderNo());
        orderOccupyReqDTO.setProvince(orderAddress.getProvince());
        orderOccupyReqDTO.setCity(orderAddress.getCity());
        orderOccupyReqDTO.setArea(orderAddress.getArea());
        orderOccupyReqDTO.setAddress(orderAddress.getAddress());
        orderOccupyReqDTO.setPoi(orderAddress.getPoiNote());
        orderOccupyReqDTO.setOperatorName(storeService.queryStoreName(orderAfterSale.getStoreId()));

        // 商品明细
        List<OrderOccupySkuDetailReqDTO> orderOccupySkuDetailReqDTOS = Lists.newArrayList();
        orderOccupyReqDTO.setOrderOccupySkuDetailReqDTOS(orderOccupySkuDetailReqDTOS);

        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setSkuIds(Collections.singletonList(orderItemSnapshot.getSkuId()));
        List<ProductsMappingResp> skuMappings = RpcResultUtil.handle(productsMappingQueryProvider.selectMappingList(queryReq));
        if (CollectionUtils.isEmpty(skuMappings) || StringUtils.isEmpty(skuMappings.get(0).getSku())) {
            throw new BizException("找不到SkuCode");
        }
        OrderOccupySkuDetailReqDTO orderOccupySkuDetailReqDTO = new OrderOccupySkuDetailReqDTO();
        orderOccupySkuDetailReqDTO.setSkuCode(skuMappings.get(0).getSku());
        orderOccupySkuDetailReqDTO.setOccupyQuantity(itemAmount);
        orderOccupySkuDetailReqDTO.setSkuTenantId(GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(orderItemSnapshot.getGoodsType()) ? Constant.XIANMU_TENANT_ID : orderAfterSale.getTenantId());
        orderOccupySkuDetailReqDTO.setMarketItemPresaleSwitch(orderItemSnapshot.getPresaleSwitch());
        orderOccupySkuDetailReqDTOS.add(orderOccupySkuDetailReqDTO);

        RpcResultUtil.handle(saleInventoryCenterCommandProvider.orderOccupy(orderOccupyReqDTO));
        return false;
    }
}
