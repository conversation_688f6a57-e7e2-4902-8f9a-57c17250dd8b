package com.cosfo.ordercenter.service.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.common.util.RpcResponseUtil;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.service.biz.StoreService;
import com.cosfo.ordercenter.service.constant.DeliveryConstant;
import com.cosfo.ordercenter.service.constant.NumberConstant;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreQueryProvider;
import net.xianmu.usercenter.client.merchant.req.MerchantAddressQueryReq;
import net.xianmu.usercenter.client.merchant.resp.MerchantAddressResultResp;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/8/22 14:39
 * @Description:
 */
@Service
@Slf4j
public class StoreServiceImpl implements StoreService {

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;

    @DubboReference
    private MerchantStoreQueryProvider merchantStoreQueryProvider;

    /**
     * 查询正常的门店地址
     *
     * @param storeId
     * @param tenantId
     * @return
     */
    @Override
    public MerchantAddressResultResp queryDefaultAddress(Long storeId, Long tenantId) {
        if (Objects.isNull(storeId)) {
            throw new ParamsException("门店Id不能为空");
        }
        if (Objects.isNull(tenantId)) {
            throw new ParamsException("租户Id不能为空");
        }
        return selectByStoreId(storeId, tenantId);
    }

    public MerchantAddressResultResp selectByStoreId(Long storeId, Long tenantId) {
        MerchantAddressQueryReq merchantAddressQueryReq = new MerchantAddressQueryReq();
        merchantAddressQueryReq.setStoreId(storeId);
        merchantAddressQueryReq.setTenantId(tenantId);
        merchantAddressQueryReq.setDefaultFlag(DeliveryConstant.DEFAULT_STATUS);
        merchantAddressQueryReq.setStatus(DeliveryConstant.STORE_NORMAL_STATUS);
        List<MerchantAddressResultResp> merchantAddressList = RpcResultUtil.handle(merchantAddressQueryProvider.getMerchantAddressList(merchantAddressQueryReq));
        if (CollectionUtil.isNotEmpty(merchantAddressList)) {
            return merchantAddressList.get(NumberConstant.ZERO);
        }
        throw new BizException("门店地址信息不存在");
    }

    @Override
    public String queryStoreName(Long storeId) {
        if(storeId == null){
            return null;
        }
        try {
            MerchantStoreResultResp storeResultResp = RpcResponseUtil.handler(merchantStoreQueryProvider.getMerchantStoreById(storeId));
            if(storeResultResp != null){
                return storeResultResp.getStoreName();
            }
        } catch (Exception e) {
            log.error("查询门店信息异常，storeId={}", storeId, e);
        }
        return null;
    }
}

