package com.cosfo.ordercenter.service.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class AsyncThreadPoolConfig {

    @Bean(name = "notifyPool")
    public ThreadPoolExecutor notifyPool() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                2, 4, 1, TimeUnit.MINUTES, new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("notify-thread-%d").build()
        );
        return executor;
    }
}
