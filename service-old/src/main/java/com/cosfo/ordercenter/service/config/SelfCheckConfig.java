package com.cosfo.ordercenter.service.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
public class SelfCheckConfig {
    /**
     * 自检租户id
     */
    @Value("${selfCheck.tenantId:2}")
    private Long tenantId;

    /**
     * 是否开启自检
     */
    @Value("${selfCheck.switch:false}")
    private Boolean checkSwitch;
}
