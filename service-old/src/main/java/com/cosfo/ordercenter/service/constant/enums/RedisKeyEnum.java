package com.cosfo.ordercenter.service.constant.enums;

import lombok.Getter;

/**
 * @Author: fansongsong
 * @Date: 2023-03-30
 * @Description: 定义redis的key枚举
 */
@Getter
public enum RedisKeyEnum {
    OC00001("批量售后订单分布式锁"),
    OC00002("订单下单分布式锁"),
    ;

    /**
     * 锁系统前缀
     */
    private static final String SPACE = "order-center";

    /**
     * 连接符
     */
    public static final String SEPARATOR = "_";

    public String join(Object... args) {
        StringBuilder key = new StringBuilder(SPACE).append(SEPARATOR).append(super.toString());
        for (Object arg : args) {
            key.append(SEPARATOR).append(arg);
        }
        return key.toString();
    }

    RedisKeyEnum(String desc) {

    }

}
