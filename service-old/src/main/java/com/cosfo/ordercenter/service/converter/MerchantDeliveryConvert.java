package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.delivery.DeliveryFeeCalRuleDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.dao.model.po.DeliveryFeeCalRule;
import com.cosfo.ordercenter.dao.model.po.MerchantDeliveryFeeRule;
import com.cosfo.ordercenter.dao.model.po.OrderDeliveryFeeSnapshot;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/8/21 16:57
 * @Description:
 */
@Mapper
public interface MerchantDeliveryConvert {

    MerchantDeliveryConvert INSTANCE = Mappers.getMapper(MerchantDeliveryConvert.class);

    @Mapping(source = "id", target = "ruleId")
    @Mapping(source = "type", target = "warehouseType")
    @Mapping(source = "freeDeliveryType", target = "deliveryType")
    @Mapping(source = "hitAreas", target = "hitAreaList")
    MerchantDeliveryRuleInfoDTO convert2Dto(MerchantDeliveryFeeRule feeRule);

    @Mapping(source = "deliveryFee", target = "orderDeliveryFee")
    @Mapping(target = "orderInfo", expression = "java(com.alibaba.fastjson.JSON.toJSONString(dto.getOrderInfo()))")
    @Mapping(target = "ruleInfo", expression = "java(com.alibaba.fastjson.JSON.toJSONString(dto.getRuleList()))")
    @Mapping(target = "hitRuleFee", expression = "java(com.alibaba.fastjson.JSON.toJSONString(dto.getHitRuleList()))")
    OrderDeliveryFeeSnapshot convert2Po(MerchantDeliveryFeeSnapshotDTO dto);

    DeliveryFeeCalRuleDTO convert2Dto(DeliveryFeeCalRule deliveryFeeCalRule);

}
