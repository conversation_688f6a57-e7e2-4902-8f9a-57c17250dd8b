package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/18
 */
public interface OrderAddressConvert {

    OrderAddressConvert INSTANCE = Mappers.getMapper(OrderAddressConvert.class);

    /**
     * 转化为OrderAddressDTO
     *
     * @param orderAddress
     * @return
     */
    OrderAddressDTO toDTO(OrderAddress orderAddress);

    OrderAddress toEntity(OrderAddressDTO dto);

    List<OrderAddressDTO> toDTOList(List<OrderAddress> orderAddressList);
}
