package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderAfterSaleConverter {

    OrderAfterSaleConverter INSTANCE = Mappers.getMapper(OrderAfterSaleConverter.class);

    OrderAfterSale toEntity(OrderAfterSaleDTO orderAfterSaleDTO);


    OrderAfterSaleWithOrderDTO toWithOrder(OrderAfterSale orderAfterSale);

    OrderAfterSaleDTO entity2Dto(OrderAfterSale orderAfterSale);

    List<OrderAfterSaleDTO> entitys2Dtos(List<OrderAfterSale> orderAfterSales);
}
