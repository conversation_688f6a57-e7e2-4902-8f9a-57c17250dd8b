package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderAfterSaleOutDTO;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
public class OrderAfterSaleOldConverter {

    public static OrderAfterSaleOutDTO buildOrderAfterSaleOutDTO(OrderAfterSale orderAfterSale, OrderItem orderItem, OrderItemSnapshot orderItemSnapshot){

        if (orderAfterSale == null) {
            return null;
        }
        OrderAfterSaleOutDTO orderAfterSaleOutDTO = new OrderAfterSaleOutDTO();
        orderAfterSaleOutDTO.setId(orderAfterSale.getId());
        orderAfterSaleOutDTO.setOrderAfterSaleNo(orderAfterSale.getAfterSaleOrderNo());
        orderAfterSaleOutDTO.setAmount(orderAfterSale.getAmount());
        orderAfterSaleOutDTO.setTotalPrice(orderAfterSale.getTotalPrice());
        orderAfterSaleOutDTO.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleOutDTO.setSkuId(orderItemSnapshot.getSkuId());
        orderAfterSaleOutDTO.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
        orderAfterSaleOutDTO.setAreaItemId(orderItemSnapshot.getAreaItemId());
        orderAfterSaleOutDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
        orderAfterSaleOutDTO.setTitle(orderItemSnapshot.getTitle());
        orderAfterSaleOutDTO.setMainPicture(orderItemSnapshot.getMainPicture());
        orderAfterSaleOutDTO.setSpecification(orderItemSnapshot.getSpecification());
        orderAfterSaleOutDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
        orderAfterSaleOutDTO.setPrice(orderItem.getPayablePrice());
        return orderAfterSaleOutDTO;
    }
}
