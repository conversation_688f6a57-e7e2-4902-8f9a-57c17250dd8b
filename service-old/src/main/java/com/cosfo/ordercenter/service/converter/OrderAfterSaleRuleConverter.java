package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleRuleDTO;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 *
 * @author: xiaowk
 * @date: 2023/8/10 下午6:53
 */
@Mapper
public interface OrderAfterSaleRuleConverter {

    OrderAfterSaleRuleConverter INSTANCE = Mappers.getMapper(OrderAfterSaleRuleConverter.class);

    OrderAfterSaleRule toEntity(OrderAfterSaleRuleDTO orderAfterSaleRuleDTO);

    OrderAfterSaleRuleDTO toDTO(OrderAfterSaleRule orderAfterSaleRule);

    List<OrderAfterSaleRuleDTO> toDTOList(List<OrderAfterSaleRule> orderAfterSaleRuleList);
}
