package com.cosfo.ordercenter.service.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderDetailDTO;
import com.cosfo.ordercenter.client.resp.OrderOutDTO;
import com.cosfo.ordercenter.client.resp.OrderSkuQuantityDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderDetail;
import com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity;
import com.github.pagehelper.PageInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderConverter {

    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    /**
     * order -> orderDTO
     *
     * @param order
     * @return
     */
    OrderDTO toDTO(Order order);

    /**
     * orderList -> orderDTOList
     *
     * @param orderList
     * @return
     */
    List<OrderDTO> toDTOList(List<Order> orderList);

    /**
     * page to pageInfo
     *
     * @param page
     * @return
     */
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "pageNum", source = "current")
    @Mapping(target = "list", source = "records")
    PageInfo<OrderDTO> toPageInfo(Page<Order> page);


    List<OrderSkuQuantityDTO> skuToDTOList(List<OrderSkuQuantity> orderSkuQuantities);


    /**
     * orderDetailList -> orderDetailDTOList
     *
     * @param orderDetails
     * @return
     */
    List<OrderDetailDTO> detailToDTOList(List<OrderDetail> orderDetails);

    Order toEntity(OrderDTO orderDTO);

    /**
     * 转化为OrderOutDTO
     *
     * @param order
     * @return
     */
    OrderOutDTO toOrderOutDTO(Order order);
}
