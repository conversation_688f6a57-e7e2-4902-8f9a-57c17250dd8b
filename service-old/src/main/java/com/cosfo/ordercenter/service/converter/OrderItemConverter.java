package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.resp.OrderItemOutDTO;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderItemConverter {
    OrderItemConverter INSTANCE = Mappers.getMapper(OrderItemConverter.class);

    /**
     * orderItem -> orderItemDTO
     *
     * @param orderItem
     * @return
     */
    OrderItemDTO toDTO(OrderItem orderItem);

    /**
     * orderItemList -> DTOList
     *
     * @param orderItems
     * @return
     */
    List<OrderItemAndSnapshotDTO> toWithSnapshotDTOList(List<OrderItem> orderItems, @Context Map<Long, OrderItemSnapshot> snapshotMap);

    @Mapping(target = "orderItemId", source = "id")
    OrderItemAndSnapshotDTO toWithSnapshotDTO(OrderItem orderItem, @Context Map<Long, OrderItemSnapshot> snapshotMap);

    @AfterMapping
    default void fillSnapshot(@MappingTarget OrderItemAndSnapshotDTO snapshotDTO, @Context Map<Long, OrderItemSnapshot> snapshotMap) {
        OrderItemSnapshot orderItemSnapshot = snapshotMap.get(snapshotDTO.getOrderItemId());
        if (orderItemSnapshot != null) {
            snapshotDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
            snapshotDTO.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
            snapshotDTO.setSupplierName(orderItemSnapshot.getSupplierName());
            snapshotDTO.setTitle(orderItemSnapshot.getTitle());
            snapshotDTO.setMainPicture(orderItemSnapshot.getMainPicture());
            snapshotDTO.setSpecification(orderItemSnapshot.getSpecification());
            snapshotDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
            snapshotDTO.setWarehouseType(orderItemSnapshot.getWarehouseType());
            snapshotDTO.setGoodsType(orderItemSnapshot.getGoodsType());
            snapshotDTO.setDeliveryType(orderItemSnapshot.getDeliveryType());
            snapshotDTO.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
            snapshotDTO.setAfterSaleRule(orderItemSnapshot.getAfterSaleRule());
            snapshotDTO.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
            snapshotDTO.setSkuId(orderItemSnapshot.getSkuId());
            snapshotDTO.setMaxAfterSaleAmount(orderItemSnapshot.getMaxAfterSaleAmount());
            snapshotDTO.setSkuCode(orderItemSnapshot.getSkuCode());
            snapshotDTO.setCustomSkuCode(orderItemSnapshot.getCustomSkuCode());
            snapshotDTO.setPresaleSwitch(orderItemSnapshot.getPresaleSwitch());
        }
    }

    /**
     * orderItemList -> DTOList
     *
     * @param orderItems
     * @return
     */
    List<OrderItemDTO> toDTOList(List<OrderItem> orderItems);

    List<OrderItemAndSnapshotDTO> toDTO(List<OrderItemWithSnapshot> itemWithSnapshots);
    @Mapping(target = "supplierSkuId", source = "supplySkuId")
    OrderItemAndSnapshotDTO toDTO(OrderItemWithSnapshot itemWithSnapshot);

    /**
     * 转化为OrderItemOutDTO
     *
     * @param orderItem
     * @param snapshotMap
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "price", source = "payablePrice")
    OrderItemOutDTO toOrderItemOutDTO(OrderItem orderItem, @Context Map<Long, OrderItemSnapshot> snapshotMap);

    @AfterMapping
    default void fillOrderItemSnapshot(@MappingTarget OrderItemOutDTO orderItemOutDTO, @Context Map<Long, OrderItemSnapshot> snapshotMap) {
        OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderItemOutDTO.getId());
        if (orderItemSnapshot != null) {
            orderItemOutDTO.setDeliveryType(orderItemSnapshot.getDeliveryType());
            orderItemOutDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
            orderItemOutDTO.setWarehouseType(orderItemSnapshot.getWarehouseType());
            orderItemOutDTO.setSupplierSkuId(orderItemSnapshot.getSupplierSkuId());
            orderItemOutDTO.setSupplierName(orderItemSnapshot.getSupplierName());
            orderItemOutDTO.setTitle(orderItemSnapshot.getTitle());
            orderItemOutDTO.setMainPicture(orderItemSnapshot.getMainPicture());
            orderItemOutDTO.setSpecification(orderItemSnapshot.getSpecification());
            orderItemOutDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
            orderItemOutDTO.setGoodsType(orderItemSnapshot.getGoodsType());
            orderItemOutDTO.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
            orderItemOutDTO.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
        }
    }
}
