package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.resp.OrderItemExtraDTO;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderItemExtraConvert {

    OrderItemExtraConvert INSTANCE = Mappers.getMapper(OrderItemExtraConvert.class);

    List<OrderItemExtraDTO> toDTOList(List<OrderItemExtra> orderItemExtraList);
}
