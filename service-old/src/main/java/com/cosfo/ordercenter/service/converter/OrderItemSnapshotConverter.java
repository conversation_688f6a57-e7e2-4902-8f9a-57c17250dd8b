package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.req.OrderItemCreateReq;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderItemSnapshotConverter {
    OrderItemSnapshotConverter INSTANCE = Mappers.getMapper(OrderItemSnapshotConverter.class);

    /**
     * snapshot -> snapshotDTO
     *
     * @param orderItemSnapshot
     * @return
     */
    OrderItemSnapshotDTO toDTO(OrderItemSnapshot orderItemSnapshot);

    /**
     * list -> DTOList
     *
     * @param orderItemSnapshots
     * @return
     */
    List<OrderItemSnapshotDTO> toDTOList(List<OrderItemSnapshot> orderItemSnapshots);

    OrderItemSnapshot reqToEntity(OrderItemCreateReq createReq);

}
