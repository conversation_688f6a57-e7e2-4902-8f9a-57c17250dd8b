package com.cosfo.ordercenter.service.converter;

import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryQueryParam;
import com.cosfo.ordercenter.dao.model.param.OrderQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderParamConverter {

    OrderParamConverter INSTANCE = Mappers.getMapper(OrderParamConverter.class);

    OrderQueryParam reqToParam(OrderQueryReq queryReq);

    OrderDeliveryQueryParam reqToParam(OrderNeedDeliveryReq orderNeedDeliveryReq);
}
