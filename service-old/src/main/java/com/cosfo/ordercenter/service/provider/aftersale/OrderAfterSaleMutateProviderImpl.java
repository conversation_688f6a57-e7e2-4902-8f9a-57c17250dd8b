package com.cosfo.ordercenter.service.provider.aftersale;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.manage.client.order.aftersale.OrderAfterSaleProvider;
import com.cosfo.manage.client.order.req.OrderAfterSaleSelfReviewAgentReq;
import com.cosfo.ordercenter.client.common.AuditFlagEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.OrderTypeEnum;
import com.cosfo.ordercenter.client.common.SystemSourceEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleBatchReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleEnableApplyReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateStoreNoReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleMutateService;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.aftersale.executor.OrderAfterSaleExecutorContext;
import com.cosfo.ordercenter.service.biz.bo.OrderAfterSaleAuditBO;
import com.cosfo.ordercenter.service.biz.dto.OrderAfterSaleServerProviderAuditDTO;
import com.cosfo.ordercenter.service.constant.Constant;
import com.cosfo.ordercenter.service.constant.NumberConstant;
import com.cosfo.ordercenter.service.constant.enums.RedisKeyEnum;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.service.exception.code.OpenApiErrorCode;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.result.ResultStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderAfterSaleMutateProviderImpl implements OrderAfterSaleMutateService {

    @Resource
    private OrderAfterSaleExecutorContext orderAfterSaleExecutorContext;
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @DubboReference
    private OrderAfterSaleProvider orderAfterSaleProvider;

    private static HashSet<OrderAfterSaleStatusEnum> AFTER_SALE_PROCESS_STATUS = Sets.newHashSet(INVENTORY_DEALING, REFUNDING, AUDITED_SUCCESS, AUDITED_FAILED, CANCEL, WAIT_REFUND);

    private static HashSet<Integer> AFTER_SALE_FAIL_STATUS = Sets.newHashSet(CANCEL.getValue(), AUDITED_FAILED.getValue());

    // 待审核状态，包括待审核和待确认
    private static HashSet<Integer> UNAUDITED_STATUS = Sets.newHashSet(UNAUDITED.getValue(), WAIT_CONFIRM.getValue());

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Long> createAfterDeliveryAfterSale(OrderAfterSaleDTO orderAfterSaleDTO) {
        String afterSaleOrderNo = Constant.createOrderNo(Constant.NORMAL_ORDER_AFTER_SALE_CODE);
        orderAfterSaleDTO.setAfterSaleOrderNo(afterSaleOrderNo);
        Integer serviceType = orderAfterSaleDTO.getServiceType();

        Order order = orderDao.getById(orderAfterSaleDTO.getOrderId());
        //补充信息
        orderAfterSaleDTO.setTenantId(order.getTenantId());
//        orderAfterSaleDTO.setOrderId(order.getId());
        orderAfterSaleDTO.setStoreId(order.getStoreId());
        orderAfterSaleDTO.setAccountId(order.getAccountId());
        orderAfterSaleDTO.setTotalPrice(orderAfterSaleDTO.getApplyPrice());
        orderAfterSaleDTO.setWarehouseType(order.getWarehouseType());
        orderAfterSaleDTO.setOrderId(ObjectUtil.isNotNull(orderAfterSaleDTO.getOrderId()) ? orderAfterSaleDTO.getOrderId() : order.getId());
        orderAfterSaleDTO.setReason(ObjectUtil.isNotNull(orderAfterSaleDTO.getReason()) ? orderAfterSaleDTO.getReason() : "拍多/拍错/不想要");
        if (!StringUtils.isEmpty(order.getWarehouseNo())) {
            orderAfterSaleDTO.setReturnWarehouseNo(String.valueOf(order.getWarehouseNo()));
        }

        orderAfterSaleDTO.setAutoFinishedTime(LocalDateTime.now().plusDays(order.getAutoFinishedTime()));
        orderAfterSaleBizService.orderAfterSaleDataCheck(orderAfterSaleDTO, order);

        Long afterSaleId;
        try {
            afterSaleId = transactionTemplate.execute(status -> {
                Long orderAfterSaleId = orderAfterSaleExecutorContext.load(serviceType).createOrderAfterSale(orderAfterSaleDTO);

                return orderAfterSaleId;
            });
            if (afterSaleId == null) {
                throw new BizException("创建售后失败");
            }
        } catch (Exception ex) {
            throw new BizException("创建售后失败");
        }

        return DubboResponse.getOK(afterSaleId);

    }

    @Override
    public DubboResponse<List<Long>> createPreDeliveryAfterSale(List<OrderAfterSaleDTO> orderAfterSaleDTOS) {
        // 批量仅支持配送前售后
        if (CollectionUtils.isEmpty(orderAfterSaleDTOS)) {
            log.warn("售后申请数据为空");
            return DubboResponse.getOK(Collections.emptyList());
        }

        // 统计 orderItem 数量
        boolean hasDuplicate = orderAfterSaleDTOS.stream().map(OrderAfterSaleDTO::getOrderItemId).distinct().count() != orderAfterSaleDTOS.size();
        if (hasDuplicate) {
            log.warn("售后申请数据存在重复 orderItemId={}", JSON.toJSON(orderAfterSaleDTOS));
            throw new BizException("售后申请提交失败了，请刷新后重试");
        }

        Order order = orderDao.getById(orderAfterSaleDTOS.get(0).getOrderId());
        if (order == null) {
            log.error("售后单对应订单不存在, afterSaleList={}", JSON.toJSON(orderAfterSaleDTOS));
            throw new BizException("售后单对应订单不存在");
        }
        Integer serviceType = orderAfterSaleDTOS.get(0).getServiceType();
        Integer afterSaleType = orderAfterSaleDTOS.get(0).getAfterSaleType();
        String reason = orderAfterSaleDTOS.get(0).getReason();

        List<Long> result = new ArrayList<>();
        List<String> keys = Lists.newArrayList();
        if (OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType())) {
            // 处理组合包逻辑
            // 组合包配送前售后需要售后全部订单子项，直接取 orderItem 拼装售后信息
            List<Order> combineOrderList = orderDao.queryByCombineId(order.getCombineOrderId(), order.getTenantId());
            log.info("组合包订单售后, OrderList={}", combineOrderList);
            for (Order combineOrder : combineOrderList) {
                //判断配送状态
                if (!OrderStatusEnum.ableApplyNotSendAfterSale(combineOrder.getStatus())) {
                    log.warn("订单id={}不支持批量接口申请售后, 订单当前状态={}", combineOrder.getId(), combineOrder.getStatus());
                    throw new BizException("不好意思，组合包订单存在已配送订单，不能退款");
                }
            }
            List<OrderItem> orderItems = orderItemDao.batchQueryByOrderIds(combineOrderList.stream().map(Order::getId).collect(Collectors.toList()));
            Map<Long, List<OrderItem>> orderItemMap = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
            try {
                lockOrder(keys, combineOrderList.stream().map(Order::getId).collect(Collectors.toList()));
                // 拼装组合包订单售后单, 过滤申请订单
                for (Order combineOrder : combineOrderList) {
                    List<OrderItem> itemList = orderItemMap.get(combineOrder.getId());
                    // 校验改成批量
                    List<OrderAfterSaleDTO> combineAfterSaleList = itemList.stream().map(orderItem -> buildOrderAfterSaleDTO(combineOrder, orderItem, afterSaleType, serviceType, reason)).collect(Collectors.toList());
                    orderAfterSaleBizService.orderAfterSaleDataCheckForOneOrder(combineAfterSaleList, combineOrder, itemList);
                    List<Long> combineResult = transactionTemplate.execute(status -> orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(combineAfterSaleList));
                    if (combineResult == null) {
                        throw new BizException("创建售后单失败");
                    }
                    result.addAll(combineResult);

                }
            } catch (ProviderException providerException) {
              throw providerException;
            } catch (Exception ex) {
                throw new BizException("创建售后单失败", ex);
            } finally {
                unlockOrder(keys);
            }
        } else {
            log.info("普通订单配送前售后");
            // 原始售后单先处理
            LocalDateTime autoFinishedTime = LocalDateTime.now().plusDays(order.getAutoFinishedTime());
            orderAfterSaleDTOS.forEach(orderAfterSaleDTO -> {
                orderAfterSaleDTO.setAutoFinishedTime(autoFinishedTime);
            });
            try {
                lockOrder(keys, Lists.newArrayList(order.getId()));
                List<OrderItem> orderItems = orderItemDao.queryByIds(orderAfterSaleDTOS.stream().map(OrderAfterSaleDTO::getOrderItemId).collect(Collectors.toList()));
                orderAfterSaleBizService.orderAfterSaleDataCheckForOneOrder(orderAfterSaleDTOS, order, orderItems);
                result = transactionTemplate.execute(status -> orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(orderAfterSaleDTOS));
                if (result == null) {
                    throw new BizException("创建售后单失败");
                }
            } catch (ProviderException providerException) {
                throw providerException;
            }catch (Exception ex) {
                throw new BizException("创建售后单失败", ex);
            } finally {
                unlockOrder(keys);
            }


        }
        return DubboResponse.getOK(result);
    }

    private OrderAfterSaleDTO buildOrderAfterSaleDTO(Order combineOrder, OrderItem orderItem, Integer afterSaleType, Integer serviceType, String reason) {
        OrderAfterSaleDTO orderAfterSale = new OrderAfterSaleDTO();
        orderAfterSale.setTenantId(combineOrder.getTenantId());
        orderAfterSale.setOrderId(combineOrder.getId());
        orderAfterSale.setOrderItemId(orderItem.getId());
        orderAfterSale.setAmount(orderItem.getAmount());
        orderAfterSale.setAfterSaleType(afterSaleType);
        orderAfterSale.setServiceType(serviceType);
        orderAfterSale.setApplyPrice(orderItem.getTotalPrice());
        orderAfterSale.setWarehouseType(combineOrder.getWarehouseType());
        orderAfterSale.setReason(reason);
        orderAfterSale.setAutoFinishedTime(LocalDateTime.now().plusDays(combineOrder.getAutoFinishedTime()));

        return orderAfterSale;
    }

    @Override
    public DubboResponse<Boolean> cancel(Long orderAfterSaleId) {
        OrderAfterSale orderAfterSale = orderAfterSaleDao.getById(orderAfterSaleId);
        if (ObjectUtil.isNull(orderAfterSale)) {
            log.error("售后单不存在 orderAfterSaleId={}", orderAfterSaleId);
            throw new BizException("售后单不存在");
        }

        OrderAfterSaleStatusEnum status = fromCode(orderAfterSale.getStatus());
        // 退款中 已同意 已拒绝 已取消 待退款 都不支持取消
        if (AFTER_SALE_PROCESS_STATUS.contains(status)) {
            throw new BizException("该售后单已经处理中，不能取消");
        }

        if (UNAUDITED_STATUS.contains(orderAfterSale.getStatus())) {
            OrderAfterSale update = new OrderAfterSale();
            update.setId(orderAfterSaleId);
            update.setStatus(OrderAfterSaleStatusEnum.CANCEL.getValue());
            return DubboResponse.getOK(orderAfterSaleDao.updateById(update));
        }

        return DubboResponse.getOK(orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).cancel(orderAfterSale));
    }

    @Override
    public DubboResponse<Boolean> updateStatus(OrderAfterSaleStatusUpdateReq orderAfterSaleStatusUpdateReq) {
        return DubboResponse.getOK(orderAfterSaleDao.updateStatus(orderAfterSaleStatusUpdateReq));
    }

    @Override
    public DubboResponse<Boolean> updateById(OrderAfterSaleDTO orderAfterSaleDTO) {
        OrderAfterSale orderAfterSale = OrderAfterSaleConverter.INSTANCE.toEntity(orderAfterSaleDTO);
        if (nullObjCheck(orderAfterSale)) {
            log.warn("售后单更新数据为空, orderAfterSaleId={}", orderAfterSaleDTO, new BizException("售后单更新数据为空"));
            return DubboResponse.getOK(false);
        }
        return DubboResponse.getOK(orderAfterSaleDao.updateById(orderAfterSale));
    }

    private boolean nullObjCheck(OrderAfterSale afterSale) {
        if (afterSale.getTenantId() == null
                && afterSale.getOrderId() == null
                && afterSale.getOrderItemId() == null
                && afterSale.getStoreId() == null
                && afterSale.getAccountId() == null
                && afterSale.getAfterSaleOrderNo() == null
                && afterSale.getAmount() == null
                && afterSale.getAfterSaleType() == null
                && afterSale.getServiceType() == null
                && afterSale.getApplyPrice() == null
                && afterSale.getTotalPrice() == null
                && afterSale.getDeliveryFee() == null
                && afterSale.getReason() == null
                && afterSale.getUserRemark() == null
                && afterSale.getProofPicture() == null
                && afterSale.getStatus() == null
                && afterSale.getHandleRemark() == null
                && afterSale.getOperatorName() == null
                && afterSale.getCreateTime() == null
                && afterSale.getUpdateTime() == null
                && afterSale.getFinishedTime() == null
                && afterSale.getHandleTime() == null
                && afterSale.getRecycleTime() == null
                && afterSale.getRecycleDetails() == null
                && afterSale.getResponsibilityType() == null
                && afterSale.getStoreNo() == null
                && afterSale.getWarehouseType() == null
                && afterSale.getAutoFinishedTime() == null
                && afterSale.getApplyQuantity() == null
                && afterSale.getAdminRemark() == null
                && afterSale.getAdminRemarkTime() == null
                && afterSale.getSecondHandleRemark() == null
                && afterSale.getReturnAddressId() == null
                && afterSale.getReturnWarehouseNo() == null) {
            return true;
        }
        return false;
    }

    @Override
    public DubboResponse<Boolean> processFinish(List<OrderAfterSaleProcessFinishReq> list) {
        if (CollectionUtils.isEmpty(list)) {
            return DubboResponse.getOK(true);
        }
        OrderAfterSaleProcessFinishReq processFinishReq = list.get(0);
        OrderAfterSale orderAfterSale = orderAfterSaleDao.queryByAfterSaleNo(processFinishReq.getOrderAfterSaleNo());
        if (orderAfterSale == null) {
            throw new BizException("售后单不存在");
        }
        return DubboResponse.getOK(orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).finish(list));
    }

    @Override
    public DubboResponse<Boolean> reviewSubmissions(OrderAfterSaleAuditReq orderAfterSaleAuditReq) {
        OrderAfterSale orderAfterSale = orderAfterSaleDao.queryByAfterSaleNo(orderAfterSaleAuditReq.getAfterSaleOrderNo());
        if (orderAfterSale == null) {
            log.error("售后单不存在, req={}", orderAfterSaleAuditReq);
            throw new BizException("售后单不存在");
        }
        List<Integer> needAuditStatusList = Arrays.asList(UNAUDITED.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());
        if (!needAuditStatusList.contains(orderAfterSale.getStatus())) {
            throw new BizException("状态非待审核，请确认后重新发起");
        }

        orderAfterSaleAuditReq.setOrderAfterSaleId(orderAfterSale.getId());

        // 校验审核权限
        checkReviewPermission(orderAfterSaleAuditReq, orderAfterSale);

        if (Objects.isNull(orderAfterSaleAuditReq.getTotalPrice())) {
            throw new BizException("售后金额不能为空，请输入售后金额");
        }
        if (orderAfterSaleAuditReq.getTotalPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("售后金额不能小于0");
        }
        if (orderAfterSaleAuditReq.getTotalPrice().compareTo(orderAfterSale.getApplyPrice()) > 0) {
            throw new BizException("售后金额不可大于申请金额，请确认后重新发起");
        }

        // 确认本次审核是否需要服务商确认
        setNeedServiceProvider(orderAfterSale.getTenantId(), orderAfterSaleAuditReq);

        //分账中目前在客户端判断
        if (Objects.equals(AuditFlagEnum.AUDIT_SUCCESS.getFlag(), orderAfterSaleAuditReq.getAuditStatus())) {
            //检验数量
            verifyPriceAndQuantity(orderAfterSale);
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).reviewSuccess(orderAfterSaleAuditReq);
        } else {
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).reviewReject(orderAfterSaleAuditReq, orderAfterSale);
        }
        return DubboResponse.getOK(true);
    }

    private void setNeedServiceProvider(Long tenantId, OrderAfterSaleAuditReq orderAfterSaleAuditReq) {
        OrderAfterSaleServerProviderAuditDTO orderAfterSaleServerProviderAuditDTO = OrderAfterSaleServerProviderAuditDTO.builder()
                .tenantId(tenantId)
                .warehouseType(orderAfterSaleAuditReq.getWarehouseType())
                .goodsType(orderAfterSaleAuditReq.getGoodsType())
                .auditStatus(orderAfterSaleAuditReq.getAuditStatus())
                .responsibilityType(Optional.ofNullable(orderAfterSaleAuditReq.getResponsibilityType()).map(Integer::valueOf).orElse(null)).build();
        boolean needServiceProviderAudit = orderAfterSaleBizService.checkNeedServiceProviderAudit(orderAfterSaleServerProviderAuditDTO);
        orderAfterSaleAuditReq.setNeedServiceProviderAudit(needServiceProviderAudit);
    }

    /**
     * 校验品牌方自己审核代仓的权限
     * @param orderAfterSale
     */
    private void checkReviewPermission(OrderAfterSaleAuditReq orderAfterSaleAuditReq, OrderAfterSale orderAfterSale) {
        // 请求来源
        SystemSourceEnum systemSourceEnum = SystemSourceEnum.getByCode(orderAfterSaleAuditReq.getSystemSource());
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderAfterSale.getOrderItemId());
        Integer goodsType = orderItemSnapshot.getGoodsType();
        orderAfterSaleAuditReq.setWarehouseType(orderAfterSale.getWarehouseType());
        orderAfterSaleAuditReq.setGoodsType(orderItemSnapshot.getGoodsType());
        switch (systemSourceEnum) {
            case MANAGE:
                // 校验品牌方的审核权限
                checkBrandReviewPermission(orderAfterSale, goodsType);
                break;
            case OMS:
                // 校验oms的审核权限
                checkServiceProviderReviewPermission(orderAfterSale, goodsType);
                break;
            default:
                throw new BizException("不支持的请求来源");
        }
    }

    /**
     * 品牌方审核权限校验
     * @param orderAfterSale
     * @param goodsType
     */
    private void checkBrandReviewPermission(OrderAfterSale orderAfterSale, Integer goodsType) {
        boolean agentWarehouseFlag = Objects.equals(orderAfterSale.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode());
        OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq(orderAfterSale.getTenantId());
        boolean agentAfterSaleSelfReviewFlag = RpcResultUtil.handle(orderAfterSaleProvider.needSelfReviewFlag(req));
        if (agentWarehouseFlag && !agentAfterSaleSelfReviewFlag) {
            // 代仓售后且品牌方没开启自审配置
            throw new BizException("该售后订单您暂无审核权限，请联系代仓服务商进行审核");
        }
        boolean quotationFlag = Objects.equals(orderAfterSale.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode()) && Objects.equals(goodsType, GoodsTypeEnum.QUOTATION_TYPE.getCode());
        if (quotationFlag) {
            throw new BizException("该售后订单您暂无审核权限");
        }
    }

    /**
     * 服务商权限校验
     * @param orderAfterSale
     * @param goodsType
     */
    private void checkServiceProviderReviewPermission(OrderAfterSale orderAfterSale, Integer goodsType) {
        // 非三方仓不支持审核
        if (!Objects.equals(orderAfterSale.getWarehouseType(), WarehouseTypeEnum.THREE_PARTIES.getCode())) {
            throw new BizException("该售后订单您暂无审核权限");
        }
        OrderAfterSaleSelfReviewAgentReq req = new OrderAfterSaleSelfReviewAgentReq(orderAfterSale.getTenantId());
        boolean agentAfterSaleSelfReviewFlag = RpcResultUtil.handle(orderAfterSaleProvider.needSelfReviewFlag(req));
        // 代仓售后且品牌方开启自审配置
        if (Objects.equals(goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode()) && agentAfterSaleSelfReviewFlag) {
            throw new BizException("该售后订单您暂无审核权限，品牌方开启了代仓审核权限");
        }
    }

    private void verifyPriceAndQuantity(OrderAfterSale orderAfterSale) {
        OrderAfterSaleEnableApplyReq afterSaleEnableApplyReq = new OrderAfterSaleEnableApplyReq();
        afterSaleEnableApplyReq.setOrderId(orderAfterSale.getOrderId());
        afterSaleEnableApplyReq.setTenantId(orderAfterSale.getTenantId());
        afterSaleEnableApplyReq.setOrderItemId(orderAfterSale.getOrderItemId());
        Map<Long, OrderAfterSaleEnableDTO> enableApplyMap = RpcResultUtil.handle(orderAfterSaleQueryService.queryEnableApply(afterSaleEnableApplyReq));
        OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableApplyMap.get(orderAfterSale.getOrderItemId());
        if (orderAfterSaleEnableDTO.getEnableApplyQuantity() < 0) {
            throw new BizException("可售后件数不足");
        }
        if (orderAfterSaleEnableDTO.getEnableApplyPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException("可售后金额不足,最大可售后金额为" + orderAfterSaleEnableDTO.getEnableApplyPrice() + "元");
        }
    }

    @Override
    public DubboResponse<Boolean> autoFinished() {
        return DubboResponse.getOK(orderAfterSaleDao.autoFinish());
    }

    @Override
    public DubboResponse<Boolean> updateAfterSaleStoreNo(@Valid OrderAfterSaleUpdateStoreNoReq req) {
        OrderAfterSale afterSale = orderAfterSaleDao.queryByAfterSaleNo(req.getOrderAfterSaleNo());
        if(afterSale == null){
            throw new BizException("售后单不存在");
        }

        boolean result = orderAfterSaleDao.updateStoreNo(afterSale.getId(), req.getSourceStoreNo(), req.getStoreNo());

        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<List<Long>> batchCreateAfterSaleForOpen(OrderAfterSaleBatchReq orderAfterSaleBatchReq) {
        List<Long> orderIdList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(dto -> dto.getOrderId()).distinct().collect(Collectors.toList());
        List<String> keys = Lists.newArrayList();
        List<Long> afterSaleIdList;
        try {
            long count = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleDTO::getTenantId).distinct().count();
            if (count > NumberConstant.ONE) {
                throw new BizException("仅支持单租户批量售后", OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            }
            Long tenantId = orderAfterSaleBatchReq.getApplyAfterSaleList().get(NumberConstant.ZERO).getTenantId();

            // 订单加锁
            lockOrder(keys, orderIdList);
            // 幂等校验
            checkCustomerAfterSaleOrderNo(orderAfterSaleBatchReq, tenantId);
            // 订单校验
            Map<Long, List<OrderAfterSaleDTO>> orderAfterSaleMap = validOrderAndTransferParam(orderAfterSaleBatchReq, orderIdList);
            try {
                // 批量创建售后单
                afterSaleIdList = transactionTemplate.execute(status -> {
                    List<Long> afterSaleIds = Lists.newArrayList();
                    for (Map.Entry<Long, List<OrderAfterSaleDTO>> orderEntry : orderAfterSaleMap.entrySet()) {
                        List<OrderAfterSaleDTO> orderAfterSaleDTOS = orderEntry.getValue();
                        // 退款录入账单、退货退款录入账单支持
                        Integer serviceType = orderAfterSaleDTOS.get(0).getServiceType();
                        List<Long> orderAfterSaleIds = orderAfterSaleExecutorContext.load(serviceType).batchCreateOrderAfterSale(orderAfterSaleDTOS);
                        afterSaleIds.addAll(orderAfterSaleIds);
                    }
                    return afterSaleIds;
                });
                if (CollectionUtils.isEmpty(afterSaleIdList)) {
                    throw new BizException("创建售后单失败");
                }
            } catch (Exception e) {
                log.error("创建售后单失败,orderAfterSaleMap:{}", JSON.toJSONString(orderAfterSaleMap), e);
                throw new BizException("创建售后单失败");
            }
        } finally {
            unlockOrder(keys);
        }
        return DubboResponse.getOK(afterSaleIdList);
    }

    private Map<Long, List<OrderAfterSaleDTO>> validOrderAndTransferParam(OrderAfterSaleBatchReq orderAfterSaleBatchReq, List<Long> orderIdList) {
        List<Order> orders = orderDao.listByIds(orderIdList);
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, Function.identity(), (v1, v2) -> v1));

        // 校验当前订单状态是否支持配送后售后
        Optional<Order> orderOptional = orders.stream().filter(order -> !OrderStatusEnum.ableApplyDeliveredAfterSale(order.getStatus())).findFirst();
        if (orderOptional.isPresent()) {
            log.info("订单当前状态不支持发起配送后售后,订单号={}", orderOptional.get().getCustomerOrderId());
            throw new BizException("订单当前状态不支持发起配送后售后,订单号：" + orderOptional.get().getCustomerOrderId(), OpenApiErrorCode.ORDER_STATUS_UN_SUPPORT_CODE);
        }

        Map<Long, List<OrderAfterSaleDTO>> orderAfterSaleMap = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().collect(Collectors.groupingBy(OrderAfterSaleDTO::getOrderId));

        for (Map.Entry<Long, List<OrderAfterSaleDTO>> orderEntry : orderAfterSaleMap.entrySet()) {
            Long orderId = orderEntry.getKey();
            Order order = orderMap.get(orderId);
            List<OrderAfterSaleDTO> orderAfterSaleDTOS = orderEntry.getValue();
            if (Objects.isNull(order)) {
                log.info("售后单对应订单不存在, orderAfterSaleDTOS={},orderAfterSaleBatchReq={}", JSON.toJSON(orderAfterSaleDTOS), JSON.toJSONString(orderAfterSaleBatchReq));
                throw new BizException("售后单对应订单不存在,售后单：" + StringUtils.join(orderAfterSaleDTOS.stream()
                        .map(OrderAfterSaleDTO::getCustomerAfterSaleOrderNo).collect(Collectors.toList()), ','), OpenApiErrorCode.CREATE_AFTER_VALID_CODE);
            }

            orderAfterSaleBizService.checkDataAndBuildDto(orderAfterSaleDTOS, order);

//            orderAfterSaleDTOS.forEach(orderAfterSaleDTO -> {
//                LocalDateTime autoFinishedTme = orderAfterSaleBizService.orderAfterSaleDataCheck(orderAfterSaleDTO, order);
//                orderAfterSaleDTO.setAutoFinishedTime(autoFinishedTme);
//            });

        }
        return orderAfterSaleMap;
    }

    /**
     * 外部订单号加锁
     *
     * @param keys
     * @param orderIdList
     */
    private void lockOrder(List<String> keys, List<Long> orderIdList) {
        for (Long orderId : orderIdList) {
            String redisKey = RedisKeyEnum.OC00001.join(orderId);
            RLock lock = redissonClient.getLock(redisKey);
            // 未获取到锁，退出
            if (!lock.tryLock()) {
                throw new BizException("部分订单正在发起售后，请稍后重试", OpenApiErrorCode.CREATE_AFTER_CONCURRENCE_VALID_CODE);
            }
            keys.add(redisKey);
        }
    }

    /**
     * 外部订单号解锁
     *
     * @param keys
     */
    private void unlockOrder(List<String> keys) {
        for (String key : keys) {
            RLock lock = redissonClient.getLock(key);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 请求中存在已处理的非失败单号，强校验拦截
     *
     * @param orderAfterSaleBatchReq
     * @param tenantId
     */
    private void checkCustomerAfterSaleOrderNo(OrderAfterSaleBatchReq orderAfterSaleBatchReq, Long tenantId) {
        List<String> customerAfterSaleOrderNoList = orderAfterSaleBatchReq.getApplyAfterSaleList().stream().map(OrderAfterSaleDTO::getCustomerAfterSaleOrderNo).collect(Collectors.toList());
        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setCustomerAfterSaleOrderNos(customerAfterSaleOrderNoList);
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryListByParam(orderAfterSaleQueryParam);
        // 若售后单处于常规状态，拦截
        Optional<OrderAfterSale> normalOrderAfterSale = orderAfterSales.stream().filter(orderAfterSale -> !AFTER_SALE_FAIL_STATUS.contains(orderAfterSale.getStatus())).findFirst();
        if (!CollectionUtils.isEmpty(orderAfterSales) && normalOrderAfterSale.isPresent()) {
            List<String> existList = orderAfterSales.stream().map(OrderAfterSale::getCustomerAfterSaleOrderNo).collect(Collectors.toList());
            log.info("存在外部系统单号已处理,orderAfterSaleQueryParam:{},tenantId:{},existList:{}", JSON.toJSONString(orderAfterSaleQueryParam), tenantId, JSON.toJSONString(existList));
            throw new BizException("存在外部系统单号已处理，请排除相关单号" + normalOrderAfterSale.get().getCustomerAfterSaleOrderNo(), OpenApiErrorCode.AFTER_ORDER_NO_NO_EXIST_CODE);
        }
    }

    @Override
    public DubboResponse<Boolean> serviceProviderReviewSubmissions(OrderAfterSaleAuditReq auditReq) {
        // 参数校验
        checkParams(auditReq);

        // 业务对象组装
        OrderAfterSaleAuditBO orderAfterSaleAuditBO = buildOrderAfterSaleAuditBO(auditReq);

        // 状态校验
        OrderAfterSale orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        if (!Objects.equals(orderAfterSale.getStatus(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue())) {
            throw new BizException("售后状态非待确认");
        }

        // 金额校验
        verifyPriceAndQuantity(orderAfterSale);

        if (Objects.equals(orderAfterSaleAuditBO.getAuditStatus(), AuditFlagEnum.AUDIT_SUCCESS.getFlag())) {
            // 审核通过处理
            orderAfterSaleExecutorContext.load(orderAfterSale.getServiceType()).serviceProviderPassSubmissions(orderAfterSaleAuditBO);
        } else {
            // 审核拒绝处理
            serviceProviderRejectSubmissions(orderAfterSaleAuditBO);
        }
        return DubboResponse.getOK(true);
    }

    private void serviceProviderRejectSubmissions(OrderAfterSaleAuditBO orderAfterSaleAuditBO) {
        OrderAfterSale orderAfterSale = orderAfterSaleAuditBO.getOrderAfterSale();
        Long id = orderAfterSale.getId();
        OrderAfterSale update = new OrderAfterSale();
        update.setId(id);
        update.setServiceProviderAuditTime(LocalDateTime.now());
        update.setHandleRemark(orderAfterSaleAuditBO.getHandleRemark());
        update.setStatus(AUDITED_FAILED.getValue());
        update.setResponsibilityType(orderAfterSaleAuditBO.getResponsibility());
        orderAfterSaleDao.updateById(update);
        log.info("变更售后单:{}状态为[已拒绝-5]", orderAfterSale.getAfterSaleOrderNo());
    }

    private OrderAfterSaleAuditBO buildOrderAfterSaleAuditBO(OrderAfterSaleAuditReq auditReq) {
        OrderAfterSale orderAfterSale = orderAfterSaleDao.queryByAfterSaleNo(auditReq.getAfterSaleOrderNo());
        return OrderAfterSaleAuditBO.builder()
                .auditStatus(auditReq.getAuditStatus())
                .handleRemark(auditReq.getHandleRemark())
                .totalPrice(auditReq.getTotalPrice())
                .responsibility(Optional.of(auditReq.getResponsibilityType()).map(Integer::valueOf).orElse(null))
                .orderAfterSale(orderAfterSale)
                .build();
    }

    private void checkParams(OrderAfterSaleAuditReq auditReq) {
        if (StringUtils.isEmpty(auditReq.getHandleRemark())) {
            throw new ParamsException("审核说明不能为空");
        }
        if (Objects.isNull(auditReq.getTotalPrice())) {
            throw new ParamsException("售后金额不能为空");
        }
        if (auditReq.getTotalPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new ParamsException("售后金额不能小于0");
        }
        if (Objects.isNull(auditReq.getAuditStatus())) {
            throw new ParamsException("审核状态不能为空");
        }
    }
}
