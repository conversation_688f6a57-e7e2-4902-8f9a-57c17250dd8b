package com.cosfo.ordercenter.service.provider.aftersale;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleCalRefundPriceReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleCountReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleEnableApplyReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleOutQueryDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.QueryBillOrderAfterSaleReq;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleOutDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.client.resp.QueryResentOrderAfterSaleDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleEnableApplyParam;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.OrderItemSnapshotService;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleConverter;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleOldConverter;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/17
 */
@Slf4j
@DubboService
public class OrderAfterSaleQueryProviderImpl implements OrderAfterSaleQueryService {

    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotService orderItemSnapshotService;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderDao orderDao;
    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;

    @DubboReference
    private FulfillmentOrderQueryProvider fulfillmentOrderQueryProvider;

    // 不包括【已拒绝】、【已取消】
    private static final List<Integer> ALL_AFTER_SALE_STATUS =
            Arrays.asList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(), OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                    OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                    OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(), OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue(),
                    OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue());
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;


    @Override
    public DubboResponse<List<OrderAfterSaleOutDTO>> queryOrderAfterSaleInfo(OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO) {
        if (orderAfterSaleOutQueryDTO.getAfterSaleOrderNos().size() > 20) {
            throw new BizException("售后单数量不能大于20个");
        }

        Long tenantId = orderAfterSaleOutQueryDTO.getTenantId();
        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setAfterSaleOrderNos(orderAfterSaleOutQueryDTO.getAfterSaleOrderNos());
        // 查询售后单信息
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam);
        if (CollectionUtils.isEmpty(orderAfterSales)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        // 订单项
        List<Long> orderItemIds = orderAfterSales.stream().map(OrderAfterSale::getOrderItemId).collect(Collectors.toList());
        List<OrderItem> orderItems = orderItemDao.batchQuery(tenantId, orderItemIds);
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, item -> item));
        Map<Long, OrderItemSnapshot> itemSnapshotMap = orderItemSnapshotService.queryOrderItemSnapshot(orderItems);
        List<OrderAfterSaleOutDTO> orderAfterSaleOutDTOS = orderAfterSales.stream().map(orderAfterSale -> {
            OrderItem orderItem = orderItemMap.get(orderAfterSale.getOrderItemId());
            OrderItemSnapshot orderItemSnapshot = itemSnapshotMap.get(orderAfterSale.getOrderItemId());
            return OrderAfterSaleOldConverter.buildOrderAfterSaleOutDTO(orderAfterSale, orderItem, orderItemSnapshot);
        }).collect(Collectors.toList());
        return DubboResponse.getOK(orderAfterSaleOutDTOS);
    }

    @Override
    public DubboResponse<List<OrderAfterSaleDTO>> queryByOrderId(Long orderId, Long tenantId) {
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryByOrderId(orderId, tenantId);
        return DubboResponse.getOK(OrderAfterSaleConverter.INSTANCE.entitys2Dtos(orderAfterSales));
    }

    @Override
    public DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> queryEnableApply(OrderAfterSaleEnableApplyReq req) {
        OrderAfterSaleEnableApplyParam orderAfterSaleEnableApplyParam = new OrderAfterSaleEnableApplyParam();
        orderAfterSaleEnableApplyParam.setOrderId(req.getOrderId());
        orderAfterSaleEnableApplyParam.setTenantId(req.getTenantId());
        if (Objects.nonNull(req.getOrderItemId())) {
            orderAfterSaleEnableApplyParam.setOrderItemIds(Collections.singletonList(req.getOrderItemId()));
        }
        return DubboResponse.getOK(orderAfterSaleBizService.queryEnableApplyByApplyParam(orderAfterSaleEnableApplyParam));
    }



    @Override
    public DubboResponse<Integer> countOrderAfterSale(OrderAfterSaleCountReq orderAfterSaleCountReq) {
        return DubboResponse.getOK(orderAfterSaleDao.countOrderAfterSale(orderAfterSaleCountReq));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleDTO>> queryList(OrderAfterSaleQueryReq orderAfterSaleQueryReq) {
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryList(orderAfterSaleQueryReq);
        return DubboResponse.getOK(OrderAfterSaleConverter.INSTANCE.entitys2Dtos(orderAfterSales));
    }

    @Override
    public DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> queryPage(OrderAfterSalePageQueryReq req) {
        Page<OrderAfterSale> orderAfterSalePage = orderAfterSaleDao.queryPage(req);
        if (orderAfterSalePage == null || CollectionUtils.isEmpty(orderAfterSalePage.getRecords())) {
            return DubboResponse.getOK(PageInfo.emptyPageInfo());
        }
        List<Long> orderItemIds = orderAfterSalePage.getRecords().stream().map(OrderAfterSale::getOrderItemId).collect(Collectors.toList());
        Map<Long, OrderItem> orderItemMap = getOrderItemMap(orderItemIds);
        Map<Long, OrderItemSnapshot> snapshotMap = getSnapshotMap(orderItemIds);
        Map<Long, Order> orderMap = getOrderMap(orderItemIds);
        List<OrderAfterSaleWithOrderDTO> afterSaleWithOrderDTOList = orderAfterSalePage.getRecords().stream()
                .map(orderAfterSale -> handleOrderAfterSaleDetailInfo(orderAfterSale, orderItemMap, snapshotMap, orderMap)).collect(Collectors.toList());
        PageInfo<OrderAfterSaleWithOrderDTO> result = new PageInfo<>();
        result.setList(afterSaleWithOrderDTOList);
        result.setPageNum(Long.valueOf(orderAfterSalePage.getCurrent()).intValue());
        result.setPageSize(Long.valueOf(orderAfterSalePage.getSize()).intValue());
        result.setTotal(orderAfterSalePage.getTotal());
        return DubboResponse.getOK(result);
    }

    private Map<Long, OrderItem> getOrderItemMap(List<Long> orderItemIds) {
        List<OrderItem> orderItems = orderItemDao.batchQuery(null, orderItemIds);
        Map<Long, OrderItem> orderItemMap = orderItems.stream().collect(Collectors.toMap(OrderItem::getId, o -> o));
        return orderItemMap;
    }

    private Map<Long, OrderItemSnapshot> getSnapshotMap(List<Long> orderItemIds) {
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshot> snapshotMap = orderItemSnapshots.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, o -> o));
        return snapshotMap;
    }

    private Map<Long, Order> getOrderMap(List<Long> orderItemIds) {
        List<Order> orders = orderDao.queryList(OrderQueryReq.builder().orderItemIds(orderItemIds).build());
        Map<Long, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getId, order -> order));
        return orderMap;
    }

    private static OrderAfterSaleWithOrderDTO handleOrderAfterSaleDetailInfo(OrderAfterSale orderAfterSale, Map<Long, OrderItem> orderItemMap, Map<Long, OrderItemSnapshot> snapshotMap,
            Map<Long, Order> orderMap) {
        OrderAfterSaleWithOrderDTO afterSaleWithOrderDTO = OrderAfterSaleConverter.INSTANCE.toWithOrder(orderAfterSale);
        OrderItem orderItem = orderItemMap.get(orderAfterSale.getOrderItemId());
        OrderItemSnapshot orderItemSnapshot = snapshotMap.get(orderAfterSale.getOrderItemId());
        Order order = orderMap.get(orderItem.getOrderId());
        afterSaleWithOrderDTO.setOrderType(order.getOrderType());
        afterSaleWithOrderDTO.setCombineOrderId(order.getCombineOrderId());
        afterSaleWithOrderDTO.setWarehouseType(order.getWarehouseType());
        afterSaleWithOrderDTO.setOrderNo (order.getOrderNo ());

        afterSaleWithOrderDTO.setTitle(orderItemSnapshot.getTitle());
        afterSaleWithOrderDTO.setMainPicture(orderItemSnapshot.getMainPicture());
        afterSaleWithOrderDTO.setSpecification(orderItemSnapshot.getSpecification());
        afterSaleWithOrderDTO.setSpecificationUnit(orderItemSnapshot.getSpecificationUnit());
        afterSaleWithOrderDTO.setSupplyPrice(orderItemSnapshot.getSupplyPrice());
        afterSaleWithOrderDTO.setGoodsType(orderItemSnapshot.getGoodsType());
        afterSaleWithOrderDTO.setSupplierTenantId(orderItemSnapshot.getSupplierTenantId());
        afterSaleWithOrderDTO.setAfterSaleUnit(orderItemSnapshot.getAfterSaleUnit());
        afterSaleWithOrderDTO.setMaxAfterSaleAmount(orderItemSnapshot.getMaxAfterSaleAmount());
        afterSaleWithOrderDTO.setSkuId(orderItemSnapshot.getSkuId());

        afterSaleWithOrderDTO.setOrderAmount(orderItem.getAmount());
        afterSaleWithOrderDTO.setOrderPrice(orderItem.getTotalPrice());
        afterSaleWithOrderDTO.setPrice(orderItem.getPayablePrice());
        afterSaleWithOrderDTO.setPayablePrice(orderItem.getPayablePrice());
        afterSaleWithOrderDTO.setItemId(orderItem.getItemId());
        return afterSaleWithOrderDTO;
    }

    @Override
    public DubboResponse<List<OrderAfterSaleDTO>> queryByNos(List<String> orderAfterSaleNos) {
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryByNos(orderAfterSaleNos);
        List<OrderAfterSaleDTO> orderAfterSaleDTOS = OrderAfterSaleConverter.INSTANCE.entitys2Dtos (orderAfterSales);
        fillOrderNo(orderAfterSaleDTOS);
        return DubboResponse.getOK(orderAfterSaleDTOS);    }

    @Override
    public DubboResponse<List<OrderAfterSaleDTO>> queryByIds(List<Long> orderAfterSaleIds) {
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryByIds(orderAfterSaleIds);
        List<OrderAfterSaleDTO> orderAfterSaleDTOS = OrderAfterSaleConverter.INSTANCE.entitys2Dtos (orderAfterSales);
        fillOrderNo(orderAfterSaleDTOS);
        return DubboResponse.getOK(orderAfterSaleDTOS);
    }

    private void fillOrderNo(List<OrderAfterSaleDTO> orderAfterSaleDTOS) {
        if(CollectionUtil.isEmpty (orderAfterSaleDTOS)){
            return;
        }
        List<Long> orderItemIds = orderAfterSaleDTOS.stream().map(OrderAfterSaleDTO::getOrderItemId).collect(Collectors.toList());
        Map<Long, OrderItem> orderItemMap = getOrderItemMap(orderItemIds);
        Map<Long, Order> orderMap = getOrderMap(orderItemIds);
        for(OrderAfterSaleDTO dto : orderAfterSaleDTOS){
            OrderItem orderItem = orderItemMap.get(dto.getOrderItemId());
            Order order = orderMap.get(orderItem.getOrderId());
            dto.setOrderNo (order.getOrderNo ());
        }
    }

    @Override
    public DubboResponse<BigDecimal> calculateRefundPrice(OrderAfterSaleCalRefundPriceReq req) {
        Integer quantity = req.getQuantity();
        OrderItem orderItem = orderItemDao.getById(req.getOrderItemId());
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderItem.getId());

        BigDecimal applyPrice = NumberUtil.mul(quantity, orderItem.getTotalPrice());
        Integer maxApplyAmount = orderItem.getAmount() * orderItemSnapshot.getMaxAfterSaleAmount();
        BigDecimal refundPrice = NumberUtil.div(applyPrice, maxApplyAmount, 2);
        OrderAfterSaleEnableApplyReq enableApplyReq = new OrderAfterSaleEnableApplyReq();
        enableApplyReq.setTenantId(orderItem.getTenantId());
        enableApplyReq.setOrderItemId(orderItem.getId());
        enableApplyReq.setOrderId(orderItem.getOrderId());

        Map<Long, OrderAfterSaleEnableDTO> enableApplyMap = RpcResultUtil.handle(queryEnableApply(enableApplyReq));
        OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = enableApplyMap.get(req.getOrderItemId());
        BigDecimal enableApplyPrice = orderAfterSaleEnableDTO.getEnableApplyPrice();
        return DubboResponse.getOK(NumberUtil.min(enableApplyPrice, refundPrice));
    }

    @Override
    public DubboResponse<Map<Long, Integer>> countOrderAfterSaleByOrderId(OrderAfterSaleCountReq req) {
        return DubboResponse.getOK(orderAfterSaleDao.countOrderAfterSaleByOrderId(req));
    }

    @Override
    public DubboResponse<Long> getRecentlyUsedReturnAddressId(Long tenantId) {
        return DubboResponse.getOK(orderAfterSaleDao.getRecentlyUsedReturnAddressId(tenantId));
    }

    @Override
    public DubboResponse<List<OrderAfterSaleDTO>> queryOrderAfterSaleForBill(QueryBillOrderAfterSaleReq req) {
        List<OrderAfterSale> orderAfterSaleList = orderAfterSaleDao.queryOrderAfterSaleForBill(req);
        return DubboResponse.getOK(OrderAfterSaleConverter.INSTANCE.entitys2Dtos(orderAfterSaleList));
    }

    @Override
    public DubboResponse<List<QueryResentOrderAfterSaleDTO>> queryResentOrderAfterSaleForTms(@Valid QueryResentOrderAfterSaleReq req) {
        List<Order> orderList = orderDao.queryByStoreIdAndDeliveryTime(req.getStoreId(), req.getDeliveryDate().atStartOfDay(), WarehouseTypeEnum.THREE_PARTIES.getCode());
        if (CollectionUtils.isEmpty(orderList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setSkuList(Collections.singletonList(req.getSku()));
        List<ProductsMappingResp> handle = RpcResultUtil.handle(productsMappingQueryProvider.selectMappingList(queryReq));
        ProductsMappingResp productsMappingResp = handle.stream().max(Comparator.comparing(ProductsMappingResp::getId)).orElse(new ProductsMappingResp());
        Long agentSkuId = productsMappingResp.getAgentSkuId();

        if (agentSkuId == null) {
            log.warn("skucode={}, 未找到货品记录", req.getSku());
            return DubboResponse.getOK(Collections.emptyList());
        }

        List<Long> orderIds = orderList.stream().map(Order::getId).collect(Collectors.toList());

        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderIds(orderIds);
        if (CollectionUtils.isEmpty(orderItemSnapshots)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        List<Long> orderItemIdList = orderItemSnapshots.stream().filter(e -> agentSkuId.equals(e.getSupplierSkuId())).map(OrderItemSnapshot::getOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIdList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        OrderAfterSaleQueryParam param = new OrderAfterSaleQueryParam();
        param.setOrderItemIds(orderItemIdList);
        List<OrderAfterSale> orderAfterSaleList = orderAfterSaleDao.queryListByCondition(param);
        if (CollectionUtils.isEmpty(orderAfterSaleList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        LocalDateTime createStartTime = req.getCreateDate().atStartOfDay();
        LocalDateTime createEndTime = req.getCreateDate().plusDays(1).atStartOfDay();

        List<QueryResentOrderAfterSaleDTO> resultList = orderAfterSaleList.stream()
                .filter(e -> (OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(e.getServiceType())
                        && e.getCreateTime().isAfter(createStartTime)
                        && e.getCreateTime().isBefore(createEndTime)))
                .map(e -> {
                    QueryResentOrderAfterSaleDTO dto = new QueryResentOrderAfterSaleDTO();
                    dto.setAfterSaleOrderNo(e.getAfterSaleOrderNo());
                    dto.setCreateTime(e.getCreateTime());
                    dto.setCreateUser(StringUtils.isBlank(e.getOperatorName()) ? "系统" : e.getOperatorName());
                    return dto;
                })
                .collect(Collectors.toList());

        return DubboResponse.getOK(resultList);
    }
}
