package com.cosfo.ordercenter.service.provider.aftersale;

import com.cosfo.ordercenter.client.resp.OrderAfterSaleRuleDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleRuleService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleRuleDao;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSaleRule;
import com.cosfo.ordercenter.service.converter.OrderAfterSaleRuleConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 *
 * @author: xiaowk
 * @date: 2023/8/10 下午6:38
 */
@Slf4j
@DubboService
public class OrderAfterSaleRuleProviderImpl implements OrderAfterSaleRuleService {

    @Resource
    private OrderAfterSaleRuleDao orderAfterSaleRuleDao;

    @Override
    public DubboResponse<Long> add(OrderAfterSaleRuleDTO orderAfterSaleRuleDTO) {
        OrderAfterSaleRule addObj = OrderAfterSaleRuleConverter.INSTANCE.toEntity(orderAfterSaleRuleDTO);
        Long id = orderAfterSaleRuleDao.addOrUpdate(addObj);
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<List<OrderAfterSaleRuleDTO>> queryByTenantId(Long tenantId) {
        List<OrderAfterSaleRule> orderAfterSaleRuleList = orderAfterSaleRuleDao.queryByTenantId(tenantId);
        return DubboResponse.getOK(OrderAfterSaleRuleConverter.INSTANCE.toDTOList(orderAfterSaleRuleList));
    }

    @Override
    public DubboResponse<Boolean> updateRule(OrderAfterSaleRuleDTO orderAfterSaleRuleDTO) {
        Integer count = orderAfterSaleRuleDao.updateRule(OrderAfterSaleRuleConverter.INSTANCE.toEntity(orderAfterSaleRuleDTO));
        return DubboResponse.getOK(count > 0);
    }

    @Override
    public DubboResponse<Boolean> deleteRule(Long id) {
        Integer count = orderAfterSaleRuleDao.deleteRule(id);
        return DubboResponse.getOK(count > 0);
    }
}
