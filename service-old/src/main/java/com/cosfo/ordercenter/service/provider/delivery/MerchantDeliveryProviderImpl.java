package com.cosfo.ordercenter.service.provider.delivery;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.common.WarehouseTypeEnum;
import com.cosfo.ordercenter.client.req.MerchantDeliveryRuleQueryReq;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryRuleInfoDTO;
import com.cosfo.ordercenter.client.service.MerchantDeliveryService;
import com.cosfo.ordercenter.client.validgroup.QueryRuleGroup;
import com.cosfo.ordercenter.service.biz.delivery.DeliveryFeeService;
import com.cosfo.ordercenter.service.biz.delivery.warehouse.executor.MerchantDeliveryWarehouseContext;
import com.cosfo.ordercenter.service.util.ValidatorUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/8/18 18:00
 * @Description:
 */
@Service
@Slf4j
@DubboService
public class MerchantDeliveryProviderImpl implements MerchantDeliveryService {
    @Resource
    private MerchantDeliveryWarehouseContext merchantDeliveryWarehouseContext;
    @Resource
    private DeliveryFeeService deliveryFeeService;


    /**
     * 查询所有规则
     * @param merchantDeliveryRuleQueryReq
     * @return
     */
    @Override
    public DubboResponse<List<MerchantDeliveryRuleInfoDTO>> queryAllRuleList(MerchantDeliveryRuleQueryReq merchantDeliveryRuleQueryReq) {
        return DubboResponse.getOK(deliveryFeeService.queryAllDeliveryRule(merchantDeliveryRuleQueryReq));
    }


    /**
     * 查询下单时的运费
     * @param deliveryDTO
     * @return
     */
    @Override
    public DubboResponse<MerchantDeliveryFeeSnapshotDTO> queryMerchantDeliveryFee(@Valid DeliveryTotalDTO deliveryDTO) {
        // 校验参数有效性
        ValidatorUtils.validateEntity(deliveryDTO, QueryRuleGroup.class);
        Integer warehouseType = deliveryDTO.getOrderInfoDTO().getWarehouseType();
        WarehouseTypeEnum warehouseTypeEnum = WarehouseTypeEnum.getByCode(warehouseType);
        if (Objects.isNull(warehouseTypeEnum)) {
            throw new ParamsException("请输入正确的仓库类型！");
        }
        deliveryDTO.getOrderInfoDTO().setWarehouseTypeEnum(warehouseTypeEnum);

        log.info("{},开始计算运费：{}",
            warehouseTypeEnum.getDesc(),
            JSON.toJSONString(deliveryDTO));

        // 规则中保存的省份没有"省""市"字样
        String storeProvince = deliveryDTO.getOrderInfoDTO().getStoreProvince();
        storeProvince = storeProvince.replaceAll("(?<!市)省$|(?<!市)市$", "");
        deliveryDTO.getOrderInfoDTO().setStoreProvince(storeProvince);

        // 查询所有规则
        List<MerchantDeliveryRuleInfoDTO> deliveryRuleInfoDTOS = deliveryFeeService.queryAllDeliveryRule(MerchantDeliveryRuleQueryReq.builder()
            .tenantId(deliveryDTO.getTenantId())
            .warehouseType(warehouseType)
            .warehouseNo(deliveryDTO.getOrderInfoDTO().getWarehouseNo())
            .build());
        if (CollectionUtils.isEmpty(deliveryRuleInfoDTOS)) {
            throw new BizException("未配置运费！");
        }
        log.info("{},开始计算运费。所有规则(含未命中)：{}",
            warehouseTypeEnum.getDesc(),
            JSON.toJSONString(deliveryRuleInfoDTOS));

        // 按仓库，分别计算运费
        MerchantDeliveryFeeSnapshotDTO merchantDeliveryFeeSnapshotDTO = merchantDeliveryWarehouseContext.load(warehouseType)
            .calculateDeliveryFee(deliveryDTO, deliveryRuleInfoDTOS);
        log.info("{},计算运费完成。打印快照：{}",
            warehouseTypeEnum.getDesc(),
            JSON.toJSONString(merchantDeliveryFeeSnapshotDTO));

        return DubboResponse.getOK(merchantDeliveryFeeSnapshotDTO);
    }
}
