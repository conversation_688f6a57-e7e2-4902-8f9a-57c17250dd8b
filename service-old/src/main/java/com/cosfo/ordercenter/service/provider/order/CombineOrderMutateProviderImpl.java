package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.service.CombineOrderMutateService;
import com.cosfo.ordercenter.dao.dao.CombineOrderDao;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CombineOrderMutateProviderImpl implements CombineOrderMutateService {

    @Resource
    private CombineOrderDao combineOrderDao;

    @Override
    public DubboResponse<Long> add(Long combineItemId, Long tenantId) {
        Long id = combineOrderDao.add(combineItemId, tenantId);
        return DubboResponse.getOK(id);
    }
}
