package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.service.CombineOrderQueryService;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.service.converter.OrderConverter;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class CombineOrderQueryProviderImpl implements CombineOrderQueryService {

    @Resource
    private OrderDao orderDao;

    @Override
    public DubboResponse<List<OrderDTO>> queryByCombineId(Long combineItemId, Long tenantId) {
        List<Order> orders = orderDao.queryByCombineId(combineItemId, tenantId);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTOList(orders));
    }

    @Override
    public DubboResponse<List<OrderDTO>> queryByCombineIds(Collection<Long> combineItemIds, Long tenantId) {
        List<Order> orders = orderDao.queryByCombineIds(Sets.newHashSet(combineItemIds), tenantId);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTOList(orders));
    }
}
