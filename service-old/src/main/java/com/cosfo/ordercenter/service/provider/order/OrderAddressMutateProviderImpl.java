package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.service.OrderAddressMutateService;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.service.converter.OrderAddressConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderAddressMutateProviderImpl implements OrderAddressMutateService {

    @Resource
    private OrderAddressDao orderAddressDao;

    @Override
    public DubboResponse<Long> add(OrderAddressDTO orderAddressDTO) {

        OrderAddress entity = OrderAddressConvert.INSTANCE.toEntity(orderAddressDTO);
        entity.setId(null);
        return DubboResponse.getOK(orderAddressDao.add(entity));
    }
}
