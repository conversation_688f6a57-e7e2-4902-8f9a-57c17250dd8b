package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.service.OrderAddressQueryService;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.service.converter.OrderAddressConvert;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderAddressQueryProviderImpl implements OrderAddressQueryService {

    @Resource
    private OrderAddressDao orderAddressDao;

    @Override
    public DubboResponse<OrderAddressDTO> queryByOrderId(Long tenantId, Long orderId) {
        List<OrderAddress> orderAddresses = orderAddressDao.queryByOrderIds(Lists.newArrayList(orderId), tenantId);
        if (CollectionUtils.isEmpty(orderAddresses)) {
            return DubboResponse.getDefaultError("订单地址不存在");
        }
        return DubboResponse.getOK(OrderAddressConvert.INSTANCE.toDTO(orderAddresses.get(0)));
    }

    @Override
    public DubboResponse<List<OrderAddressDTO>> queryByOrderIds(Long tenantId, List<Long> orderIds) {
        List<OrderAddress> orderAddresses = orderAddressDao.queryByOrderIds(orderIds, tenantId);
        return DubboResponse.getOK(OrderAddressConvert.INSTANCE.toDTOList(orderAddresses));

    }
}
