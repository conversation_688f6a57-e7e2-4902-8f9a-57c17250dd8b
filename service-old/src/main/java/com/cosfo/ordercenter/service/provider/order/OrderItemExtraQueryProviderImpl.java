package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.req.OrderItemExtraQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemExtraDTO;
import com.cosfo.ordercenter.client.service.OrderItemExtraQueryService;
import com.cosfo.ordercenter.dao.dao.OrderItemExtraDao;
import com.cosfo.ordercenter.dao.model.param.OrderItemExtraQueryParam;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;
import com.cosfo.ordercenter.service.converter.OrderItemExtraConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-09-26
 * @Description:
 */
@Slf4j
@DubboService
public class OrderItemExtraQueryProviderImpl implements OrderItemExtraQueryService {

    @Resource
    private OrderItemExtraDao orderItemExtraDao;

    @Override
    public DubboResponse<List<OrderItemExtraDTO>> queryOrderItemExtraList(OrderItemExtraQueryReq orderItemExtraQueryReq) {
        OrderItemExtraQueryParam orderItemExtraQueryParam = OrderItemExtraQueryParam.builder().tenantId(orderItemExtraQueryReq.getTenantId())
                .orderId(orderItemExtraQueryReq.getOrderId())
                .customerOrderItemIdList(orderItemExtraQueryReq.getCustomerOrderItemIdList()).build();
        List<OrderItemExtra> orderItemExtraList = orderItemExtraDao.queryList(orderItemExtraQueryParam);

        return DubboResponse.getOK(OrderItemExtraConvert.INSTANCE.toDTOList(orderItemExtraList));
    }
}
