package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.common.OrderStatusEnum;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.OrderItemStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateDeliveryQuantityReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.service.OrderItemMutateService;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderItemMutateProviderImpl implements OrderItemMutateService {

    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private TransactionTemplate transactionTemplate;

    public static final List<Integer> NOT_SENT_FINISHED_AFTER_SALE_STATUS_LIST = Lists.newArrayList(OrderAfterSaleStatusEnum.REFUNDING.getValue(), OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue());

    @Override
    public DubboResponse<Boolean> updateAfterSaleExpiryTime(OrderItemUpdateReq dto) {
        return DubboResponse.getOK(orderItemDao.updateAfterSaleExpiryTime(dto));
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> updateDeliveryQuantity(OrderItemUpdateDeliveryQuantityReq dto) {
        if (CollectionUtils.isEmpty(dto.getOrderItemQuantities())) {
            throw new BizException("配送明细不能为空");
        }
        Set<Long> hasRefundOrderItemIdSet = Collections.emptySet();

        OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setStatusList(NOT_SENT_FINISHED_AFTER_SALE_STATUS_LIST);
        req.setOrderIds(Collections.singletonList(dto.getOrderId()));
        req.setAfterSaleType(OrderAfterSaleTypeEnum.NOT_SEND.getType());
        List<OrderAfterSale> orderAfterSaleList = orderAfterSaleDao.queryList(req);
        if (!CollectionUtils.isEmpty(orderAfterSaleList)) {
            hasRefundOrderItemIdSet = orderAfterSaleList.stream().map(OrderAfterSale::getOrderItemId).collect(Collectors.toSet());
        }

        try {
            Set<Long> finalHasRefundOrderItemIdSet = hasRefundOrderItemIdSet;
            Boolean result = transactionTemplate.execute((status) -> {
                // 更新订单明细已配送数量
                for (OrderItemUpdateDeliveryQuantityReq.OrderItemQuantity itemQuantity : dto.getOrderItemQuantities()) {
                    orderItemDao.updateDeliveryQuantity(itemQuantity.getOrderItemId(), itemQuantity.getQuantity());
                }
                //判断是否配送完成
                List<OrderItem> list = orderItemDao.queryByOrderId(dto.getOrderId());
                Optional<OrderItem> any = list.stream().filter(orderItem -> !finalHasRefundOrderItemIdSet.contains(orderItem.getId()) && orderItem.getAmount() > orderItem.getDeliveryQuantity())
                        .findAny();
                boolean isUnFinish = any.isPresent();
                //更新订单状态
                OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                updateReq.setOrderId(dto.getOrderId());
                updateReq.setStatus(isUnFinish ? OrderStatusEnum.SEGMENT_WAITING_DELIVERY.getCode() : OrderStatusEnum.DELIVERING.getCode());
                return orderDao.updateStatus(updateReq);
            });
        } catch (Exception ex) {
            throw new BizException("更新配送数量失败");
        }

        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateStatus(OrderItemStatusUpdateReq req) {
        return DubboResponse.getOK(orderItemDao.updateStatus(req));
    }

}
