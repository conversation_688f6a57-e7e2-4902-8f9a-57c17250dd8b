package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.dao.model.po.OrderItemWithSnapshot;
import com.cosfo.ordercenter.service.converter.OrderItemConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemQueryProviderImpl implements OrderItemQueryService {
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @Override
    public DubboResponse<OrderItemDTO> queryById(Long orderItemId) {
        OrderItem orderItem = orderItemDao.getById(orderItemId);
        return DubboResponse.getOK(OrderItemConverter.INSTANCE.toDTO(orderItem));
    }

    @Override
    public DubboResponse<OrderItemAndSnapshotDTO> queryDetailById(Long orderItemId) {
        OrderItem orderItem = orderItemDao.getById(orderItemId);
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderItemId);
        HashMap<Long, OrderItemSnapshot> snapshotMap = new HashMap<>();
        snapshotMap.put(orderItemId, orderItemSnapshot);
        OrderItemAndSnapshotDTO snapshot = OrderItemConverter.INSTANCE.toWithSnapshotDTO(orderItem, snapshotMap);
        return DubboResponse.getOK(snapshot);
    }

    @Override
    public DubboResponse<List<OrderItemAndSnapshotDTO>> queryByOrderId(Long orderId) {
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(orderId);
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        List<OrderItemSnapshot> snapshotList = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        Map<Long, OrderItemSnapshot> snapshotMap = snapshotList.stream().collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, snapshot -> snapshot));
        List<OrderItemAndSnapshotDTO> withSnapshotDTOList = OrderItemConverter.INSTANCE.toWithSnapshotDTOList(orderItems, snapshotMap);
        return DubboResponse.getOK(withSnapshotDTOList);
    }

    @Override
    public DubboResponse<List<OrderItemDTO>> queryOrderItemList(Long orderId) {
        List<OrderItem> orderItems = orderItemDao.queryByOrderId(orderId);
        return DubboResponse.getOK(OrderItemConverter.INSTANCE.toDTOList(orderItems));
    }

    @Override
    public DubboResponse<List<OrderItemAndSnapshotDTO>> queryOrderItemList(OrderItemQueryReq orderItemQueryReq) {
        if (CollectionUtils.isEmpty(orderItemQueryReq.getOrderIds())) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<OrderItemWithSnapshot> orderItemWithSnapshots = orderItemDao.batchQueryOrderItemDetail(orderItemQueryReq.getOrderIds(), orderItemQueryReq.getSupplierIds());
        return DubboResponse.getOK(OrderItemConverter.INSTANCE.toDTO(orderItemWithSnapshots));
    }

    @Override
    public DubboResponse<List<OrderItemDTO>> queryByIds(List<Long> orderItemIds) {
        List<OrderItem> orderItemDTOS = orderItemDao.queryByIds(orderItemIds);
        return DubboResponse.getOK(OrderItemConverter.INSTANCE.toDTOList(orderItemDTOS));
    }
}
