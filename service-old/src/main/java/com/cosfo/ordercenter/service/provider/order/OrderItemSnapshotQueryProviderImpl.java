package com.cosfo.ordercenter.service.provider.order;

import com.cosfo.ordercenter.client.req.OrderItemSnapshotQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.cosfo.ordercenter.client.service.OrderItemSnapshotQueryService;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.converter.OrderItemSnapshotConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderItemSnapshotQueryProviderImpl implements OrderItemSnapshotQueryService {
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @Override
    public DubboResponse<OrderItemSnapshotDTO> queryByOrderItemId(Long orderItemId) {
        OrderItemSnapshot orderItemSnapshot = orderItemSnapshotDao.queryByOrderItemId(orderItemId);
        return DubboResponse.getOK(OrderItemSnapshotConverter.INSTANCE.toDTO(orderItemSnapshot));
    }

    @Override
    public DubboResponse<List<OrderItemSnapshotDTO>> queryByOrderItemIds(List<Long> orderItemIds) {
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
        return DubboResponse.getOK(OrderItemSnapshotConverter.INSTANCE.toDTOList(orderItemSnapshots));
    }

    @Override
    public DubboResponse<List<OrderItemSnapshotDTO>> queryList(OrderItemSnapshotQueryReq queryReq) {
        List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryList(queryReq);
        return DubboResponse.getOK(OrderItemSnapshotConverter.INSTANCE.toDTOList(orderItemSnapshots));
    }
}
