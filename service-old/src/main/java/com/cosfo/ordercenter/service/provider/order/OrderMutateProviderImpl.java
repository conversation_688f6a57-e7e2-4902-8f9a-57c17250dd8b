package com.cosfo.ordercenter.service.provider.order;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.OrderAfterSaleQueryReq;
import com.cosfo.ordercenter.client.req.OrderCombineItemCreateReq;
import com.cosfo.ordercenter.client.req.OrderItemCreateReq;
import com.cosfo.ordercenter.client.req.OrderItemUpdateReq;
import com.cosfo.ordercenter.client.req.OrderSelfLiftReq;
import com.cosfo.ordercenter.client.req.OrderStatusBatchUpdateReq;
import com.cosfo.ordercenter.client.req.OrderStatusUpdateReq;
import com.cosfo.ordercenter.client.req.OrderUpdateDelivertDateReq;
import com.cosfo.ordercenter.client.req.OrderUpdateStoreNoReq;
import com.cosfo.ordercenter.client.req.ProfitSharingFinishTimeReq;
import com.cosfo.ordercenter.client.req.RefreshOrderAmountReq;
import com.cosfo.ordercenter.client.req.event.LockStockSuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderAuditReq;
import com.cosfo.ordercenter.client.req.event.OrderCancelReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.req.event.OrderConfirmReq;
import com.cosfo.ordercenter.client.req.event.OrderCreateReq;
import com.cosfo.ordercenter.client.req.event.OrderFulfillmentOrderCreateReq;
import com.cosfo.ordercenter.client.req.event.OrderPaySuccessReq;
import com.cosfo.ordercenter.client.req.event.OrderSelfLiftingFinishReq;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderItemAfterSaleRuleDTO;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.service.OrderAfterSaleQueryService;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.cosfo.ordercenter.client.service.OrderMutateService;
import com.cosfo.ordercenter.dao.dao.OrderAddressDao;
import com.cosfo.ordercenter.dao.dao.OrderCombineSnapshotDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderDeliveryFeeSnapshotDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemExtraDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.param.OrderDeliveryUpdateParam;
import com.cosfo.ordercenter.dao.model.param.OrderItemStatusBatchUpdateParam;
import com.cosfo.ordercenter.dao.model.param.OrderStatusUpdateParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAddress;
import com.cosfo.ordercenter.dao.model.po.OrderCombineSnapshot;
import com.cosfo.ordercenter.dao.model.po.OrderDeliveryFeeSnapshot;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemExtra;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.NotifyBizService;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.constant.DeliveryConstant;
import com.cosfo.ordercenter.service.constant.enums.RedisKeyEnum;
import com.cosfo.ordercenter.service.converter.MerchantDeliveryConvert;
import com.cosfo.ordercenter.service.converter.OrderAddressConvert;
import com.cosfo.ordercenter.service.converter.OrderConverter;
import com.cosfo.ordercenter.service.converter.OrderItemSnapshotConverter;
import com.cosfo.ordercenter.service.util.RpcResponseUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class OrderMutateProviderImpl implements OrderMutateService {

    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderCombineSnapshotDao orderCombineSnapshotDao;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private OrderItemExtraDao orderItemExtraDao;
    @Resource
    private OrderDeliveryFeeSnapshotDao orderDeliveryFeeSnapshotDao;
    @Resource
    private OrderItemQueryService orderItemQueryService;
    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;
    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @DubboReference
    private TenantProvider tenantProvider;
    @Resource
    private NotifyBizService notifyBizService;


    @Override
    public DubboResponse<Boolean> updateById(OrderDTO orderDTO) {
        return DubboResponse.getOK(orderDao.updateById(orderDTO));
    }

    @Override
    public DubboResponse<Boolean> updatePayType(OrderDTO orderDTO) {
        return DubboResponse.getOK(orderDao.updatePayType(orderDTO));
    }

    @Override
    public DubboResponse<Boolean> updateStatus(OrderStatusUpdateReq orderStatusUpdateReq) {
        return DubboResponse.getOK(orderDao.updateStatus(orderStatusUpdateReq));
    }

    @Override
    public DubboResponse<Integer> batchUpdateStatus(OrderStatusBatchUpdateReq orderStatusBatchUpdateReq) {
        Integer result = orderDao.batchUpdateStatus(orderStatusBatchUpdateReq);
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Boolean> selfLifting(OrderSelfLiftReq orderSelfLiftReq) {
        try {
            Boolean result = transactionTemplate.execute(status -> {
                List<OrderItemAndSnapshotDTO> data = RpcResponseUtil.handler(orderItemQueryService.queryByOrderId(orderSelfLiftReq.getOrderId()));
                List<OrderItem> updateList = data.stream().map(snapshotDTO -> {
                    OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = JSON.parseObject(snapshotDTO.getAfterSaleRule(), OrderItemAfterSaleRuleDTO.class);
                    OrderItem orderItem = new OrderItem();
                    orderItem.setId(snapshotDTO.getOrderItemId());
                    orderItem.setAfterSaleExpiryTime(LocalDateTime.now().plusHours(orderItemAfterSaleRuleDTO.getApplyEndTime()));
                    return orderItem;
                }).collect(Collectors.toList());
                boolean re = orderItemDao.batchUpdateAfterSaleExpiryTime(updateList);
                re = re & orderDao.selfLifting(orderSelfLiftReq);
                return re;
            });
            if (result == null || !result) {
                throw new BizException("自提失败");
            }
        } catch (Exception e) {
            log.error("自提失败", e);
            throw new BizException("自提失败");
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> batchUpdateProfitSharingFinishTime(@Valid ProfitSharingFinishTimeReq profitSharingFinishTimeReq) {

        List<Order> updateList = new ArrayList<>();
        for (ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder profitSharingFinishTimeOrder : profitSharingFinishTimeReq.getProfitSharingFinishTimeOrderList()) {
            Order order = new Order();
            order.setId(profitSharingFinishTimeOrder.getOrderId());
            order.setProfitSharingFinishTime(profitSharingFinishTimeOrder.getProfitSharingFinishTime());
            updateList.add(order);
        }
        return DubboResponse.getOK(orderDao.batchUpdateProfitSharingFinishTime(updateList));
    }

    @Override
    public DubboResponse<Boolean> auditSuccess(@Valid OrderAuditReq req) {
        try {
            OrderStatusUpdateParam updateParam = new OrderStatusUpdateParam();
            updateParam.setOrderId(req.getOrderId());
            updateParam.setOriginStatusList(Collections.singletonList(OrderStatusEnum.WAIT_AUDIT.getCode()));
            updateParam.setStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
            boolean result = orderDao.updateStatusByOriginStatus(updateParam);
            log.info("订单审核通过, 订单状态更新结果={}", result);
            if(!result){
                throw new BizException("订单审核状态更新失败");
            }
        } catch (Exception e) {
            log.error("订单审核状态更新失败，req={}", req, e);
            throw new BizException("订单审核状态更新失败");
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> close(OrderCloseReq orderCloseReq) {
        Order order = queryOrderId(orderCloseReq);
        if (order == null) {
            throw new BizException("该订单不存在");
        }
        List<Order> needCloseOrder = Lists.newArrayList(order);
        boolean isCombineOrder = OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType());
        if (isCombineOrder) {
            Long combineOrderId = order.getCombineOrderId();
            List<Order> orders = orderDao.queryByCombineId(combineOrderId, order.getTenantId());
            needCloseOrder = orders;
        }
        for (Order closeOrderDTO : needCloseOrder) {
            if (!OrderStatusEnum.ableApplyNotSendAfterSale(closeOrderDTO.getStatus())) {
                throw new BizException(isCombineOrder ? "组合包订单存在已配送订单，不能发起关单" : "订单存在已配送订单，不能发起关单");
            }
        }
        // 查询售后单
        List<OrderAfterSaleDTO> afterSaleList = getOrderAfterSaleDTOS(needCloseOrder);
        if (!CollectionUtils.isEmpty(afterSaleList)) {
            throw new BizException("该订单存在未处理售后单，请先处理售后单", 200, "CLOSE_ORDER_EXIST_AFTER_SALES");
        }

        List<Order> finalNeedCloseOrder = needCloseOrder;
        try {
            Boolean result = transactionTemplate.execute(status -> {
                // 更新订单状态为关单中
                updateOrderForClose(finalNeedCloseOrder);
                //创建售后单
                orderAfterSaleBizService.createAfterSaleOrderForClose(finalNeedCloseOrder);
                //自动审核
                orderAfterSaleBizService.autoReviewSubmissionsForClose(finalNeedCloseOrder);
                return true;
            });
            if (result == null || !result) {
                throw new BizException("关单失败");
            }
        } catch (Exception ex) {
            throw new BizException("关单失败");
        }

        return DubboResponse.getOK(true);
    }

    /**
     * 若是openAPi订单，更新customerOrderId,使得外部单号可以顺利再次下发
     *
     * @param finalNeedCloseOrder
     */
    private void updateCustomerOrderIdIfClosing(List<Order> finalNeedCloseOrder) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(finalNeedCloseOrder)) {
            return;
        }
        List<String> orderNos = finalNeedCloseOrder.stream().map(Order::getOrderNo).collect(Collectors.toList());
        List<Order> orders = orderDao.queryByOrderNos(Lists.newArrayList(orderNos));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders = orders.stream().filter(order -> OrderStatusEnum.CLOSING.getCode().equals(order.getStatus())).collect(Collectors.toList());

        String randomNumber = RandomStringUtils.randomAlphanumeric(5);
        for (Order closeOrder : orders) {
            if (Objects.isNull(closeOrder)) {
                continue;
            }
            if (!OrderSourceEnum.OPENAPI.getValue().equals(closeOrder.getOrderSource()) || StringUtils.isEmpty(closeOrder.getCustomerOrderId())) {
                continue;
            }
            if (closeOrder.getCustomerOrderId().length() > 44) {
                log.error("外部单号过长,不进行更新,:{}", closeOrder.getCustomerOrderId(), new Exception("外部单号过长"));
                continue;
            }
            String newCustomerOrderId = closeOrder.getCustomerOrderId() + RedisKeyEnum.SEPARATOR + randomNumber;
            Boolean updateResult = orderDao.updateOrderCustomerOrderId(closeOrder.getId(), newCustomerOrderId);
            if (!updateResult) {
                throw new BizException("该订单更新外部单号失败，关单失败");
            }
        }
    }

    private void updateOrderForClose(List<Order> needCloseOrder) {
        for (Order closeOrder : needCloseOrder) {
            OrderStatusUpdateParam updateParam = new OrderStatusUpdateParam();
            updateParam.setOrderId(closeOrder.getId());
            updateParam.setTenantId(closeOrder.getTenantId());
            updateParam.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(),OrderStatusEnum.WAIT_AUDIT.getCode(),OrderStatusEnum.WAITING_DELIVERY.getCode()));
            updateParam.setStatus(OrderStatusEnum.CLOSING.getCode());
            boolean result = orderDao.updateStatusByOriginStatus(updateParam);

//            OrderStatusUpdateReq statusUpdateReq = new OrderStatusUpdateReq();
//            statusUpdateReq.setOrderId(closeOrder.getId());
//            statusUpdateReq.setOriginStatus(OrderStatusEnum.WAIT_DELIVERY.getCode());
//            statusUpdateReq.setStatus(OrderStatusEnum.CLOSING.getCode());
//            statusUpdateReq.setTenantId(closeOrder.getTenantId());
//            Boolean updateResult = orderDao.updateStatus(statusUpdateReq);
//
//            OrderStatusUpdateReq waitingDeliveryStatusUpdateReq = new OrderStatusUpdateReq();
//            waitingDeliveryStatusUpdateReq.setOrderId(closeOrder.getId());
//            waitingDeliveryStatusUpdateReq.setOriginStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
//            waitingDeliveryStatusUpdateReq.setStatus(OrderStatusEnum.CLOSING.getCode());
//            waitingDeliveryStatusUpdateReq.setTenantId(closeOrder.getTenantId());
//            Boolean waitingDeliveryStatusResult = orderDao.updateStatus(waitingDeliveryStatusUpdateReq);
//            if (!updateResult && !waitingDeliveryStatusResult) {
            if (!result) {
                throw new BizException("该订单状态不是可关单状态，关单失败");
            }
        }
        // 若是外部下发订单，更换外部单号
        updateCustomerOrderIdIfClosing(needCloseOrder);
    }

    private List<OrderAfterSaleDTO> getOrderAfterSaleDTOS(List<Order> needCloseOrder) {
        OrderAfterSaleQueryReq queryReq = new OrderAfterSaleQueryReq();
        queryReq.setOrderIds(needCloseOrder.stream().map(Order::getId).collect(Collectors.toList()));
        queryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue()));
        List<OrderAfterSaleDTO> afterSaleList = RpcResultUtil.handle(orderAfterSaleQueryService.queryList(queryReq));
        return afterSaleList;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Long> create(OrderCreateReq req) {
        log.info("创建订单,req={}", req);
        Long orderId;
        try {
            orderId = transactionTemplate.execute(status -> {
                OrderDTO orderDTO = req.getOrderDTO();
                Order order = OrderConverter.INSTANCE.toEntity(orderDTO);
                orderDao.save(order);
                List<OrderItem> orderItems = handleOrderItem(req.getOrderItemList(), order);
                handleOrderItemSnapshot(req.getOrderItemList(), orderItems, order);
                handleDeliverySnapshot(req.getDeliveryFeeSnapshotDTO(), order);
                handleOrderAddress(req.getOrderAddressDTO(), order);
                // 开放平台-外部下单
                if (!StringUtils.isEmpty(orderDTO.getCustomerOrderId()) && OrderSourceEnum.OPENAPI.getValue().equals(orderDTO.getOrderSource())) {
                    handleOrderItemExtra(req.getOrderItemList(), orderItems);
                }
                return order.getId();
            });
            if (orderId == null) {
                throw new BizException("创建订单失败");
            }
        } catch (Exception ex) {
            throw new BizException("创建订单失败");
        }
        return DubboResponse.getOK(orderId);
    }

    private void handleOrderAddress(OrderAddressDTO addressDTO, Order order) {
        OrderAddress orderAddress = OrderAddressConvert.INSTANCE.toEntity(addressDTO);
        orderAddress.setId(null);
        orderAddress.setOrderId(order.getId());
        orderAddressDao.add(orderAddress);
    }

    private void handleDeliverySnapshot(MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO, Order order) {
        if (deliveryFeeSnapshotDTO == null) {
            return;
        }
        OrderDeliveryFeeSnapshot deliveryFeeSnapshot = MerchantDeliveryConvert.INSTANCE.convert2Po(deliveryFeeSnapshotDTO);
        deliveryFeeSnapshot.setId(null);
        deliveryFeeSnapshot.setOrderId(order.getId());
        orderDeliveryFeeSnapshotDao.save(deliveryFeeSnapshot);
    }

    private void handleOrderItemSnapshot(List<OrderItemCreateReq> orderItemCreateReqs, List<OrderItem> collect, Order order) {
        List<OrderItemSnapshot> snapshotList = new ArrayList<>();
        Map<Long, OrderItemCreateReq> itemCreateReqMap = orderItemCreateReqs.stream().collect(Collectors.toMap(OrderItemCreateReq::getItemId, o -> o));
        List<OrderCombineSnapshot> combineSnapshots = new ArrayList<>();
        collect.forEach(orderItem -> {
            OrderItemCreateReq orderItemCreateReq = itemCreateReqMap.get(orderItem.getItemId());
            OrderItemSnapshot orderItemSnapshot = OrderItemSnapshotConverter.INSTANCE.reqToEntity(orderItemCreateReq);
            orderItemSnapshot.setTenantId(orderItem.getTenantId());
            orderItemSnapshot.setOrderItemId(orderItem.getId());
            orderItemSnapshot.setMainPicture(Objects.isNull(orderItemCreateReq.getMainPicture()) ? null : orderItemCreateReq.getMainPicture().split(",")[0]);
            orderItemSnapshot.setAfterSaleRule(JSON.toJSONString(orderItemCreateReq.getOrderAfterSaleRule()));
            orderItemSnapshot.setOrderId(orderItem.getOrderId());
            if (Objects.equals(order.getOrderType(), OrderTypeEnum.COMBINE_ORDER.getValue())) {
                OrderCombineSnapshot combineSnapshot = new OrderCombineSnapshot();
                combineSnapshot.setTenantId(order.getTenantId());
                combineSnapshot.setCombineOrderId(order.getCombineOrderId());
                combineSnapshot.setCombineItemId(orderItemCreateReq.getCombineItemId());
                combineSnapshot.setItemId(orderItemCreateReq.getItemId());
                OrderCombineItemCreateReq orderCombineItemCreateReq = orderItemCreateReq.getOrderCombineItemCreateReq();
                combineSnapshot.setQuantity(orderCombineItemCreateReq.getQuantity());
                combineSnapshot.setOriginalPrice(orderCombineItemCreateReq.getOriginalPrice());
                combineSnapshot.setOrderItemId(orderItem.getId());
                combineSnapshots.add(combineSnapshot);
            }
            snapshotList.add(orderItemSnapshot);
        });
        // batch save snapshot
        orderItemSnapshotDao.batchSave(snapshotList);
        //
        if (Objects.equals(order.getOrderType(), OrderTypeEnum.COMBINE_ORDER.getValue())) {
            orderCombineSnapshotDao.batchSave(combineSnapshots);
        }
    }


    private void handleOrderItemExtra(List<OrderItemCreateReq> orderItemCreateReqs, List<OrderItem> collect) {
        List<OrderItemExtra> orderItemExtraList = new ArrayList<>();
        Map<Long, OrderItemCreateReq> itemCreateReqMap = orderItemCreateReqs.stream().collect(Collectors.toMap(OrderItemCreateReq::getItemId, o -> o));
        collect.forEach(orderItem -> {
            OrderItemCreateReq orderItemCreateReq = itemCreateReqMap.get(orderItem.getItemId());
            OrderItemExtra orderItemExtra = new OrderItemExtra();
            orderItemExtra.setTenantId(orderItem.getTenantId());
            orderItemExtra.setOrderId(orderItem.getOrderId());
            orderItemExtra.setOrderItemId(orderItem.getId());
            orderItemExtra.setCustomerOrderItemId(orderItemCreateReq.getCustomerOrderItemId());
            orderItemExtra.setCustomerSkuCode(orderItemCreateReq.getCustomerSkuCode());
            orderItemExtra.setCustomerSkuTitle(orderItemCreateReq.getCustomerSkuTitle());
            orderItemExtra.setCustomerSkuSpecification(orderItemCreateReq.getCustomerSkuSpecification());
            orderItemExtra.setCustomerSkuSpecificationUnit(orderItemCreateReq.getCustomerSkuSpecificationUnit());
            orderItemExtra.setSkuCode(orderItemCreateReq.getSupplySku());

            orderItemExtraList.add(orderItemExtra);
        });
        // batch save
        orderItemExtraDao.batchSave(orderItemExtraList);
    }

    private List<OrderItem> handleOrderItem(List<OrderItemCreateReq> orderItemCreateReqs, Order order) {
        List<OrderItem> collect = orderItemCreateReqs.stream().map(orderItemDTO -> {
            OrderItem orderItem = new OrderItem();
            orderItem.setItemId(orderItemDTO.getItemId());
            orderItem.setAmount(orderItemDTO.getAmount());
            orderItem.setTenantId(order.getTenantId());
            orderItem.setOrderId(order.getId());
            orderItem.setOrderType(order.getOrderType());
            orderItem.setPayablePrice(orderItemDTO.getPrice());
            if (order.getWarehouseNo() != null) {
                orderItem.setStoreNo(Integer.valueOf(order.getWarehouseNo()));
            }
            BigDecimal totalPrice = NumberUtil.mul(orderItemDTO.getPrice(), orderItemDTO.getAmount());
            if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(order.getWarehouseType())) {
                orderItem.setStatus(OrderItemStatusEnum.NO_PAYMENT.getCode());
            } else {
                orderItem.setStatus(OrderItemStatusEnum.CREATING_ORDER.getCode());
            }
            orderItem.setTotalPrice(totalPrice);
            return orderItem;
        }).collect(Collectors.toList());
        orderItemDao.batchSave(collect);
        return collect;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> cancel(OrderCancelReq req) {

        try {
            Boolean re = transactionTemplate.execute(status -> {
                OrderStatusBatchUpdateReq updateReq = new OrderStatusBatchUpdateReq();
                updateReq.setOrderIds(req.getOrderIds());
                updateReq.setTenantId(req.getTenantId());
                updateReq.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.NO_PAYMENT.getCode()));
                updateReq.setUpdateStatus(OrderStatusEnum.CANCELED.getCode());
                boolean result = orderDao.batchUpdateStatus(updateReq) == req.getOrderIds().size();
                log.info("取消订单，订单状态更新结果={}", result);

                OrderItemStatusBatchUpdateParam updateParam = new OrderItemStatusBatchUpdateParam();
                updateParam.setStatus(OrderItemStatusEnum.CANCELED.getCode());
                updateParam.setTenantId(req.getTenantId());
                updateParam.setOrderIds(req.getOrderIds());
                result = result & orderItemDao.batchUpdateStatus(updateParam);
                log.info("取消订单，订单项状态更新结果={}", result);

                if (!result) {
                    throw new ProviderException("订单取消失败");
                }
                return true;
            });

            if (re == null || !re) {
                throw new BizException("订单取消失败");
            }
        } catch (Exception ex) {
            throw new BizException("订单取消失败");
        }

        return DubboResponse.getOK(true);
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> lockStockSuccess(LockStockSuccessReq req) {

        try {
            Boolean re = transactionTemplate.execute(status -> {
                OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                updateReq.setOrderId(req.getOrderId());
                updateReq.setTenantId(req.getTenantId());
                updateReq.setDeliveryTime(req.getDeliveryTime());
                updateReq.setOriginStatus(OrderStatusEnum.CREATING_ORDER.getCode());
                updateReq.setStatus(OrderStatusEnum.NO_PAYMENT.getCode());
                boolean result = orderDao.updateStatus(updateReq);

                log.info("锁定库存成功, 订单状态更新结果={}", result);

                OrderItemStatusBatchUpdateParam updateParam = new OrderItemStatusBatchUpdateParam();
                updateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
                updateParam.setStoreNo(req.getStoreNo());
                updateParam.setTenantId(req.getTenantId());
                updateParam.setStatus(OrderItemStatusEnum.NO_PAYMENT.getCode());
                result = result & orderItemDao.batchUpdateStatus(updateParam);

                log.info("锁定库存成功, 订单项状态更新结果={}", result);

                if (!result) {
                    throw new BizException("订单状态更新失败");
                }
                return true;
            });

            if (re == null || !re) {
                throw new BizException("订单状态更新失败");
            }
        } catch (Exception ex) {
            throw new BizException("订单状态更新失败");
        }
        return DubboResponse.getOK(true);
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> fulfillmentOrderCreate(OrderFulfillmentOrderCreateReq req) {
        try {
            Boolean re = transactionTemplate.execute(status -> {
                OrderDeliveryUpdateParam updateParam = new OrderDeliveryUpdateParam();
                updateParam.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_AUDIT.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode()));
                updateParam.setOrderId(req.getOrderId());
                updateParam.setDeliveryTime(req.getDeliveryTime());
                updateParam.setFulfillmentNo(req.getFulfillmentNo());
                boolean result = orderDao.updateDeliveryTime(updateParam);
                log.info("履约单创建, 订单项状态更新结果={}", result);

                if (req.getStoreNo() != null) {
                    OrderItemStatusBatchUpdateParam statusBatchUpdateParam = new OrderItemStatusBatchUpdateParam();
                    statusBatchUpdateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
                    statusBatchUpdateParam.setStoreNo(req.getStoreNo());
                    statusBatchUpdateParam.setStatus(OrderItemStatusEnum.PAID.getCode());
                    result = result & orderItemDao.batchUpdateStatus(statusBatchUpdateParam);
                }

                if (!result) {
                    throw new ProviderException("订单更新失败");
                }
                return true;
            });
            if (re == null || !re) {
                throw new BizException("订单更新失败");
            }
        } catch (Exception e) {
            throw new BizException("订单更新失败");
        }

        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> paySuccess(OrderPaySuccessReq req) {
        try {
            Integer targetStatus = OrderStatusEnum.WAITING_DELIVERY.getCode();
            Order order = orderDao.getById(req.getOrderId());
            if(PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ()) && !order.getOrderType ().equals (OrderTypeEnum.PRESALE_ORDER.getValue ())){
                targetStatus = OrderStatusEnum.WAIT_AUDIT.getCode ();
            }else {
                // 不是三方仓订单，查询是否需要审核订单
                if (!WarehouseTypeEnum.THREE_PARTIES.getCode ().equals (order.getWarehouseType ())) {
                    boolean needAuditFlag = RpcResponseUtil.handler (tenantProvider.getOrderAuditSwitch (order.getStoreId ()));
                    targetStatus = needAuditFlag ? OrderStatusEnum.WAIT_AUDIT.getCode () : OrderStatusEnum.WAITING_DELIVERY.getCode ();
                } else if (WarehouseTypeEnum.THREE_PARTIES.getCode ().equals (order.getWarehouseType ()) && OrderTypeEnum.PRESALE_ORDER.getValue ().equals (order.getOrderType ())) {
                    targetStatus = OrderStatusEnum.WAIT_DELIVERY.getCode ();
                }
            }

            Integer finalTargetStatus = targetStatus;
            boolean r = Boolean.TRUE.equals(transactionTemplate.execute(status -> {
                OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                updateReq.setOrderId(req.getOrderId());
                updateReq.setOriginStatus(OrderStatusEnum.NO_PAYMENT.getCode());
                updateReq.setStatus(finalTargetStatus);
                boolean result = orderDao.updateStatus(updateReq);
                log.info("订单支付成功, 订单状态更新结果={}", result);

                OrderItemStatusBatchUpdateParam batchUpdateParam = new OrderItemStatusBatchUpdateParam();
                batchUpdateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
                batchUpdateParam.setStatus(OrderItemStatusEnum.PAID.getCode());
                result = result & orderItemDao.batchUpdateStatus(batchUpdateParam);

                log.info("订单支付成功, 订单项状态更新结果={}", result);

                if (!result) {
                    throw new BizException("订单状态更新失败");
                }
                return true;
            }));
            if (!r) {
                throw new BizException("订单状态更新失败");
            }
        } catch (Exception e) {
            log.error("订单状态更新失败", e);
            throw new BizException("订单状态更新失败");
        }
        return DubboResponse.getOK(true);
    }

    private Order queryOrderId(OrderCloseReq orderCloseReq) {
        if (orderCloseReq.getOrderId() != null) {
            return orderDao.getById(orderCloseReq.getOrderId());
        }
        // 查询组合单
        if (!StringUtils.isEmpty(orderCloseReq.getOrderNo())) {
            List<Order> orders = orderDao.queryByOrderNos(Lists.newArrayList(orderCloseReq.getOrderNo()));
            return CollectionUtils.isEmpty(orders) ? null : orders.get(0);
        }
        return null;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public DubboResponse<Boolean> confirm(OrderConfirmReq req) {

        try {
            Boolean re = transactionTemplate.execute(status -> {
                OrderStatusUpdateReq updateReq = new OrderStatusUpdateReq();
                updateReq.setStatus(OrderStatusEnum.FINISHED.getCode());
                updateReq.setOrderId(req.getOrderId());
                updateReq.setTenantId(req.getTenantId());
                boolean result = orderDao.updateStatus(updateReq);
                log.info("确认收货, 订单状态更新结果={}", result);
                List<OrderItem> orderItems = orderItemDao.queryByOrderId(req.getOrderId());
                List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
                // 查询订单快照
                List<OrderItemSnapshot> orderItemSnapshots = orderItemSnapshotDao.queryByOrderItemIds(orderItemIds);
                Map<Long, String> ruleMap = orderItemSnapshots.stream()
                    .collect(Collectors.toMap(OrderItemSnapshot::getOrderItemId, OrderItemSnapshot::getAfterSaleRule));
                // 更新可申请售后时间
                for (OrderItem orderItem : orderItems) {
                    String rule = ruleMap.get(orderItem.getId());
                    OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = JSON.parseObject(rule, OrderItemAfterSaleRuleDTO.class);
                    LocalDateTime afterSaleExpiryTime = LocalDateTime.now().plusHours(orderItemAfterSaleRuleDTO.getApplyEndTime());
                    OrderItemUpdateReq orderItemUpdateReq = new OrderItemUpdateReq();
                    orderItemUpdateReq.setOrderItemId(orderItem.getId());
                    orderItemUpdateReq.setAfterSaleExpiryTime(afterSaleExpiryTime);
                    result = result & orderItemDao.updateAfterSaleExpiryTime(orderItemUpdateReq);
                    log.info("确认收货，订单项={}更新结果={}", orderItem.getId(), result);
                }
                if (!result) {
                    throw new BizException("订单状态更新失败");
                }
                return true;
            });
            if (re == null || !re) {
                throw new BizException("订单状态更新失败");
            }
        } catch (Exception e) {
            throw new BizException("订单状态更新失败");
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> finish() {
        log.warn("接口废弃");
        return DubboResponse.getOK(false);
    }

    @Override
    public DubboResponse<Integer> updateOrderDeliveryTime(@Valid OrderUpdateDelivertDateReq req) {
        List<Order> sourceOrderList = orderDao.queryByOrderNos(req.getOrderNoList());
        Integer count = orderDao.updateOrderDeliveryTime(req.getOrderNoList(), req.getDeliveryDate().atStartOfDay());
        notifyBizService.updateOrderDeliveryTimeSmsNotify(sourceOrderList, req.getDeliveryDate());
        log.info("订单列表{},数量{},更新配送时间为[{}],更新数量{}", req.getOrderNoList(), req.getOrderNoList().size(), req.getDeliveryDate(), count);
        return DubboResponse.getOK(count);
    }

    @Override
    public DubboResponse<Boolean> selfLiftingFinish(OrderSelfLiftingFinishReq req) {
        return DubboResponse.getOK(orderDao.selfLiftingFinish(req));
    }

    @Override
    public DubboResponse<Boolean> updateOrderStoreNo(@Valid OrderUpdateStoreNoReq req) {
        List<Order> orderList = orderDao.queryByOrderNos(Collections.singletonList(req.getOrderNo()));
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BizException("该订单不存在");
        }
        boolean result = orderItemDao.updateStoreNo(orderList.get(0).getId(), req.getSourceStoreNo(), req.getStoreNo());
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<OrderDTO> refreshOrderAmount(@Valid RefreshOrderAmountReq req) {
        OrderDTO orderDTO = null;
        try {
            orderDTO = transactionTemplate.execute(status -> {
                // 更新原订单
                Order order = orderDao.updateDeliveryFee(req.getOrderId(), req.getDeliveryFeeSnapshotDTO().getDeliveryFee(), req.getOriDeliveryFee());

                // 更新原快照
                boolean effective = orderDeliveryFeeSnapshotDao.updateEffectiveFlag(req.getOrderId(), DeliveryConstant.SCENE_PLACE_ORDER);
                if (!effective) {
                    throw new BizException("更新运费快照失败！");
                }

                // 新增快照
                handleDeliverySnapshot(req.getDeliveryFeeSnapshotDTO(), order);

                return OrderConverter.INSTANCE.toDTO(order);
            });
            if (orderDTO == null) {
                throw new BizException("更新订单金额失败!");
            }
            return DubboResponse.getOK(orderDTO);
        } catch (Exception ex) {
            log.error("更新订单金额失败", ex);
            throw new BizException("更新订单金额失败");
        }


    }
}
