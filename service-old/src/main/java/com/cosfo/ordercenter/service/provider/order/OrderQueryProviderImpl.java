package com.cosfo.ordercenter.service.provider.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.client.common.OrderAfterSaleTypeEnum;
import com.cosfo.ordercenter.client.req.OrderNeedDeliveryReq;
import com.cosfo.ordercenter.client.req.OrderOmsQueryReq;
import com.cosfo.ordercenter.client.req.OrderOutQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderItemOutDTO;
import com.cosfo.ordercenter.client.resp.OrderOutDTO;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.cosfo.ordercenter.dao.dao.*;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.*;
import com.cosfo.ordercenter.service.biz.OrderItemSnapshotService;
import com.cosfo.ordercenter.service.config.RequestSourceConfig;
import com.cosfo.ordercenter.service.constant.Constant;
import com.cosfo.ordercenter.service.converter.OrderAddressConvert;
import com.cosfo.ordercenter.service.converter.OrderConverter;
import com.cosfo.ordercenter.service.converter.OrderItemConverter;
import com.cosfo.ordercenter.service.converter.OrderParamConverter;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class OrderQueryProviderImpl implements OrderQueryService {

    @Resource
    private OrderDao orderDao;
    @Resource
    private RequestSourceConfig requestSourceConfig;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;
    @Resource
    private OrderAfterSaleDao orderAfterSaleDao;
    @Resource
    private OrderAddressDao orderAddressDao;
    @Resource
    private OrderItemSnapshotService orderItemSnapshotService;

    @Override
    public DubboResponse<OrderDTO> queryById(Long orderId) {
        Order order = orderDao.getById(orderId);
        OrderDTO dto = OrderConverter.INSTANCE.toDTO(order);
        return DubboResponse.getOK(dto);
    }

    @Override
    public DubboResponse<OrderDTO> queryByNo(String orderNo) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo);
        queryWrapper.last("limit 1");
        Order order = orderDao.getOne(queryWrapper);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTO(order));
    }

    @Override
    public DubboResponse<List<OrderDTO>> queryByIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<Order> orders = orderDao.listByIds(orderIds);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTOList(orders));
    }

    @Override
    public DubboResponse<List<OrderDTO>> queryByNos(List<String> orderNos) {
        if (CollectionUtils.isEmpty(orderNos)) {
            return DubboResponse.getOK(Collections.emptyList());
        }
        List<Order> orders = orderDao.queryByOrderNos(orderNos);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTOList(orders));
    }

    @Override
    public DubboResponse<List<OrderDTO>> queryOrderList(OrderQueryReq orderQueryReq) {
        int batchSize = Constant.DEFAULT_SIZE;
        if (orderQueryReq.getBatchSize() != null && orderQueryReq.getBatchSize() < batchSize) {
            batchSize = orderQueryReq.getBatchSize();
        }
        orderQueryReq.setBatchSize(batchSize);
        List<Order> list = orderDao.queryList(orderQueryReq);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toDTOList(list));
    }

    @Override
    public DubboResponse<PageInfo<OrderDTO>> queryOrderPage(OrderQueryReq orderQueryReq) {
        Page<Order> orderPage = orderDao.queryPage(orderQueryReq);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toPageInfo(orderPage));
    }

    @Override
    public DubboResponse<List<String>> queryNeedDeliveryOrder(OrderNeedDeliveryReq needDeliveryReq) {
        List<String> orderNoList = orderDao.queryNeedDeliveryOrder(OrderParamConverter.INSTANCE.reqToParam(needDeliveryReq));
        return DubboResponse.getOK(orderNoList);
    }

    @Override
    public DubboResponse<PageInfo<OrderDTO>> queryOmsOrderPage(OrderOmsQueryReq orderQueryReq) {
        String attachment = RpcContext.getContext().getAttachment(CommonConstants.REMOTE_APPLICATION_KEY);
        if (StringUtils.isEmpty(attachment) || !requestSourceConfig.getOms().equals(attachment)) {
            log.warn("订单列表接口调用错误，调用端:{}", attachment);
            throw new BizException("不支持此操作");
        }
        Page<Order> orderPage = orderDao.queryPage(orderQueryReq);
        return DubboResponse.getOK(OrderConverter.INSTANCE.toPageInfo(orderPage));
    }

    @Override
    public DubboResponse<List<OrderOutDTO>> queryOrderInfo(@Valid OrderOutQueryReq orderOutQueryReq) {
        if (orderOutQueryReq.getOrderNos().size() > 20) {
            throw new BizException("订单数量不能大于20个");
        }

        Long tenantId = orderOutQueryReq.getTenantId();
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setTenantId(tenantId);
        orderQueryReq.setOrderNos(orderOutQueryReq.getOrderNos());
        List<Order> orders = orderDao.queryList(orderQueryReq);
        if (CollectionUtils.isEmpty(orders)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        List<Long> orderIds = orders.stream().map(Order::getId).collect(Collectors.toList());
        List<OrderAddress> orderAddresses = orderAddressDao.queryByOrderIds(orderIds, tenantId);
        Map<Long, OrderAddress> orderAddressMap = orderAddresses.stream().collect(Collectors.toMap(OrderAddress::getOrderId, item -> item));
        List<OrderItem> orderItems = orderItemDao.batchQueryByOrderIds(orderIds);
        Map<Long, List<OrderItem>> orderItemListMap = orderItems.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
        List<Long> orderItemIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toList());
        Map<Long, OrderItemSnapshot> itemSnapshotMap = orderItemSnapshotService.queryOrderItemSnapshot(orderItems);
        OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setTenantId(tenantId);
        orderAfterSaleQueryParam.setOrderItemIds(orderItemIds);
        List<OrderAfterSale> orderAfterSales = orderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam);
        Map<Long, List<OrderAfterSale>> orderAfterSaleDTOListMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderAfterSales)) {
            orderAfterSaleDTOListMap = orderAfterSales.stream()
                .collect(Collectors.groupingBy(OrderAfterSale::getOrderItemId));
        }

        List<OrderOutDTO> orderOutDTOS = assemblyOrderOutDTO(orders, orderItemListMap, itemSnapshotMap, orderAfterSaleDTOListMap, orderAddressMap);
        return DubboResponse.getOK(orderOutDTOS);
    }

    /**
     * 组装数据
     *
     * @param orders
     * @param orderItemListMap
     * @param itemSnapshotMap
     * @param orderAfterSaleDTOListMap
     * @return
     */
    private List<OrderOutDTO> assemblyOrderOutDTO(List<Order> orders,
                                                  Map<Long, List<OrderItem>> orderItemListMap,
                                                  Map<Long, OrderItemSnapshot> itemSnapshotMap,
                                                  Map<Long, List<OrderAfterSale>> orderAfterSaleDTOListMap,
                                                  Map<Long, OrderAddress> orderAddressMap) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<OrderOutDTO> orderOutDTOS = orders.stream().map(order -> {
            OrderOutDTO orderOutDTO = OrderConverter.INSTANCE.toOrderOutDTO(order);
            OrderAddress orderAddress = orderAddressMap.get(order.getId());
            OrderAddressDTO orderAddressDTO = OrderAddressConvert.INSTANCE.toDTO(orderAddress);
            orderOutDTO.setOrderAddressDTO(orderAddressDTO);
            List<OrderItem> orderItems = orderItemListMap.get(order.getId());
            if (CollectionUtils.isEmpty(orderItems)) {
                return orderOutDTO;
            }
            List<OrderItemOutDTO> orderItemOutDTOS = orderItems.stream().map(orderItem -> {
                OrderItemOutDTO orderItemOutDTO = OrderItemConverter.INSTANCE.toOrderItemOutDTO(orderItem, itemSnapshotMap);
                Integer needSendAmount = orderItem.getAmount();
                if (orderAfterSaleDTOListMap.containsKey(orderItem.getId())) {
                    // 发货数量 - 配送前售后数量
                    List<OrderAfterSale> orderAfterSales = orderAfterSaleDTOListMap.get(orderItem.getId());
                    Integer afterSaleAmount = orderAfterSales.stream().filter(orderAfterSale -> OrderAfterSaleTypeEnum.NOT_SEND.getType().equals(orderAfterSale.getAfterSaleType())).map(OrderAfterSale::getAmount).reduce(Integer::sum).orElse(0);
                    needSendAmount = orderItem.getAmount() - afterSaleAmount;
                }

                orderItemOutDTO.setNeedSendAmount(needSendAmount);
                return orderItemOutDTO;
            }).collect(Collectors.toList());
            orderOutDTO.setOrderItemOutDTOS(orderItemOutDTOS);
            return orderOutDTO;
        }).collect(Collectors.toList());
        return orderOutDTOS;
    }
}
