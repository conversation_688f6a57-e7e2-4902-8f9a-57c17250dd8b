package com.cosfo.ordercenter.service.provider.order;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.map.MapUtil;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.SupplierOrderTotalReq;
import com.cosfo.ordercenter.client.resp.OrderDetailDTO;
import com.cosfo.ordercenter.client.resp.OrderItemSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderSkuQuantityDTO;
import com.cosfo.ordercenter.client.resp.OrderSummaryDTO;
import com.cosfo.ordercenter.client.resp.SupplierOrderTotalResp;
import com.cosfo.ordercenter.client.service.OrderReportService;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.dto.SupplierOrderDTO;
import com.cosfo.ordercenter.dao.model.po.OrderDetail;
import com.cosfo.ordercenter.dao.model.po.OrderItemSaleQuantity;
import com.cosfo.ordercenter.dao.model.po.OrderSkuQuantity;
import com.cosfo.ordercenter.service.converter.OrderConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@DubboService
public class OrderReportProviderImpl implements OrderReportService {

    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderItemDao orderItemDao;
    @Resource
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @Override
    public DubboResponse<OrderSummaryDTO> queryOrderSummary(OrderSummaryReq orderSummaryReq) {
        OrderSummaryDTO summaryDTO = new OrderSummaryDTO();
        Integer orderQuantity = orderDao.countPayOrderQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);
        BigDecimal orderTotalPrice = orderDao.sumOrderTotalPrice(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);
        Integer orderStoreQuantity = orderDao.countPayOrderStoreQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), null);

        Integer directOrderQuantity = 0;
        Integer directOrderStoreQuantity = 0;
        BigDecimal directOrderTotalPrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderSummaryReq.getStoreIds())) {
            directOrderQuantity = orderDao.countPayOrderQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
            directOrderTotalPrice = orderDao.sumOrderTotalPrice(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
            directOrderStoreQuantity = orderDao.countPayOrderStoreQuantity(orderSummaryReq.getStartTime(), orderSummaryReq.getEndTime(), orderSummaryReq.getTenantId(), orderSummaryReq.getStoreIds());
        }
        summaryDTO.setPayOrderNum(orderQuantity);
        summaryDTO.setDirectStorePayOrderNum(directOrderQuantity);
        summaryDTO.setPayOrderTotalPrice(orderTotalPrice);
        summaryDTO.setDirectStorePayOrderTotalPrice(directOrderTotalPrice);
        summaryDTO.setPayOrderStoreNum(orderStoreQuantity);
        summaryDTO.setDirectStorePayOrderStoreNum(directOrderStoreQuantity);
        return DubboResponse.getOK(summaryDTO);
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityDTO>> querySkuSaleQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantity> orderSkuQuantities = orderDao.querySkuSaleQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return DubboResponse.getOK(OrderConverter.INSTANCE.skuToDTOList(orderSkuQuantities));
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityDTO>> querySkuSaleWithStoreNoQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantity> orderSkuQuantities = orderDao.querySkuSaleWithStoreNoQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return DubboResponse.getOK(OrderConverter.INSTANCE.skuToDTOList(orderSkuQuantities));
    }

    @Override
    public DubboResponse<List<OrderSkuQuantityDTO>> querySkuSaleWithCityQuantity(OrderSkuSaleReq orderSkuSaleReq) {
        List<OrderSkuQuantity> orderSkuQuantities = orderDao.querySkuSaleWithCityQuantity(orderSkuSaleReq.getSkuIds(), orderSkuSaleReq.getTenantId(), orderSkuSaleReq.getStartTime(), orderSkuSaleReq.getEndTime());
        return DubboResponse.getOK(OrderConverter.INSTANCE.skuToDTOList(orderSkuQuantities));
    }

    @Override
    public DubboResponse<Integer> getWaitDeliveryQuantity(Long tenantId) {
        return DubboResponse.getOK(orderDao.getWaitDeliveryNum(tenantId));
    }

    @Override
    public DubboResponse<List<OrderDetailDTO>> queryOrderDetail(OrderDetailReq orderDetailReq) {
        List<OrderDetail> orderDetails = orderDao.queryOrderDetail(orderDetailReq.getOrderIds(), orderDetailReq.getTenantId());
        return DubboResponse.getOK(OrderConverter.INSTANCE.detailToDTOList(orderDetails));
    }

    @Override
    public DubboResponse<OrderItemSaleDTO> querySkuSaleQuantity(List<Long> orderIds, Long tenantId) {
        OrderItemSaleDTO orderItemSaleDTO = new OrderItemSaleDTO();
        Integer saleQuantity = orderItemDao.querySaleQuantity(tenantId, orderIds);
        Integer skuQuantity = orderItemDao.querySkuQuantity(tenantId, orderIds);
        orderItemSaleDTO.setSaleQuantity(saleQuantity);
        orderItemSaleDTO.setSkuQuantity(skuQuantity);
        return DubboResponse.getOK(orderItemSaleDTO);
    }

    @Override
    public DubboResponse<Integer> countOrderQuantity(OrderCountReq orderCountReq) {
        return DubboResponse.getOK(orderDao.countOrderQuantity(orderCountReq));
    }

    @Override
    public DubboResponse<Map<Long, Integer>> countItemSaleQuantity(ItemSaleQuantityReq itemSaleQuantityReq) {
        List<OrderItemSaleQuantity> orderItemSaleQuantities = orderDao.queryOrderItemSaleQuantity(itemSaleQuantityReq);
        if (CollectionUtils.isEmpty(orderItemSaleQuantities)) {
            return DubboResponse.getOK(MapUtil.empty());
        }
        Map<Long, Integer> result = orderItemSaleQuantities.stream().collect(Collectors.toMap(OrderItemSaleQuantity::getItemId, OrderItemSaleQuantity::getQuantity));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<List<SupplierOrderTotalResp>> querySupplierOrderSummary(@Valid SupplierOrderTotalReq supplierOrderTotalReq) {
        List<SupplierOrderDTO> supplierOrderList = orderDao.querySupplierOrderList(supplierOrderTotalReq);
        if (CollectionUtils.isEmpty(supplierOrderList)) {
            return DubboResponse.getOK(Collections.emptyList());
        }

        Map<Long, List<SupplierOrderDTO>> supplierOrderMap = supplierOrderList.stream().collect(Collectors.groupingBy(SupplierOrderDTO::getSupplierId));

        List<SupplierOrderTotalResp> supplierOrderTotalResps = supplierOrderMap.entrySet().stream().map(entry -> {
            List<SupplierOrderDTO> supplierOrderDTOS = entry.getValue();
            // 供应商订单供应总金额
            BigDecimal supplierOrderTotalPrice = supplierOrderDTOS.stream()
                    .map(dto -> NumberUtil.mul(Optional.ofNullable(dto.getSupplyPrice()).orElse(BigDecimal.ZERO), dto.getAmount()))
                    .reduce(BigDecimal.ZERO, NumberUtil::add);
            SupplierOrderTotalResp supplierOrderTotalResp = new SupplierOrderTotalResp();
            supplierOrderTotalResp.setSupplierId(entry.getKey());
            supplierOrderTotalResp.setSupplierOrderTotalPrice(supplierOrderTotalPrice);
            // 订单项列表
            supplierOrderTotalResp.setOrderIds(supplierOrderDTOS.stream().map(SupplierOrderDTO::getOrderId).collect(Collectors.toSet()));
            return supplierOrderTotalResp;
        }).collect(Collectors.toList());
        return DubboResponse.getOK(supplierOrderTotalResps);
    }

}
