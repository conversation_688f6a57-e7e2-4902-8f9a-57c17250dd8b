package com.cosfo.ordercenter.service.util;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;

/**
 * <AUTHOR>
 */
@Slf4j
public class RpcResponseUtil {

    /**
     *
     * @param response
     * @return
     * @param <T>
     */
    public static <T> T handler(DubboResponse<T> response) {
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    public static <T> T handler(DubboResponse<T> response, String errMsg) {
        if (!response.isSuccess()) {
            throw new ProviderException(errMsg);
        }
        return response.getData();
    }
}
