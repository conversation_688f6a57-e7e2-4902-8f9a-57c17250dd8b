package com.cosfo.ordercenter.service.biz.aftersale.executor;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.service.biz.NotifyBizService;
import com.cosfo.ordercenter.service.biz.OrderAfterSaleBizService;
import com.cosfo.ordercenter.service.biz.StockBizService;

import java.math.BigDecimal;
import java.time.LocalDate;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ContextConfiguration(classes = {ExchangeAfterSaleExecutor.class})
@ExtendWith(SpringExtension.class)
class ExchangeAfterSaleExecutorTest {
    @Autowired
    private ExchangeAfterSaleExecutor exchangeAfterSaleExecutor;

    @MockBean
    private NotifyBizService notifyBizService;

    @MockBean
    private OrderAfterSaleBizService orderAfterSaleBizService;

    @MockBean
    private OrderAfterSaleDao orderAfterSaleDao;

    @MockBean
    private OrderDao orderDao;

    @MockBean
    private StockBizService stockBizService;

    /**
     * Method under test: {@link ExchangeAfterSaleExecutor#finish(List)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testFinish() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
        //       at java.util.ArrayList.rangeCheck(ArrayList.java:659)
        //       at java.util.ArrayList.get(ArrayList.java:435)
        //       at com.cosfo.ordercenter.service.biz.aftersale.executor.ExchangeAfterSaleExecutor.finish(ExchangeAfterSaleExecutor.java:179)
        //   See https://diff.blue/R013 to resolve this issue.

        exchangeAfterSaleExecutor.finish(new ArrayList<>());
    }

    /**
     * Method under test: {@link ExchangeAfterSaleExecutor#finish(List)}
     */
    @Test
    void testFinish2() {
        OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setAccountId(1L);
        orderAfterSale.setAdminRemark("Admin Remark");
        orderAfterSale.setAdminRemarkTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setAfterSaleOrderNo("After Sale Order No");
        orderAfterSale.setAfterSaleType(1);
        orderAfterSale.setAmount(10);
        orderAfterSale.setApplyPrice(BigDecimal.valueOf(1L));
        orderAfterSale.setApplyQuantity(1);
        orderAfterSale.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setDeliveryFee(BigDecimal.valueOf(1L));
        orderAfterSale.setFinishedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setHandleRemark("Handle Remark");
        orderAfterSale.setHandleTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setId(1L);
        orderAfterSale.setOperatorName("Operator Name");
        orderAfterSale.setOrderId(1L);
        orderAfterSale.setOrderItemId(1L);
        orderAfterSale.setProofPicture("Proof Picture");
        orderAfterSale.setReason("Just cause");
        orderAfterSale.setRecycleDetails("Recycle Details");
        orderAfterSale.setRecycleTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setResponsibilityType(1);
        orderAfterSale.setReturnAddressId(1L);
        orderAfterSale.setReturnWarehouseNo("Return Warehouse No");
        orderAfterSale.setSecondHandleRemark("Second Handle Remark");
        orderAfterSale.setServiceType(1);
        orderAfterSale.setStatus(1);
        orderAfterSale.setStoreId(1L);
        orderAfterSale.setStoreNo(1);
        orderAfterSale.setTenantId(1L);
        orderAfterSale.setTotalPrice(BigDecimal.valueOf(1L));
        orderAfterSale.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderAfterSale.setUserRemark("User Remark");
        orderAfterSale.setWarehouseType(1);
        when(orderAfterSaleDao.queryByAfterSaleNo(Mockito.<String>any())).thenReturn(orderAfterSale);

        OrderAfterSaleProcessFinishReq orderAfterSaleProcessFinishReq = new OrderAfterSaleProcessFinishReq();
        orderAfterSaleProcessFinishReq.setDeliveryType(1);
        orderAfterSaleProcessFinishReq.setOrderAfterSaleNo("Order After Sale No");
        orderAfterSaleProcessFinishReq.setRemark("Remark");
        orderAfterSaleProcessFinishReq.setShortCount(3);
        orderAfterSaleProcessFinishReq.setShouldCount(3);
        orderAfterSaleProcessFinishReq.setSku("Sku");
        orderAfterSaleProcessFinishReq.setState(1);

        ArrayList<OrderAfterSaleProcessFinishReq> reqs = new ArrayList<>();
        reqs.add(orderAfterSaleProcessFinishReq);
        assertFalse(exchangeAfterSaleExecutor.finish(reqs));
        verify(orderAfterSaleDao).queryByAfterSaleNo(Mockito.<String>any());
    }
}

