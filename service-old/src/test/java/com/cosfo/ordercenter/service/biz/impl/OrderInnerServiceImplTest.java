package com.cosfo.ordercenter.service.biz.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.dto.OrderAutoFinishDTO;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderItem;

import java.math.BigDecimal;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import net.xianmu.common.exception.ProviderException;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.support.TransactionTemplate;

@ContextConfiguration(classes = {OrderInnerServiceImpl.class})
@ExtendWith(SpringExtension.class)
class OrderInnerServiceImplTest {
    @MockBean
    private OrderDao orderDao;

    @Autowired
    private OrderInnerServiceImpl orderInnerServiceImpl;

    @MockBean
    private OrderItemDao orderItemDao;

    @MockBean
    private OrderItemSnapshotDao orderItemSnapshotDao;

    @MockBean
    private TransactionTemplate transactionTemplate;

    /**
     * Method under test: {@link OrderInnerServiceImpl#autoFinish(OrderAutoFinishDTO)}
     */
    @Test
    void testAutoFinish() {
        when(orderDao.queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any())).thenReturn(new ArrayList<>());

        OrderAutoFinishDTO autoFinishDTO = new OrderAutoFinishDTO();
        autoFinishDTO.setDeliveryTimeEnd(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setDeliveryTimeStart(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setLimit(1);
        assertEquals(0, orderInnerServiceImpl.autoFinish(autoFinishDTO));
        verify(orderDao).queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any());
    }

    /**
     * Method under test: {@link OrderInnerServiceImpl#autoFinish(OrderAutoFinishDTO)}
     */
    @Test
    void testAutoFinish2() {
        Order order = new Order();
        order.setAccountId(1L);
        order.setApplyEndTime(1);
        order.setBeginDeliveryTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setCombineOrderId(1L);
        order.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setDeliveryFee(BigDecimal.valueOf(1L));
        order.setDeliveryTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setFinishedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setId(1L);
        order.setOnlinePayChannel(2);
        order.setOrderNo("查询配送时间在[{}]-[{}]的待收货订单");
        order.setOrderType(1);
        order.setOrderVersion(1);
        order.setPayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setPayType(1);
        order.setPayablePrice(BigDecimal.valueOf(1L));
        order.setProfitSharingFinishTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setRemark("查询配送时间在[{}]-[{}]的待收货订单");
        order.setStatus(1);
        order.setStoreId(1L);
        order.setSupplierTenantId(1L);
        order.setTenantId(1L);
        order.setTotalPrice(BigDecimal.valueOf(1L));
        order.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setWarehouseNo("查询配送时间在[{}]-[{}]的待收货订单");
        order.setWarehouseType(1);

        ArrayList<Order> orderList = new ArrayList<>();
        orderList.add(order);
        when(orderDao.queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any())).thenReturn(orderList);
        when(orderItemDao.batchQueryByOrderIds(Mockito.<List<Long>>any())).thenReturn(new ArrayList<>());

        OrderAutoFinishDTO autoFinishDTO = new OrderAutoFinishDTO();
        autoFinishDTO.setDeliveryTimeEnd(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setDeliveryTimeStart(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setLimit(1);
        assertThrows(ProviderException.class, () -> orderInnerServiceImpl.autoFinish(autoFinishDTO));
        verify(orderDao).queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any());
        verify(orderItemDao).batchQueryByOrderIds(Mockito.<List<Long>>any());
    }

    /**
     * Method under test: {@link OrderInnerServiceImpl#autoFinish(OrderAutoFinishDTO)}
     */
    @Test
    void testAutoFinish3() {
        Order order = new Order();
        order.setAccountId(1L);
        order.setApplyEndTime(1);
        order.setBeginDeliveryTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setCombineOrderId(1L);
        order.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setDeliveryFee(BigDecimal.valueOf(1L));
        order.setDeliveryTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setFinishedTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setId(1L);
        order.setOnlinePayChannel(2);
        order.setOrderNo("查询配送时间在[{}]-[{}]的待收货订单");
        order.setOrderType(1);
        order.setOrderVersion(1);
        order.setPayTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setPayType(1);
        order.setPayablePrice(BigDecimal.valueOf(1L));
        order.setProfitSharingFinishTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setRemark("查询配送时间在[{}]-[{}]的待收货订单");
        order.setStatus(1);
        order.setStoreId(1L);
        order.setSupplierTenantId(1L);
        order.setTenantId(1L);
        order.setTotalPrice(BigDecimal.valueOf(1L));
        order.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        order.setWarehouseNo("查询配送时间在[{}]-[{}]的待收货订单");
        order.setWarehouseType(1);

        ArrayList<Order> orderList = new ArrayList<>();
        orderList.add(order);
        when(orderDao.queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any())).thenReturn(orderList);

        OrderItem orderItem = new OrderItem();
        orderItem.setAfterSaleExpiryTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderItem.setAmount(10);
        orderItem.setCreateTime(LocalDate.of(1970, 1, 1).atStartOfDay());
        orderItem.setDeliveryQuantity(1);
        orderItem.setId(1L);
        orderItem.setItemId(1L);
        orderItem.setOrderId(1L);
        orderItem.setOrderType(1);
        orderItem.setPayablePrice(BigDecimal.valueOf(1L));
        orderItem.setStatus(1);
        orderItem.setStoreNo(1);
        orderItem.setTenantId(1L);
        orderItem.setTotalPrice(BigDecimal.valueOf(1L));
        orderItem.setUpdateTime(LocalDate.of(1970, 1, 1).atStartOfDay());

        ArrayList<OrderItem> orderItemList = new ArrayList<>();
        orderItemList.add(orderItem);
        when(orderItemDao.batchQueryByOrderIds(Mockito.<List<Long>>any())).thenReturn(orderItemList);
        when(orderItemSnapshotDao.queryByOrderItemIds(Mockito.<List<Long>>any())).thenReturn(new ArrayList<>());

        OrderAutoFinishDTO autoFinishDTO = new OrderAutoFinishDTO();
        autoFinishDTO.setDeliveryTimeEnd(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setDeliveryTimeStart(LocalDate.of(1970, 1, 1).atStartOfDay());
        autoFinishDTO.setLimit(1);
        assertThrows(ProviderException.class, () -> orderInnerServiceImpl.autoFinish(autoFinishDTO));
        verify(orderDao).queryNeedAutoFinishedOrder(Mockito.<OrderAutoFinishDTO>any());
        verify(orderItemDao).batchQueryByOrderIds(Mockito.<List<Long>>any());
        verify(orderItemSnapshotDao).queryByOrderItemIds(Mockito.<List<Long>>any());
    }
}

