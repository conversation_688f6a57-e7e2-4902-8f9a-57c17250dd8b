package com.cosfo.ordercenter.service.provider.aftersale;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleEnableDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleOutDTO;
import com.cosfo.ordercenter.client.resp.OrderAfterSaleWithOrderDTO;
import com.cosfo.ordercenter.dao.dao.OrderAfterSaleDao;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.dao.OrderItemDao;
import com.cosfo.ordercenter.dao.dao.OrderItemSnapshotDao;
import com.cosfo.ordercenter.dao.model.param.OrderAfterSaleQueryParam;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.dao.model.po.OrderAfterSale;
import com.cosfo.ordercenter.dao.model.po.OrderItem;
import com.cosfo.ordercenter.dao.model.po.OrderItemSnapshot;
import com.cosfo.ordercenter.service.biz.OrderItemSnapshotService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.ofc.client.provider.FulfillmentOrderQueryProvider;
import net.summerfarm.ofc.client.req.QueryFulfillmentDeliveryReq;
import net.summerfarm.ofc.client.resp.FulfillmentDeliveryResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderAfterSaleQueryProviderImplTest {

    @Mock
    private OrderAfterSaleDao mockOrderAfterSaleDao;
    @Mock
    private OrderItemDao mockOrderItemDao;
    @Mock
    private OrderItemSnapshotService mockOrderItemSnapshotService;
    @Mock
    private OrderItemSnapshotDao mockOrderItemSnapshotDao;
    @Mock
    private OrderDao mockOrderDao;
    @Mock
    private FulfillmentOrderQueryProvider mockFulfillmentOrderQueryProvider;
    @InjectMocks
    private OrderAfterSaleQueryProviderImpl orderAfterSaleQueryProviderImplUnderTest;

    @Test
    void testQueryOrderAfterSaleInfo() {
        // Setup
        final OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO = new OrderAfterSaleOutQueryDTO();
        orderAfterSaleOutQueryDTO.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleOutQueryDTO.setTenantId(0L);

        // Configure OrderAfterSaleDao.queryListByCondition(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleQueryParam.setOrderItemIds(Arrays.asList(0L));
        orderAfterSaleQueryParam.setTenantId(0L);
        when(mockOrderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam)).thenReturn(orderAfterSales);

        // Configure OrderItemDao.batchQuery(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(orderItems);

        // Configure OrderItemSnapshotService.queryOrderItemSnapshot(...).
        final OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(0L);
        orderItem1.setTenantId(0L);
        orderItem1.setOrderId(0L);
        orderItem1.setItemId(0L);
        orderItem1.setAmount(0);
        orderItem1.setPayablePrice(new BigDecimal("0.00"));
        orderItem1.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems1 = Arrays.asList(orderItem1);
        when(mockOrderItemSnapshotService.queryOrderItemSnapshot(orderItems1)).thenReturn(new HashMap<>());

        // Run the test
        final DubboResponse<List<OrderAfterSaleOutDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryOrderAfterSaleInfo(
                orderAfterSaleOutQueryDTO);

        // Verify the results
    }

    @Test
    void testQueryOrderAfterSaleInfo_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO = new OrderAfterSaleOutQueryDTO();
        orderAfterSaleOutQueryDTO.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleOutQueryDTO.setTenantId(0L);

        // Configure OrderAfterSaleDao.queryListByCondition(...).
        final OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleQueryParam.setOrderItemIds(Arrays.asList(0L));
        orderAfterSaleQueryParam.setTenantId(0L);
        when(mockOrderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleOutDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryOrderAfterSaleInfo(
                orderAfterSaleOutQueryDTO);

        // Verify the results
    }

    @Test
    void testQueryOrderAfterSaleInfo_OrderItemDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleOutQueryDTO orderAfterSaleOutQueryDTO = new OrderAfterSaleOutQueryDTO();
        orderAfterSaleOutQueryDTO.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleOutQueryDTO.setTenantId(0L);

        // Configure OrderAfterSaleDao.queryListByCondition(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryParam orderAfterSaleQueryParam = new OrderAfterSaleQueryParam();
        orderAfterSaleQueryParam.setAfterSaleOrderNos(Arrays.asList("value"));
        orderAfterSaleQueryParam.setOrderItemIds(Arrays.asList(0L));
        orderAfterSaleQueryParam.setTenantId(0L);
        when(mockOrderAfterSaleDao.queryListByCondition(orderAfterSaleQueryParam)).thenReturn(orderAfterSales);

        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure OrderItemSnapshotService.queryOrderItemSnapshot(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemSnapshotService.queryOrderItemSnapshot(orderItems)).thenReturn(new HashMap<>());

        // Run the test
        final DubboResponse<List<OrderAfterSaleOutDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryOrderAfterSaleInfo(
                orderAfterSaleOutQueryDTO);

        // Verify the results
    }

    @Test
    void testQueryByOrderId() {
        // Setup
        // Configure OrderAfterSaleDao.queryByOrderId(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        when(mockOrderAfterSaleDao.queryByOrderId(0L, 0L)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByOrderId(
                0L, 0L);

        // Verify the results
    }

    @Test
    void testQueryByOrderId_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        when(mockOrderAfterSaleDao.queryByOrderId(0L, 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByOrderId(
                0L, 0L);

        // Verify the results
    }

    @Test
    void testQueryEnableApply() {
        // Setup
        final OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(0L);
        req.setOrderId(0L);
        req.setOrderItemId(0L);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(2);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("10.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(1);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
//        req1.setStatusList(Arrays.asList(0));
//        req1.setServiceType(Arrays.asList(0));
//        req1.setTenantId(0L);
//        req1.setSupplierIds(Arrays.asList(0L));
//        req1.setOrderIds(Arrays.asList(0L));
        req1.setOrderIds(Lists.newArrayList(order.getId()));
        req1.setStatusList( Arrays.asList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.INVENTORY_DEALING.getValue(), OrderAfterSaleStatusEnum.REFUNDING.getValue(),
                OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue(), OrderAfterSaleStatusEnum.INVENTORY_FAIl.getValue(), OrderAfterSaleStatusEnum.WAIT_REFUND.getValue(),
                OrderAfterSaleStatusEnum.THIRD_PROCESSING.getValue(), OrderAfterSaleStatusEnum.REFUNDDING_GOODS.getValue(),
                OrderAfterSaleStatusEnum.WAIT_REFUND_GOODS.getValue()));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(orderAfterSales);


        QueryFulfillmentDeliveryReq deliveryReq = new QueryFulfillmentDeliveryReq();
        deliveryReq.setOrderNoList(Lists.newArrayList(order.getOrderNo()));

        List<FulfillmentDeliveryResp> resps = new ArrayList<>();

        when(mockFulfillmentOrderQueryProvider.queryOrderDelivery(deliveryReq)).thenReturn(DubboResponse.getOK(resps));

        // Run the test
        Map<Long, OrderAfterSaleEnableDTO> handle = RpcResultUtil.handle(orderAfterSaleQueryProviderImplUnderTest.queryEnableApply(
                req));


        // Verify the results
        OrderAfterSaleEnableDTO orderAfterSaleEnableDTO = handle.get(0L);
        assertNotNull(orderAfterSaleEnableDTO);
        assertEquals(2, (int) orderAfterSaleEnableDTO.getEnableApplyAmount());
        assertEquals(0, orderAfterSaleEnableDTO.getEnableApplyPrice().compareTo(new BigDecimal("10")));
        assertEquals(2, (int) orderAfterSaleEnableDTO.getEnableApplyQuantity());
    }

    @Test
    void testQueryEnableApply_OrderDaoReturnsNull() {
        // Setup
        final OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(0L);
        req.setOrderId(0L);
        req.setOrderItemId(0L);

        when(mockOrderDao.getById(0L)).thenReturn(null);

        // Run the test
        final DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryEnableApply(
                req);

        // Verify the results
    }

    @Test
    void testQueryEnableApply_OrderItemDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(0L);
        req.setOrderId(0L);
        req.setOrderItemId(0L);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(Collections.emptyList());

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
        req1.setStatusList(Arrays.asList(0));
        req1.setServiceType(Arrays.asList(0));
        req1.setTenantId(0L);
        req1.setSupplierIds(Arrays.asList(0L));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryEnableApply(
                req);

        // Verify the results
    }

    @Test
    void testQueryEnableApply_OrderItemSnapshotDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(0L);
        req.setOrderId(0L);
        req.setOrderItemId(0L);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryEnableApply(
                req);

        // Verify the results
    }

    @Test
    void testQueryEnableApply_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleEnableApplyReq req = new OrderAfterSaleEnableApplyReq();
        req.setTenantId(0L);
        req.setOrderId(0L);
        req.setOrderItemId(0L);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
        req1.setStatusList(Arrays.asList(0));
        req1.setServiceType(Arrays.asList(0));
        req1.setTenantId(0L);
        req1.setSupplierIds(Arrays.asList(0L));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<Map<Long, OrderAfterSaleEnableDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryEnableApply(
                req);

        // Verify the results
    }

    @Test
    void testCountOrderAfterSale() {
        // Setup
        final OrderAfterSaleCountReq orderAfterSaleCountReq = new OrderAfterSaleCountReq();
        orderAfterSaleCountReq.setTenantId(0L);
        orderAfterSaleCountReq.setStoreId(0L);
        orderAfterSaleCountReq.setStatusList(Arrays.asList(0));
        orderAfterSaleCountReq.setOrderIds(Arrays.asList(0L));

        // Configure OrderAfterSaleDao.countOrderAfterSale(...).
        final OrderAfterSaleCountReq orderAfterSaleCountReq1 = new OrderAfterSaleCountReq();
        orderAfterSaleCountReq1.setTenantId(0L);
        orderAfterSaleCountReq1.setStoreId(0L);
        orderAfterSaleCountReq1.setStatusList(Arrays.asList(0));
        orderAfterSaleCountReq1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.countOrderAfterSale(orderAfterSaleCountReq1)).thenReturn(0);

        // Run the test
        final DubboResponse<Integer> result = orderAfterSaleQueryProviderImplUnderTest.countOrderAfterSale(
                orderAfterSaleCountReq);

        // Verify the results
    }

    @Test
    void testQueryList() {
        // Setup
        final OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setStatusList(Arrays.asList(0));
        orderAfterSaleQueryReq.setServiceType(Arrays.asList(0));
        orderAfterSaleQueryReq.setTenantId(0L);
        orderAfterSaleQueryReq.setSupplierIds(Arrays.asList(0L));
        orderAfterSaleQueryReq.setOrderIds(Arrays.asList(0L));

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setStatusList(Arrays.asList(0));
        req.setServiceType(Arrays.asList(0));
        req.setTenantId(0L);
        req.setSupplierIds(Arrays.asList(0L));
        req.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryList(
                orderAfterSaleQueryReq);

        // Verify the results
    }

    @Test
    void testQueryList_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setStatusList(Arrays.asList(0));
        orderAfterSaleQueryReq.setServiceType(Arrays.asList(0));
        orderAfterSaleQueryReq.setTenantId(0L);
        orderAfterSaleQueryReq.setSupplierIds(Arrays.asList(0L));
        orderAfterSaleQueryReq.setOrderIds(Arrays.asList(0L));

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSaleQueryReq req = new OrderAfterSaleQueryReq();
        req.setStatusList(Arrays.asList(0));
        req.setServiceType(Arrays.asList(0));
        req.setTenantId(0L);
        req.setSupplierIds(Arrays.asList(0L));
        req.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryList(
                orderAfterSaleQueryReq);

        // Verify the results
    }

    @Test
    void testQueryPage() {
        // Setup
        final OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(0L);
        req.setOrderNo("orderNo");
        req.setAfterSaleOrderNo("afterSaleOrderNo");
        req.setStatusList(Arrays.asList(0));
        req.setAfterSaleType(0);

        // Configure OrderAfterSaleDao.queryPage(...).
        final OrderAfterSalePageQueryReq req1 = new OrderAfterSalePageQueryReq();
        req1.setTenantId(0L);
        req1.setOrderNo("orderNo");
        req1.setAfterSaleOrderNo("afterSaleOrderNo");
        req1.setStatusList(Arrays.asList(0));
        req1.setAfterSaleType(0);
        when(mockOrderAfterSaleDao.queryPage(req1)).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Configure OrderItemDao.batchQuery(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderDao.queryList(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        final List<Order> orders = Arrays.asList(order);
        when(mockOrderDao.queryList(OrderQueryReq.builder()
                .orderItemIds(Arrays.asList(0L))
                .build())).thenReturn(orders);

        // Run the test
        final DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryPage(
                req);

        // Verify the results
    }

    @Test
    void testQueryPage_OrderAfterSaleDaoReturnsNull() {
        // Setup
        final OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(0L);
        req.setOrderNo("orderNo");
        req.setAfterSaleOrderNo("afterSaleOrderNo");
        req.setStatusList(Arrays.asList(0));
        req.setAfterSaleType(0);

        // Configure OrderAfterSaleDao.queryPage(...).
        final OrderAfterSalePageQueryReq req1 = new OrderAfterSalePageQueryReq();
        req1.setTenantId(0L);
        req1.setOrderNo("orderNo");
        req1.setAfterSaleOrderNo("afterSaleOrderNo");
        req1.setStatusList(Arrays.asList(0));
        req1.setAfterSaleType(0);
        when(mockOrderAfterSaleDao.queryPage(req1)).thenReturn(null);

        // Run the test
        final DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryPage(
                req);

        // Verify the results
    }

    @Test
    void testQueryPage_OrderItemDaoReturnsNoItems() {
        // Setup
        final OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(0L);
        req.setOrderNo("orderNo");
        req.setAfterSaleOrderNo("afterSaleOrderNo");
        req.setStatusList(Arrays.asList(0));
        req.setAfterSaleType(0);

        // Configure OrderAfterSaleDao.queryPage(...).
        final OrderAfterSalePageQueryReq req1 = new OrderAfterSalePageQueryReq();
        req1.setTenantId(0L);
        req1.setOrderNo("orderNo");
        req1.setAfterSaleOrderNo("afterSaleOrderNo");
        req1.setStatusList(Arrays.asList(0));
        req1.setAfterSaleType(0);
        when(mockOrderAfterSaleDao.queryPage(req1)).thenReturn(new Page<>(0L, 0L, 0L, false));

        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderDao.queryList(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        final List<Order> orders = Arrays.asList(order);
        when(mockOrderDao.queryList(OrderQueryReq.builder()
                .orderItemIds(Arrays.asList(0L))
                .build())).thenReturn(orders);

        // Run the test
        final DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryPage(
                req);

        // Verify the results
    }

    @Test
    void testQueryPage_OrderItemSnapshotDaoReturnsNoItems() {
        // Setup
        final OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(0L);
        req.setOrderNo("orderNo");
        req.setAfterSaleOrderNo("afterSaleOrderNo");
        req.setStatusList(Arrays.asList(0));
        req.setAfterSaleType(0);

        // Configure OrderAfterSaleDao.queryPage(...).
        final OrderAfterSalePageQueryReq req1 = new OrderAfterSalePageQueryReq();
        req1.setTenantId(0L);
        req1.setOrderNo("orderNo");
        req1.setAfterSaleOrderNo("afterSaleOrderNo");
        req1.setStatusList(Arrays.asList(0));
        req1.setAfterSaleType(0);
        when(mockOrderAfterSaleDao.queryPage(req1)).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Configure OrderItemDao.batchQuery(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(orderItems);

        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Configure OrderDao.queryList(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        final List<Order> orders = Arrays.asList(order);
        when(mockOrderDao.queryList(OrderQueryReq.builder()
                .orderItemIds(Arrays.asList(0L))
                .build())).thenReturn(orders);

        // Run the test
        final DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryPage(
                req);

        // Verify the results
    }

    @Test
    void testQueryPage_OrderDaoReturnsNoItems() {
        // Setup
        final OrderAfterSalePageQueryReq req = new OrderAfterSalePageQueryReq();
        req.setTenantId(0L);
        req.setOrderNo("orderNo");
        req.setAfterSaleOrderNo("afterSaleOrderNo");
        req.setStatusList(Arrays.asList(0));
        req.setAfterSaleType(0);

        // Configure OrderAfterSaleDao.queryPage(...).
        final OrderAfterSalePageQueryReq req1 = new OrderAfterSalePageQueryReq();
        req1.setTenantId(0L);
        req1.setOrderNo("orderNo");
        req1.setAfterSaleOrderNo("afterSaleOrderNo");
        req1.setStatusList(Arrays.asList(0));
        req1.setAfterSaleType(0);
        when(mockOrderAfterSaleDao.queryPage(req1)).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Configure OrderItemDao.batchQuery(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem);
        when(mockOrderItemDao.batchQuery(0L, Arrays.asList(0L))).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        when(mockOrderDao.queryList(OrderQueryReq.builder()
                .orderItemIds(Arrays.asList(0L))
                .build())).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<PageInfo<OrderAfterSaleWithOrderDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryPage(
                req);

        // Verify the results
    }

    @Test
    void testQueryByNos() {
        // Setup
        // Configure OrderAfterSaleDao.queryByNos(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        when(mockOrderAfterSaleDao.queryByNos(Arrays.asList("value"))).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByNos(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    void testQueryByNos_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        when(mockOrderAfterSaleDao.queryByNos(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByNos(
                Arrays.asList("value"));

        // Verify the results
    }

    @Test
    void testQueryByIds() {
        // Setup
        // Configure OrderAfterSaleDao.queryByIds(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        when(mockOrderAfterSaleDao.queryByIds(Arrays.asList(0L))).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByIds(
                Arrays.asList(0L));

        // Verify the results
    }

    @Test
    void testQueryByIds_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        when(mockOrderAfterSaleDao.queryByIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryByIds(
                Arrays.asList(0L));

        // Verify the results
    }

    @Test
    void testCalculateRefundPrice() {
        // Setup
        final OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(0L);
        req.setQuantity(0);

        // Configure OrderItemDao.getById(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        when(mockOrderItemDao.getById(0L)).thenReturn(orderItem);

        // Configure OrderItemSnapshotDao.queryByOrderItemId(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        when(mockOrderItemSnapshotDao.queryByOrderItemId(0L)).thenReturn(orderItemSnapshot);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(0L);
        orderItem1.setTenantId(0L);
        orderItem1.setOrderId(0L);
        orderItem1.setItemId(0L);
        orderItem1.setAmount(0);
        orderItem1.setPayablePrice(new BigDecimal("0.00"));
        orderItem1.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem1);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot1 = new OrderItemSnapshot();
        orderItemSnapshot1.setOrderItemId(0L);
        orderItemSnapshot1.setSkuId(0L);
        orderItemSnapshot1.setSupplierTenantId(0L);
        orderItemSnapshot1.setSupplierSkuId(0L);
        orderItemSnapshot1.setAreaItemId(0L);
        orderItemSnapshot1.setTitle("title");
        orderItemSnapshot1.setMainPicture("mainPicture");
        orderItemSnapshot1.setSpecificationUnit("specificationUnit");
        orderItemSnapshot1.setSpecification("specification");
        orderItemSnapshot1.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot1.setMaxAfterSaleAmount(0);
        orderItemSnapshot1.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot1.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot1);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
        req1.setStatusList(Arrays.asList(0));
        req1.setServiceType(Arrays.asList(0));
        req1.setTenantId(0L);
        req1.setSupplierIds(Arrays.asList(0L));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<BigDecimal> result = orderAfterSaleQueryProviderImplUnderTest.calculateRefundPrice(req);

        // Verify the results
    }

    @Test
    void testCalculateRefundPrice_OrderDaoReturnsNull() {
        // Setup
        final OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(0L);
        req.setQuantity(0);

        // Configure OrderItemDao.getById(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        when(mockOrderItemDao.getById(0L)).thenReturn(orderItem);

        // Configure OrderItemSnapshotDao.queryByOrderItemId(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        when(mockOrderItemSnapshotDao.queryByOrderItemId(0L)).thenReturn(orderItemSnapshot);

        when(mockOrderDao.getById(0L)).thenReturn(null);

        // Run the test
        final DubboResponse<BigDecimal> result = orderAfterSaleQueryProviderImplUnderTest.calculateRefundPrice(req);

        // Verify the results
    }

    @Test
    void testCalculateRefundPrice_OrderItemDaoQueryByOrderIdReturnsNoItems() {
        // Setup
        final OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(0L);
        req.setQuantity(0);

        // Configure OrderItemDao.getById(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        when(mockOrderItemDao.getById(0L)).thenReturn(orderItem);

        // Configure OrderItemSnapshotDao.queryByOrderItemId(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        when(mockOrderItemSnapshotDao.queryByOrderItemId(0L)).thenReturn(orderItemSnapshot);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(Collections.emptyList());

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot1 = new OrderItemSnapshot();
        orderItemSnapshot1.setOrderItemId(0L);
        orderItemSnapshot1.setSkuId(0L);
        orderItemSnapshot1.setSupplierTenantId(0L);
        orderItemSnapshot1.setSupplierSkuId(0L);
        orderItemSnapshot1.setAreaItemId(0L);
        orderItemSnapshot1.setTitle("title");
        orderItemSnapshot1.setMainPicture("mainPicture");
        orderItemSnapshot1.setSpecificationUnit("specificationUnit");
        orderItemSnapshot1.setSpecification("specification");
        orderItemSnapshot1.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot1.setMaxAfterSaleAmount(0);
        orderItemSnapshot1.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot1.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot1);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
        req1.setStatusList(Arrays.asList(0));
        req1.setServiceType(Arrays.asList(0));
        req1.setTenantId(0L);
        req1.setSupplierIds(Arrays.asList(0L));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<BigDecimal> result = orderAfterSaleQueryProviderImplUnderTest.calculateRefundPrice(req);

        // Verify the results
    }

    @Test
    void testCalculateRefundPrice_OrderItemSnapshotDaoQueryByOrderItemIdsReturnsNoItems() {
        // Setup
        final OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(0L);
        req.setQuantity(0);

        // Configure OrderItemDao.getById(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        when(mockOrderItemDao.getById(0L)).thenReturn(orderItem);

        // Configure OrderItemSnapshotDao.queryByOrderItemId(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        when(mockOrderItemSnapshotDao.queryByOrderItemId(0L)).thenReturn(orderItemSnapshot);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(0L);
        orderItem1.setTenantId(0L);
        orderItem1.setOrderId(0L);
        orderItem1.setItemId(0L);
        orderItem1.setAmount(0);
        orderItem1.setPayablePrice(new BigDecimal("0.00"));
        orderItem1.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem1);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<BigDecimal> result = orderAfterSaleQueryProviderImplUnderTest.calculateRefundPrice(req);

        // Verify the results
    }

    @Test
    void testCalculateRefundPrice_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        final OrderAfterSaleCalRefundPriceReq req = new OrderAfterSaleCalRefundPriceReq();
        req.setOrderItemId(0L);
        req.setQuantity(0);

        // Configure OrderItemDao.getById(...).
        final OrderItem orderItem = new OrderItem();
        orderItem.setId(0L);
        orderItem.setTenantId(0L);
        orderItem.setOrderId(0L);
        orderItem.setItemId(0L);
        orderItem.setAmount(0);
        orderItem.setPayablePrice(new BigDecimal("0.00"));
        orderItem.setTotalPrice(new BigDecimal("0.00"));
        when(mockOrderItemDao.getById(0L)).thenReturn(orderItem);

        // Configure OrderItemSnapshotDao.queryByOrderItemId(...).
        final OrderItemSnapshot orderItemSnapshot = new OrderItemSnapshot();
        orderItemSnapshot.setOrderItemId(0L);
        orderItemSnapshot.setSkuId(0L);
        orderItemSnapshot.setSupplierTenantId(0L);
        orderItemSnapshot.setSupplierSkuId(0L);
        orderItemSnapshot.setAreaItemId(0L);
        orderItemSnapshot.setTitle("title");
        orderItemSnapshot.setMainPicture("mainPicture");
        orderItemSnapshot.setSpecificationUnit("specificationUnit");
        orderItemSnapshot.setSpecification("specification");
        orderItemSnapshot.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot.setMaxAfterSaleAmount(0);
        orderItemSnapshot.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot.setGoodsType(0);
        when(mockOrderItemSnapshotDao.queryByOrderItemId(0L)).thenReturn(orderItemSnapshot);

        // Configure OrderDao.getById(...).
        final Order order = new Order();
        order.setId(0L);
        order.setOrderNo("orderNo");
        order.setWarehouseType(0);
        order.setStatus(0);
        order.setCombineOrderId(0L);
        order.setOrderType(0);
        when(mockOrderDao.getById(0L)).thenReturn(order);

        // Configure OrderItemDao.queryByOrderId(...).
        final OrderItem orderItem1 = new OrderItem();
        orderItem1.setId(0L);
        orderItem1.setTenantId(0L);
        orderItem1.setOrderId(0L);
        orderItem1.setItemId(0L);
        orderItem1.setAmount(0);
        orderItem1.setPayablePrice(new BigDecimal("0.00"));
        orderItem1.setTotalPrice(new BigDecimal("0.00"));
        final List<OrderItem> orderItems = Arrays.asList(orderItem1);
        when(mockOrderItemDao.queryByOrderId(0L)).thenReturn(orderItems);

        // Configure OrderItemSnapshotDao.queryByOrderItemIds(...).
        final OrderItemSnapshot orderItemSnapshot1 = new OrderItemSnapshot();
        orderItemSnapshot1.setOrderItemId(0L);
        orderItemSnapshot1.setSkuId(0L);
        orderItemSnapshot1.setSupplierTenantId(0L);
        orderItemSnapshot1.setSupplierSkuId(0L);
        orderItemSnapshot1.setAreaItemId(0L);
        orderItemSnapshot1.setTitle("title");
        orderItemSnapshot1.setMainPicture("mainPicture");
        orderItemSnapshot1.setSpecificationUnit("specificationUnit");
        orderItemSnapshot1.setSpecification("specification");
        orderItemSnapshot1.setSupplyPrice(new BigDecimal("0.00"));
        orderItemSnapshot1.setMaxAfterSaleAmount(0);
        orderItemSnapshot1.setAfterSaleUnit("afterSaleUnit");
        orderItemSnapshot1.setGoodsType(0);
        final List<OrderItemSnapshot> orderItemSnapshots = Arrays.asList(orderItemSnapshot1);
        when(mockOrderItemSnapshotDao.queryByOrderItemIds(Arrays.asList(0L))).thenReturn(orderItemSnapshots);

        // Configure OrderAfterSaleDao.queryList(...).
        final OrderAfterSaleQueryReq req1 = new OrderAfterSaleQueryReq();
        req1.setStatusList(Arrays.asList(0));
        req1.setServiceType(Arrays.asList(0));
        req1.setTenantId(0L);
        req1.setSupplierIds(Arrays.asList(0L));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.queryList(req1)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<BigDecimal> result = orderAfterSaleQueryProviderImplUnderTest.calculateRefundPrice(req);

        // Verify the results
    }

    @Test
    void testCountOrderAfterSaleByOrderId() {
        // Setup
        final OrderAfterSaleCountReq req = new OrderAfterSaleCountReq();
        req.setTenantId(0L);
        req.setStoreId(0L);
        req.setStatusList(Arrays.asList(0));
        req.setOrderIds(Arrays.asList(0L));

        // Configure OrderAfterSaleDao.countOrderAfterSaleByOrderId(...).
        final OrderAfterSaleCountReq req1 = new OrderAfterSaleCountReq();
        req1.setTenantId(0L);
        req1.setStoreId(0L);
        req1.setStatusList(Arrays.asList(0));
        req1.setOrderIds(Arrays.asList(0L));
        when(mockOrderAfterSaleDao.countOrderAfterSaleByOrderId(req1)).thenReturn(new HashMap<>());

        // Run the test
        final DubboResponse<Map<Long, Integer>> result = orderAfterSaleQueryProviderImplUnderTest.countOrderAfterSaleByOrderId(
                req);

        // Verify the results
    }

    @Test
    void testGetRecentlyUsedReturnAddressId() {
        // Setup
        when(mockOrderAfterSaleDao.getRecentlyUsedReturnAddressId(0L)).thenReturn(0L);

        // Run the test
        final DubboResponse<Long> result = orderAfterSaleQueryProviderImplUnderTest.getRecentlyUsedReturnAddressId(0L);

        // Verify the results
    }

    @Test
    void testQueryOrderAfterSaleForBill() {
        // Setup
        final QueryBillOrderAfterSaleReq req = new QueryBillOrderAfterSaleReq();
        req.setTenantId(0L);
        req.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        req.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OrderAfterSaleDao.queryOrderAfterSaleForBill(...).
        final OrderAfterSale orderAfterSale = new OrderAfterSale();
        orderAfterSale.setId(0L);
        orderAfterSale.setOrderItemId(0L);
        orderAfterSale.setAfterSaleOrderNo("afterSaleOrderNo");
        orderAfterSale.setAmount(0);
        orderAfterSale.setAfterSaleType(0);
        orderAfterSale.setServiceType(0);
        orderAfterSale.setTotalPrice(new BigDecimal("0.00"));
        orderAfterSale.setDeliveryFee(new BigDecimal("0.00"));
        orderAfterSale.setStatus(0);
        orderAfterSale.setWarehouseType(0);
        final List<OrderAfterSale> orderAfterSales = Arrays.asList(orderAfterSale);
        final QueryBillOrderAfterSaleReq req1 = new QueryBillOrderAfterSaleReq();
        req1.setTenantId(0L);
        req1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        req1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderAfterSaleDao.queryOrderAfterSaleForBill(req1)).thenReturn(orderAfterSales);

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryOrderAfterSaleForBill(
                req);

        // Verify the results
    }

    @Test
    void testQueryOrderAfterSaleForBill_OrderAfterSaleDaoReturnsNoItems() {
        // Setup
        final QueryBillOrderAfterSaleReq req = new QueryBillOrderAfterSaleReq();
        req.setTenantId(0L);
        req.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        req.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure OrderAfterSaleDao.queryOrderAfterSaleForBill(...).
        final QueryBillOrderAfterSaleReq req1 = new QueryBillOrderAfterSaleReq();
        req1.setTenantId(0L);
        req1.setStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        req1.setEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderAfterSaleDao.queryOrderAfterSaleForBill(req1)).thenReturn(Collections.emptyList());

        // Run the test
        final DubboResponse<List<OrderAfterSaleDTO>> result = orderAfterSaleQueryProviderImplUnderTest.queryOrderAfterSaleForBill(
                req);

        // Verify the results
    }
}
