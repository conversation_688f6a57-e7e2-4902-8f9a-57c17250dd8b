package com.cosfo.ordercenter;

import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.FullyQualifiedAnnotationBeanNameGenerator;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.cosfo.ordercenter.**"}, nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class)
@MapperScans(value = {
        @MapperScan(basePackages = "com.cosfo.ordercenter.infrastructure.mapper", nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class),
        @MapperScan(basePackages = "com.cosfo.ordercenter.dao.mapper", nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class)
})
@DubboComponentScan(basePackages = {"com.cosfo.ordercenter.service.provider.**", "com.cosfo.ordercenter.application.inbound.provider.**"})
@EnableAsync
public class OrderCenterApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderCenterApplication.class, args);
    }
}
