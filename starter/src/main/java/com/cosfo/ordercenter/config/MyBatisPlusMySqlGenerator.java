package com.cosfo.ordercenter.config;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public class MyBatisPlusMySqlGenerator {

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        FastAutoGenerator.create("************************************************", "test", "xianmu619")
                .globalConfig(builder -> {
                    builder.author("zhoujiachen") // 设置作者
                            .fileOverride() // 覆盖已生成文件
                            .outputDir(projectPath + "/dao" + "/src/main/java"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.cosfo.ordercenter") // 设置父包名
                            .moduleName("dao") // 设置父包模块名
                            .entity("model.po")
                            .service("dao")
                            .serviceImpl("dao.impl")
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, projectPath + "/dao" + "/src/main/resources/mapper" + "/order")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude("order_item_extra") // 设置需要生成的表名
                            .entityBuilder()
                            .enableLombok()
                            .serviceBuilder()
                            .formatServiceFileName("%sDao")
                            .formatServiceImplFileName("%sDaoImpl")
                    ; // 设置过滤表前缀
                }).templateConfig(t -> t.controller(null))
//                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
