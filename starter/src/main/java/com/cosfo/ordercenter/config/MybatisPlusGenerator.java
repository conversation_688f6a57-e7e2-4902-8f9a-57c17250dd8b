package com.cosfo.ordercenter.config;

import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import org.apache.ibatis.jdbc.ScriptRunner;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class MybatisPlusGenerator {

    /**
     * 数据源配置
     */
    private static final DataSourceConfig.Builder DATA_SOURCE_CONFIG = new DataSourceConfig
            .Builder("jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=FALSE;MODE=MYSQL", "sa", "");
    /**
     * 执行数据库脚本
     */
    protected static void initDataSource(DataSourceConfig dataSourceConfig) throws SQLException {
        Connection conn = dataSourceConfig.getConn();
        InputStream inputStream = MybatisPlusGenerator.class.getResourceAsStream("/sql/init.sql");
        ScriptRunner scriptRunner = new ScriptRunner(conn);
        scriptRunner.setAutoCommit(true);
        scriptRunner.runScript(new InputStreamReader(inputStream));
        conn.close();
    }



    public static void main(String[] args) throws SQLException {
//        initDataSource(DATA_SOURCE_CONFIG.build());
//
//        String projectPath = System.getProperty("user.dir");
//        FastAutoGenerator.create(DATA_SOURCE_CONFIG)
//                .globalConfig(builder -> {
//                    builder.author("zhoujiachen") // 设置作者
//                            .outputDir(projectPath + "/dao" + "/src/main/java"); // 指定输出目录
//                })
//                .packageConfig(builder -> {
//                    builder.parent("com.cosfo.ordercenter") // 设置父包名
//                            .moduleName("dao") // 设置父包模块名
//                            .entity("model.po")
//                            .service("dao")
//                            .serviceImpl("dao.impl")
//                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/dao" + "/src/main/resources/mapper" + "/order")); // 设置mapperXml生成路径
//                })
//                .strategyConfig(builder -> {
//                    builder.addInclude("order_after_sale")
//                            .entityBuilder()
//                            .enableFileOverride()
//                            .enableLombok()
//                            .serviceBuilder()
//                            .formatServiceFileName("%sDao")
//                            .formatServiceImplFileName("%sDaoImpl")
//                            .mapperBuilder()
//                            .enableMapperAnnotation()
//                    ; // 设置过滤表前缀
//                }).templateConfig(t -> t.controller(null))
//                .execute();
    }
}
