package com.cosfo.ordercenter.config;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderItemDTO;
import com.cosfo.ordercenter.client.resp.OrderItemSnapshotDTO;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.cosfo.ordercenter.client.service.OrderItemSnapshotQueryService;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.dao.model.po.Order;
import com.cosfo.ordercenter.service.config.SelfCheckConfig;
import com.cosfo.ordercenter.service.util.RpcResponseUtil;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 自检服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SelfCheckApplication {
    //implements ApplicationListener<ApplicationReadyEvent>
    @Resource
    private SelfCheckConfig selfCheckConfig;
    @Resource
    private OrderDao orderDao;
    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private OrderItemQueryService orderItemQueryService;
    @Resource
    private OrderItemSnapshotQueryService orderItemSnapshotQueryService;

    //    @Override
//    public void onApplicationEvent(ApplicationReadyEvent applicationReadyEvent) {
//
//    }
    public boolean check() {
        log.info("order-center自检服务启动");
        Stopwatch stopwatch = Stopwatch.createStarted();
        if (!selfCheckConfig.getCheckSwitch()) {
            log.info("order-center自检配置关闭，跳过自检");
            return true;
        }
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Order::getId);
        queryWrapper.last("limit 1");
        Order order = orderDao.getOne(queryWrapper);
        log.info("query-order:{}", stopwatch);
        if (order == null) {
            log.warn("order-center自检异常，无法获取订单数据");
            return false;
        }
        log.info("order-center自检订单,目标订单:{}", JSON.toJSONString(order));
        OrderDTO queryOrder = RpcResponseUtil.handler(orderQueryService.queryById(order.getId()));
        log.info("query-order-service:{}", stopwatch);

        log.info("order-center自检订单:{}", JSON.toJSONString(queryOrder));

        List<OrderItemDTO> orderItemList = RpcResponseUtil.handler(orderItemQueryService.queryOrderItemList(order.getId()));
        log.info("order-center自检订单明细:{}", JSON.toJSONString(orderItemList));
        log.info("query-order-item:{}", stopwatch);

        if (CollectionUtils.isEmpty(orderItemList)) {
            log.warn("order-center自检异常，无法获取订单明细数据");
            return false;
        }
        OrderItemDTO orderItemDTOCase = orderItemList.get(0);
        OrderItemSnapshotDTO snapshot = RpcResponseUtil.handler(orderItemSnapshotQueryService.queryByOrderItemId(orderItemDTOCase.getId()));
        log.info("order-center自检订单明细快照:{}", JSON.toJSONString(snapshot));

        log.info("order-center自检完成,cast={}", stopwatch.stop());
        return true;
    }
}
