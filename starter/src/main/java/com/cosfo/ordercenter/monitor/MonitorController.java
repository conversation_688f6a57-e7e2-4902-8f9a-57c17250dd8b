package com.cosfo.ordercenter.monitor;

import com.cosfo.ordercenter.config.SelfCheckApplication;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class MonitorController {

    @Resource
    private SelfCheckApplication selfCheckApplication;

    private static volatile boolean checkResult = false;

    private static volatile boolean isRan = false;


    /**
     * 心跳检测
     *
     * @return success
     */
    @GetMapping(value = "/ok")
    public String heartbeat(HttpServletResponse response) {
        if (!isRan) {
            try {
                isRan = true;
                checkResult = selfCheckApplication.check();
                if (!checkResult) {
                    throw new BizException("order-center自检失败");
                }
            } catch (Exception ex) {
                log.error("order-center自检失败", ex);
            }
        }
        if (!checkResult) {
            response.setStatus(500);
            return "error";
        }
        return "success";
    }
}