logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.manage: INFO
  pattern:
    console: "%d - %msg%n"
server:
  port: 80
  servlet:
    context-path: /order-center
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}

# 数据库配置
spring:
  application:
    name: order-center
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    username: dev
    password: xianmu619
    #?serverTimezone=UTC解决时区的报错
    url: ********************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: order-center
    appKey: pdRQrWBmO7GjvxcWh83bqA==
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 0

rocketmq:
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_saas-manage
    send-message-timeout: 10000
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin
  consumer:
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    # address: nacos://127.0.0.1:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
xm:
  log:
    enable: true
    resp: true
request:
  source:
    oms: cosfo-oms
# 自检配置
selfCheck:
  switch: true
  tenantId: 2
saasmall:
  api-host: http://cosfo-mall-svc