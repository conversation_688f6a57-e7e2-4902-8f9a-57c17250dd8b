/******************************************/
/*   DatabaseName = cosfodb   */
/*   TableName = order_after_sale   */
/******************************************/
CREATE TABLE `order_after_sale` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
                                    `tenant_id` bigint DEFAULT NULL COMMENT '租户编号',
                                    `order_id` bigint DEFAULT NULL COMMENT '订单编号',
                                    `order_item_id` bigint DEFAULT NULL COMMENT '订单项Id',
                                    `store_id` bigint DEFAULT NULL COMMENT '门店Id',
                                    `account_id` bigint DEFAULT NULL COMMENT '账户ID',
                                    `after_sale_order_no` varchar(50) DEFAULT NULL COMMENT '售后订单编号',
                                    `amount` int DEFAULT NULL COMMENT '数量',
                                    `after_sale_type` bigint DEFAULT NULL COMMENT '售后类型 0 已到货 1 未到货',
                                    `service_type` bigint DEFAULT NULL COMMENT '售后服务类型 1 退款 2 退款录入账单 3 退货退款 4 退货退款录入账单 5 换货 6 补发 7、退款录入余额 8、 退货退款录入余额',
                                    `apply_price` decimal(12, 2) DEFAULT NULL COMMENT '申请金额',
                                    `total_price` decimal(12, 2) DEFAULT NULL COMMENT '售后金额',
                                    `delivery_fee` decimal(10, 2) DEFAULT NULL COMMENT '配送费',
                                    `reason` varchar(2048) DEFAULT NULL COMMENT '售后原因',
                                    `user_remark` varchar(2048) DEFAULT NULL COMMENT '客户留言',
                                    `proof_picture` varchar(2048) DEFAULT NULL COMMENT '售后凭证照片',
                                    `status` tinyint DEFAULT NULL COMMENT '状态 1,待审核 2，已成功 3，已失败 4 ，已取消
系统内部状态 1待审核 2处理中 3退款中 4已同意 5已拒绝 6已取消 7库存退还失败 8 待退款9 三方处理中',
                                    `handle_remark` varchar(2048) DEFAULT NULL COMMENT '处理结果',
                                    `operator_name` varchar(255) DEFAULT NULL COMMENT '操作人',
                                    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `finished_time` datetime DEFAULT NULL COMMENT '完成时间',
                                    `handle_time` datetime DEFAULT NULL COMMENT '审核时间',
                                    `recycle_time` datetime DEFAULT NULL COMMENT '处理时间（回收、补发时间）',
                                    `recycle_details` varchar(2048) DEFAULT NULL COMMENT '回收详情',
                                    `responsibility_type` tinyint DEFAULT '2' COMMENT '责任类型0供应商1品牌方2门店',
                                    `store_no` int DEFAULT NULL COMMENT '城配仓 编码',
                                    `warehouse_type` tinyint DEFAULT NULL COMMENT '配送仓类型 0,无仓1三方仓 2自营仓',
                                    `auto_finished_time` datetime DEFAULT NULL COMMENT '自动完成时间',
                                    `apply_quantity` int DEFAULT NULL COMMENT '申请数量',
                                    `admin_remark` varchar(2048) DEFAULT NULL COMMENT '后台售后备注',
                                    `admin_remark_time` datetime DEFAULT NULL COMMENT '后台售后备注更新时间',
                                    `second_handle_remark` varchar(2048) DEFAULT NULL COMMENT '退货二次审核说明',
                                    `return_address_id` bigint DEFAULT NULL COMMENT '退回地址Id',
                                    PRIMARY KEY (`id`),
                                    INDEX `idx_afterorderid` (`after_sale_order_no`),
                                    INDEX `idx_status` (`tenant_id`,`status`)
) ENGINE = InnoDB;

CREATE TABLE `order` (
                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键Id',
                         `tenant_id` bigint NOT NULL COMMENT '租户Id',
                         `store_id` bigint NOT NULL COMMENT '店铺Id',
                         `account_id` bigint NOT NULL COMMENT '下单账号Id',
                         `supplier_tenant_id` bigint DEFAULT NULL COMMENT '供应商租户Id',
                         `order_no` varchar(50) NOT NULL COMMENT '订单编号',
                         `warehouse_type` tinyint NOT NULL COMMENT '配送仓类型 0,无仓1三方仓 2自营仓',
                         `payable_price` decimal(12, 2) DEFAULT NULL COMMENT '应付价格',
                         `delivery_fee` decimal(12, 2) DEFAULT NULL COMMENT '配送费',
                         `total_price` decimal(12, 2) DEFAULT NULL COMMENT '总金额',
                         `status` tinyint NOT NULL COMMENT '状态',
                         `pay_type` tinyint DEFAULT NULL COMMENT '支付方式 1,线上支付 2,账期 3、余额支付',
                         `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
                         `delivery_time` datetime DEFAULT NULL COMMENT '配送时间',
                         `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                         `finished_time` datetime DEFAULT NULL COMMENT '完成时间',
                         `online_pay_channel` int DEFAULT NULL COMMENT '支付渠道 0、微信 1、汇付',
                         `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                         `apply_end_time` int DEFAULT NULL COMMENT '可申请售后时间(小时)',
                         `auto_finished_time` int DEFAULT NULL COMMENT '自动完成时间(天)',
                         `warehouse_no` varchar(50) DEFAULT NULL COMMENT '仓库编号',
                         `begin_delivery_time` datetime DEFAULT NULL COMMENT '开始配送时间',
                         `combine_order_id` bigint NOT NULL DEFAULT '-1' COMMENT '组合订单id',
                         `order_type` tinyint NOT NULL DEFAULT '0' COMMENT '0-普通订单,1=组合订单, 2=预售订单',
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `index_unique_orderNO` (`order_no`),
                         INDEX `index_tenantId_storeId_accountId` (`tenant_id`,`store_id`,`account_id`),
                         INDEX `index_createtime_tenantid_status` (`create_time`,`tenant_id`,`status`),
                         INDEX `idx_combine_order_id` (`combine_order_id`)
) ENGINE = InnoDB;