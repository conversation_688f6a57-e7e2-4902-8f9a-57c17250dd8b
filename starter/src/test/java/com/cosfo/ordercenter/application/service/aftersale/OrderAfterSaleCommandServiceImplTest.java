package com.cosfo.ordercenter.application.service.aftersale;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.req.OrderAfterSaleAuditReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleModifyQuantityReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleRecycleFailRefundReq;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAfterSaleCommandServiceImplTest {

    @Resource
    private OrderAfterSaleCommandService orderAfterSaleCommandService;


    @Test
    void modifyQuantityTest() {
        OrderAfterSaleModifyQuantityReq modifyQuantityReq = new OrderAfterSaleModifyQuantityReq();
        modifyQuantityReq.setAfterSaleId(19102L);
        modifyQuantityReq.setAmount(40);
        modifyQuantityReq.setApplyPrice(new BigDecimal("40.00"));
        modifyQuantityReq.setTotalPrice(new BigDecimal("40.00"));
        modifyQuantityReq.setOperatorName("test");
        Boolean b = orderAfterSaleCommandService.modifyQuantity(modifyQuantityReq);
        System.out.println(b);
    }

    @Test
    void recycleFailRefundTest() {
        OrderAfterSaleRecycleFailRefundReq recycleFailRefundReq = new OrderAfterSaleRecycleFailRefundReq();
        recycleFailRefundReq.setAfterSaleId(19155L);
        recycleFailRefundReq.setTotalPrice(new BigDecimal("4.00"));
        Boolean b = orderAfterSaleCommandService.recycleFailRefund(recycleFailRefundReq);
        System.out.println(b);
    }

    @Test
    void reviewSubmissions() {
        OrderAfterSaleAuditReq req = JSON.parseObject("{\"afterSaleOrderNo\":\"AS1830177219712208897\",\"amount\":1,\"auditStatus\":1,\"handleRemark\":\"退运费，11\",\"needServiceProviderAudit\":false,\"operatorName\":\"朱永林\",\"responsibilityType\":\"0\",\"returnWarehouseNo\":\"\",\"supplierTotalRefundPrice\":40,\"systemSource\":1,\"totalPrice\":99}", OrderAfterSaleAuditReq.class);
        Boolean b = orderAfterSaleCommandService.reviewSubmissions(req);
        System.out.println(b);
    }
}