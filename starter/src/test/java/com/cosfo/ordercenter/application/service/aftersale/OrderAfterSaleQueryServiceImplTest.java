package com.cosfo.ordercenter.application.service.aftersale;

import com.cosfo.ordercenter.client.req.OrderAfterSaleCalRefundPriceReq;
import com.cosfo.ordercenter.client.req.OrderAfterSaleOutQueryDTO;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.req.QueryResentOrderAfterSaleReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleOutResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.cosfo.ordercenter.client.resp.aftersale.QueryResentOrderAfterSaleResp;
import com.github.pagehelper.PageInfo;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderAfterSaleQueryServiceImplTest {

    @Resource
    private OrderAfterSaleQueryService orderAfterSaleQueryService;

    @Test
    void queryPage() {
        OrderAfterSalePageQueryReq param = new OrderAfterSalePageQueryReq();
        param.setTenantId(2L);
        param.setPageNum(1);
        param.setPageSize(10);
        PageInfo<OrderAfterSaleWithOrderResp> orderAfterSaleWithOrderRespPageInfo = orderAfterSaleQueryService.queryPage(param);
        System.out.println(orderAfterSaleWithOrderRespPageInfo);
    }

    @Test
    void queryOrderAfterSaleInfo() {
        OrderAfterSaleOutQueryDTO queryDTO = new OrderAfterSaleOutQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setAfterSaleOrderNos(Lists.newArrayList("AS1765979061172645888", "AS1765945690212085760"));
        List<OrderAfterSaleOutResp> orderAfterSaleOutResps = orderAfterSaleQueryService.queryOrderAfterSaleInfo(queryDTO);
        System.out.println(orderAfterSaleOutResps);
        Assertions.assertNotNull(orderAfterSaleOutResps);
    }

    @Test
    void queryByNos() {
        List<String> afterSaleNos = Lists.newArrayList("AS1765945690212085760");
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryService.queryByNos(afterSaleNos);
        System.out.println(orderAfterSaleResps);
        Assertions.assertNotNull(orderAfterSaleResps);
    }

    @Test
    void queryByIds() {
        List<Long> afterSaleIds = Lists.newArrayList(19484L);
        List<OrderAfterSaleResp> orderAfterSaleResps = orderAfterSaleQueryService.queryByIds(afterSaleIds);
        System.out.println(orderAfterSaleResps);
        Assertions.assertNotNull(orderAfterSaleResps);
    }

    @Test
    void calculateRefundPrice() {
        OrderAfterSaleCalRefundPriceReq calRefundPriceReq = new OrderAfterSaleCalRefundPriceReq();
        calRefundPriceReq.setQuantity(1);
        calRefundPriceReq.setOrderItemId(278965L);
        BigDecimal bigDecimal = orderAfterSaleQueryService.calculateRefundPrice(calRefundPriceReq);
        System.out.println(bigDecimal);
        Assertions.assertNotNull(bigDecimal);
    }

    @Test
    void queryResentOrderAfterSaleForTms() {
        QueryResentOrderAfterSaleReq afterSaleReq = new QueryResentOrderAfterSaleReq();
        afterSaleReq.setCreateDate(LocalDate.now().minusDays(1));
        afterSaleReq.setSku("21153");
        afterSaleReq.setDeliveryDate(LocalDate.now());
        afterSaleReq.setStoreId(165L);
        List<QueryResentOrderAfterSaleResp> queryResentOrderAfterSaleResps = orderAfterSaleQueryService.queryResentOrderAfterSaleForTms(afterSaleReq);
        System.out.println(queryResentOrderAfterSaleResps);
        Assertions.assertNotNull(queryResentOrderAfterSaleResps);
    }
}