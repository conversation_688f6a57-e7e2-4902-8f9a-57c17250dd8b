package com.cosfo.ordercenter.application.service.delivery;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.MerchantDeliveryProvider;
import com.cosfo.ordercenter.client.req.DeliveryTotalReq;
import com.cosfo.ordercenter.client.resp.delivery.DeliveryTotalDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotResp;
import com.cosfo.ordercenter.client.service.MerchantDeliveryService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @author: xiaowk
 * @time: 2024/12/18 下午4:55
 */
@SpringBootTest
public class MerchantDeliveryProviderTest {

    @DubboReference
    private MerchantDeliveryProvider merchantDeliveryProvider;

    @DubboReference
    private MerchantDeliveryService merchantDeliveryService;


    @Test
    void queryMerchantDeliveryFee() {
        String param = "{\"deliveryTime\":\"2025-03-07\",\"orderInfoDTO\":{\"fulfillmentType\":0,\"mulOrderTotalPrice\":50,\"orderTotalCount\":4,\"orderTotalPrice\":48.96,\"storeArea\":\"西湖区\",\"storeCity\":\"杭州市\",\"storePoi\":\"113.751861,23.020853\",\"storeProvince\":\"浙江省\",\"warehouseType\":1},\"orderItemInfoDTOList\":[{\"goodsType\":1,\"itemCount\":4,\"itemId\":50658,\"itemTotalPrice\":48.96,\"skuId\":9496,\"supplierSkuId\":9496,\"totalWeight\":4.0,\"weight\":1.0}],\"storeId\":4260,\"supplierTenantId\":1,\"tenantId\":2}";
        DeliveryTotalDTO deliveryTotalDTO = JSON.parseObject(param, DeliveryTotalDTO.class);
        DubboResponse<MerchantDeliveryFeeSnapshotDTO> oldResp = merchantDeliveryService.queryMerchantDeliveryFee(deliveryTotalDTO);

        DeliveryTotalReq deliveryTotalReq = JSON.parseObject(param, DeliveryTotalReq.class);
        DubboResponse<MerchantDeliveryFeeSnapshotResp> newResp = merchantDeliveryProvider.queryMerchantDeliveryFee(deliveryTotalReq);

        System.err.println("oldResp=" + JSON.toJSONString(oldResp));
        System.err.println("newResp=" + JSON.toJSONString(newResp));

        System.err.println("比较返回值：" + JSON.toJSONString(oldResp).equals(JSON.toJSONString(newResp)));

    }
}
