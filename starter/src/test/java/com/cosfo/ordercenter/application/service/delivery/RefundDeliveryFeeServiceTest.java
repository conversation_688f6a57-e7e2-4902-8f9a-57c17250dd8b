package com.cosfo.ordercenter.application.service.delivery;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.OrderConverter;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.dao.dao.OrderDao;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.infrastructure.model.aftersale.OrderAfterSale;
import com.cosfo.ordercenter.infrastructure.model.order.Order;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @author: xiaowk
 * @time: 2024/12/19 下午2:30
 */
@SpringBootTest
public class RefundDeliveryFeeServiceTest {

    @Resource
    private RefundDeliveryFeeService newrefundDeliveryFeeService;
    @Resource
    private com.cosfo.ordercenter.service.biz.delivery.RefundDeliveryFeeService  oldrefundDeliveryFeeService;

    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderDao orderDao;

    @Test
    public void queryRefundDeliveryFee(){

        Long orderId = 122030L;
        String aftersaleStr = "[{\"accountId\":3530,\"afterSaleOrderNo\":\"*********************\",\"afterSaleType\":1,\"amount\":1,\"applyPrice\":20.00,\"autoFinishedTime\":\"2024-12-30T17:35:58.292\",\"handleRemark\":\"系统自动通过\",\"handleTime\":\"2024-12-20T17:35:58.396\",\"operatorName\":\"系统\",\"orderId\":122030,\"orderItemId\":299666,\"reason\":\"拍多/拍错/不想要\",\"serviceType\":2,\"status\":2,\"storeId\":4260,\"tenantId\":2,\"totalPrice\":20.00,\"warehouseType\":1}]";
        OrderEntity order1 = orderQueryRepository.queryById(orderId);
        Order orderDTO = OrderConverter.convertToPO(order1);;
        MerchantDeliveryFeeSnapshotDTO newResult = newrefundDeliveryFeeService.queryRefundDeliveryFee(JSON.parseArray(aftersaleStr, OrderAfterSale.class), orderDTO);

        com.cosfo.ordercenter.dao.model.po.Order order = orderDao.getById(orderId);
        MerchantDeliveryFeeSnapshotDTO oldResult = oldrefundDeliveryFeeService.queryRefundDeliveryFee(JSON.parseArray(aftersaleStr, com.cosfo.ordercenter.dao.model.po.OrderAfterSale.class), order);

        System.err.println("oldResult=" + JSON.toJSONString(oldResult));
        System.err.println("newResult=" + JSON.toJSONString(newResult));
        System.err.println("比较返回值：" + JSON.toJSONString(oldResult).equals(JSON.toJSONString(newResult)));


    }
}
