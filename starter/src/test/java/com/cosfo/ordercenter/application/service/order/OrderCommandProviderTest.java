package com.cosfo.ordercenter.application.service.order;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.req.OrderPresaleSetDeliveryDateReq;
import com.cosfo.ordercenter.client.req.event.OrderCloseReq;
import com.cosfo.ordercenter.client.req.event.OrderCreateReq;
import com.cosfo.ordercenter.client.req.event.OrderPaySuccessReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * @author: xiaowk
 * @time: 2024/4/29 下午6:13
 */
@SpringBootTest
public class OrderCommandProviderTest {

    @Resource
    private OrderCommandProvider orderCommandProvider;


    @Test
    void create() {
        OrderCreateReq req = JSON.parseObject("{\"deliveryFeeSnapshotDTO\":{\"deliveryFee\":0.01,\"hitRuleList\":[{\"deliveryFee\":0.01,\"itemId\":2136,\"ruleId\":232}],\"orderInfo\":{\"orderInfoDTO\":{\"orderTotalCount\":1,\"orderTotalPrice\":270.00,\"storeArea\":\"上城区\",\"storeCity\":\"杭州市\",\"storePoi\":\"120.197334,30.226544\",\"storeProvince\":\"浙江\",\"warehouseNo\":197,\"warehouseType\":2,\"warehouseTypeEnum\":\"SELF_SUPPLY\"},\"orderItemInfoDTOList\":[{\"itemCount\":1,\"itemId\":2136,\"itemTotalPrice\":270.00,\"skuId\":18722,\"supplierSkuId\":18382}],\"storeId\":4260,\"supplierTenantId\":2,\"tenantId\":2},\"remark\":\"通过每单阶梯价生成\",\"ruleList\":[{\"defaultType\":1,\"deliveryType\":1,\"hitAreaList\":[],\"hitItemIds\":[],\"priceType\":2,\"priority\":0,\"relateNumber\":0.00,\"ruleId\":232,\"ruleType\":0,\"stepFeeDescList\":[{\"deliveryFee\":0.01,\"stepThreshold\":1.00}],\"tenantId\":2,\"warehouseType\":2}],\"tenantId\":2},\"orderAddressDTO\":{\"address\":\"杭州市上城区人民政府梅林路9\",\"area\":\"上城区\",\"city\":\"杭州市\",\"contactName\":\"店铺测试kk\",\"contactPhone\":\"***********\",\"id\":4256,\"poiNote\":\"120.197334,30.226544\",\"province\":\"浙江省\",\"tenantId\":2},\"orderDTO\":{\"accountId\":3530,\"applyEndTime\":120,\"autoFinishedTime\":10,\"combineOrderId\":1663454814895161843,\"deliveryFee\":0.01,\"orderNo\":\"*****************\",\"orderType\":1,\"orderVersion\":1,\"payType\":2,\"payablePrice\":270.01,\"planOrderNo\":\"\",\"remark\":\"\",\"status\":1,\"storeId\":4260,\"supplierTenantId\":2,\"tenantId\":2,\"warehouseNo\":\"197\",\"warehouseType\":2},\"orderItemList\":[{\"afterSaleUnit\":\"瓶\",\"amount\":1,\"buyMultiple\":1,\"buyMultipleSwitch\":false,\"combineItemId\":2269,\"enableApplyAfterSale\":false,\"goodsType\":2,\"itemCode\":\"\",\"itemId\":2136,\"itemSaleMode\":0,\"itemType\":2,\"mainPicture\":\"test/05anapg8o9o1481rp.jpeg\",\"maxAfterSaleAmount\":1,\"orderAfterSaleRule\":{\"applyEndTime\":120,\"autoFinishedTime\":10},\"orderCombineItemCreateReq\":{\"originalPrice\":300.00,\"quantity\":1},\"price\":270.00,\"skuCode\":\"*************\",\"skuId\":18722,\"specification\":\"0_12箱*12盒\",\"specificationUnit\":\"瓶\",\"supplierSkuId\":18382,\"supplierTenantId\":1,\"supplyPrice\":300.00,\"supplySku\":\"*************\",\"tenantId\":2,\"title\":\"组合包自营有仓商品\",\"totalPrice\":270.00}]}", OrderCreateReq.class);
        DubboResponse<Long> response = orderCommandProvider.create(req);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void close() {
        OrderCloseReq req = new OrderCloseReq();
        req.setOrderId(116143L);
        req.setTenantId(2L);
        DubboResponse<Boolean> response = orderCommandProvider.close(req);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void paySuccess() {
        OrderPaySuccessReq req = new OrderPaySuccessReq();
        req.setOrderId(116078L);
        DubboResponse<Boolean> response = orderCommandProvider.paySuccess(req);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    void setDeliveryDatePresaleOrder() {
        OrderPresaleSetDeliveryDateReq req = new OrderPresaleSetDeliveryDateReq();
        req.setOrderNo("OR172103327248876");
        req.setDeliveryDate(LocalDate.now().plusDays(1));

        DubboResponse<Boolean> response = orderCommandProvider.setDeliveryDatePresaleOrder(req);
        System.out.println(JSON.toJSONString(response));
    }

}
