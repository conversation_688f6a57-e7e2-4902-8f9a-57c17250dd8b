package com.cosfo.ordercenter.application.service.order;

import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.client.provider.OrderItemQueryProvider;
import com.cosfo.ordercenter.client.req.OrderItemQueryReq;
import com.cosfo.ordercenter.client.resp.OrderItemAndSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderItemAndSnapshotResp;
import com.cosfo.ordercenter.client.service.OrderItemQueryService;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2024/5/15 下午2:28
 */
@SpringBootTest
public class OrderItemQueryProviderTest {

    @Resource
    private OrderItemQueryProvider orderItemQueryProvider;

    @Resource
    private OrderItemQueryService orderItemQueryService;

    @Test
    public void queryOrderItemListTest() {
        OrderItemQueryReq req = new OrderItemQueryReq();
        req.setOrderIds(Lists.newArrayList(114406L));
        req.setTenantId(2L);

        DubboResponse<List<OrderItemAndSnapshotResp>> response = orderItemQueryProvider.queryOrderItemList(req);
        System.err.println(JSON.toJSONString(response));
    }


    @Test
    void queryList() {
        OrderItemQueryReq orderItemQueryReq = new OrderItemQueryReq();
        orderItemQueryReq.setOrderIds(org.assertj.core.util.Lists.newArrayList(114406L));
        DubboResponse<List<OrderItemAndSnapshotDTO>> response = orderItemQueryService.queryOrderItemList(orderItemQueryReq);
        System.out.println(JSON.toJSONString(response));
    }
}
