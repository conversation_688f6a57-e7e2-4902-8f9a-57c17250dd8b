package com.cosfo.ordercenter.application.service.order;

import com.alibaba.fastjson.JSON;
import com.cosfo.manage.client.sms.SmsSenderProvider;
import com.cosfo.manage.client.sms.req.SmsReq;
import com.cosfo.ordercenter.client.provider.OrderCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderOmsQueryReq;
import com.cosfo.ordercenter.client.req.OrderQueryReq;
import com.cosfo.ordercenter.client.req.OrderUpdateDelivertDateReq;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.service.OrderMutateService;
import com.cosfo.ordercenter.client.service.OrderQueryService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
public class OrderQueryProviderTest {

    @Resource
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private OrderMutateService orderMutateService;
    @Resource
    private OrderCommandProvider orderCommandProvider;

    @DubboReference
    private SmsSenderProvider smsSenderProvider;

    @Test
    void queryOrderInfoTest() {
        DubboResponse<OrderResp> orderRespDubboResponse = orderQueryProvider.queryById(108143L);
        System.out.println(JSON.toJSONString(orderRespDubboResponse));
        DubboResponse<OrderDTO> orderDTODubboResponse = orderQueryService.queryById(108143L);
        System.out.println(JSON.toJSONString(orderDTODubboResponse));
    }


    @Test
    void queryByIdsTest() {
        ArrayList<Long> ids = Lists.newArrayList(108143L, 108142L);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryByIds(ids);
        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryByIds(ids);
        System.out.println(JSON.toJSONString(response));

        Assertions.assertEquals(JSON.toJSONString(response), JSON.toJSONString(listDubboResponse));
    }

    @Test
    void queryOrderListTest() {
        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setBatchSize(10);
        DubboResponse<List<OrderResp>> response = orderQueryProvider.queryOrderList(orderQueryReq);

        DubboResponse<List<OrderDTO>> listDubboResponse = orderQueryService.queryOrderList(orderQueryReq);

        System.out.println(JSON.toJSONString(response));
        Assertions.assertEquals(JSON.toJSONString(response), JSON.toJSONString(listDubboResponse));
    }

    @Test
    void queryOrderOmsPageTest() {
        OrderOmsQueryReq orderOmsQueryReq = new OrderOmsQueryReq();
        orderOmsQueryReq.setPageNum(1);
        orderOmsQueryReq.setPageSize(10);


        RpcContext.getContext().setAttachment(org.apache.dubbo.common.constants.CommonConstants.REMOTE_APPLICATION_KEY, "cosfo-oms");
        DubboResponse<PageInfo<OrderResp>> pageInfoDubboResponse = orderQueryProvider.queryOmsOrderPage(orderOmsQueryReq);

        DubboResponse<PageInfo<OrderDTO>> pageInfoDubboResponse1 = orderQueryService.queryOmsOrderPage(orderOmsQueryReq);

        System.out.println(JSON.toJSONString(pageInfoDubboResponse));

        Assertions.assertEquals(JSON.toJSONString(pageInfoDubboResponse), JSON.toJSONString(pageInfoDubboResponse1));
    }


    @Test
    void updateDeliveryTime() {
        SmsReq req = new SmsReq();
        req.setPhone("15671561596");
        req.setArgs(Arrays.asList("OR171195164508539", "2024-04-01", "2024-04-03"));
        req.setSceneId(7L);
        smsSenderProvider.sendSms(req);
    }

    @Test
    void updateDeliveryTimeOld() {
        OrderUpdateDelivertDateReq req = new OrderUpdateDelivertDateReq();
        req.setOrderNoList(Lists.newArrayList("OR171196045817367", "OR171196052437482"));
        req.setDeliveryDate(LocalDate.now().plusDays(4));
        DubboResponse<Integer> response = orderMutateService.updateOrderDeliveryTime(req);
        System.out.println(response.getData());

        try {
            Thread.sleep(10000000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    void updateDeliveryTimeNew() {
        OrderUpdateDelivertDateReq req = new OrderUpdateDelivertDateReq();
        req.setOrderNoList(Lists.newArrayList("OR171196045817367", "OR171196052437482"));
        req.setDeliveryDate(LocalDate.now().plusDays(4));
        DubboResponse<Integer> response = orderCommandProvider.updateOrderDeliveryTime(req);
        System.out.println(response.getData());

        try {
            Thread.sleep(10000000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
