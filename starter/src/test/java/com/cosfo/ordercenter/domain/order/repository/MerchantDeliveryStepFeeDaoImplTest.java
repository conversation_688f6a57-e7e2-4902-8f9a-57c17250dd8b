package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.dao.dao.MerchantDeliveryStepFeeDao;
import com.cosfo.ordercenter.dao.model.param.QueryDeliveryStepFeeParam;
import com.cosfo.ordercenter.dao.model.po.MerchantDeliveryStepFee;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@SpringBootTest
class MerchantDeliveryStepFeeDaoImplTest {

    @Resource
    private MerchantDeliveryStepFeeDao merchantDeliveryStepFeeDao;

    @Test
    void testlist() {
        QueryDeliveryStepFeeParam param = new QueryDeliveryStepFeeParam();
        param.setTenantId(2L);
        param.setRuleIds(Arrays.asList(1352L));
        List<MerchantDeliveryStepFee> list = merchantDeliveryStepFeeDao.listStepDesc(param);
        System.err.println(list);
    }
}