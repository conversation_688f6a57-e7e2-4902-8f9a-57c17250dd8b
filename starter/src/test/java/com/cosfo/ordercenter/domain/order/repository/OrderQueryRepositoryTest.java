package com.cosfo.ordercenter.domain.order.repository;

import com.cosfo.ordercenter.domain.order.entity.*;
import com.cosfo.ordercenter.domain.order.param.query.*;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class OrderQueryRepositoryTest {

    @Resource
    private OrderQueryRepository orderQueryRepository;

    @Test
    void sumOrderTotalPrice() {
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        List<Long> storeIds = Lists.newArrayList();
        BigDecimal bigDecimal = orderQueryRepository.sumOrderTotalPrice(startTime, endTime, 2L, storeIds);
        System.out.println(bigDecimal);
        Assertions.assertNotNull(bigDecimal);
    }

    @Test
    void countPayOrderQuantity() {
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        Long tenantId = 2L;
        List<Long> storeIds = Lists.newArrayList();
        Integer quantity = orderQueryRepository.countPayOrderQuantity(startTime, endTime, tenantId, storeIds);
        System.out.println(quantity);
        Assertions.assertNotNull(quantity);
    }

    @Test
    void countPayOrderStoreQuantity() {

        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        Long tenantId = 2L;
        List<Long> storeIds = Lists.newArrayList();
        Integer quantity = orderQueryRepository.countPayOrderStoreQuantity(startTime, endTime, tenantId, storeIds);
        System.out.println(quantity);
        Assertions.assertNotNull(quantity);
    }

    @Test
    void getWaitDeliveryNum() {

        Long tenantId = 2L;
        Integer quantity = orderQueryRepository.getWaitDeliveryNum(tenantId);
        System.out.println(quantity);
        Assertions.assertNotNull(quantity);
    }

    @Test
    void querySkuSaleQuantity() {
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        Long tenantId = 2L;
        List<Long> skuIds = Lists.newArrayList(24441L);
        List<OrderSkuQuantityEntity> quantity = orderQueryRepository.querySkuSaleQuantity(skuIds, tenantId, startTime, endTime);
        System.out.println(quantity);
        Assertions.assertNotNull(quantity);
    }

    @Test
    void querySkuSaleWithStoreNoQuantity() {
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        Long tenantId = 2L;
        List<Long> skuIds = Lists.newArrayList(24441L);
        List<OrderSkuQuantityEntity> orderSkuQuantityEntities = orderQueryRepository.querySkuSaleWithStoreNoQuantity(skuIds, tenantId, startTime, endTime);
        System.out.println(orderSkuQuantityEntities);
        Assertions.assertNotNull(orderSkuQuantityEntities);
    }

    @Test
    void querySkuSaleWithCityQuantity() {
        LocalDateTime startTime = LocalDateTime.now().minusDays(30);
        LocalDateTime endTime = LocalDateTime.now();
        Long tenantId = 2L;
        List<Long> skuIds = Lists.newArrayList(24441L);
        List<OrderSkuQuantityEntity> orderSkuQuantityEntities = orderQueryRepository.querySkuSaleWithCityQuantity(skuIds, tenantId, startTime, endTime);
        System.out.println(orderSkuQuantityEntities);
        Assertions.assertNotNull(orderSkuQuantityEntities);
    }

    @Test
    void queryOrderDetail() {
        List<Long> orderIds = Lists.newArrayList(110841L);
        List<OrderDetailEntity> orderDetailEntities = orderQueryRepository.queryOrderDetail(orderIds, 2L);
        System.out.println(orderDetailEntities);
        Assertions.assertNotNull(orderDetailEntities);
    }

    @Test
    void queryPage() {
        OrderQueryParam orderQueryReq = new OrderQueryParam();
        orderQueryReq.setPageNum(1);
        orderQueryReq.setPageSize(10);
        PageInfo<OrderEntity> orderEntityPageInfo = orderQueryRepository.queryPage(orderQueryReq);
        System.out.println(orderEntityPageInfo);
        Assertions.assertNotNull(orderEntityPageInfo);

    }

    @Test
    void testQueryPage() {
        OrderOmsQueryParam orderQueryReq = new OrderOmsQueryParam();
        orderQueryReq.setPageNum(1);
        orderQueryReq.setPageSize(10);
        PageInfo<OrderEntity> orderEntityPageInfo = orderQueryRepository.queryPage(orderQueryReq);
        System.out.println(orderEntityPageInfo);
        Assertions.assertNotNull(orderEntityPageInfo);
    }

    @Test
    void queryList() {
        OrderQueryParam orderQueryReq = new OrderQueryParam();
        List<OrderEntity> orderEntityList = orderQueryRepository.queryList(orderQueryReq);
        System.out.println(orderEntityList);
        Assertions.assertNotNull(orderEntityList);
    }

    @Test
    void queryNeedDeliveryOrder() {
        OrderDeliveryQueryParam orderDeliveryQueryParam = new OrderDeliveryQueryParam();
        List<String> orderEntityList = orderQueryRepository.queryNeedDeliveryOrder(orderDeliveryQueryParam);
        System.out.println(orderEntityList);
        Assertions.assertNotNull(orderEntityList);
    }

    @Test
    void queryByOrderNos() {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByOrderNos(Lists.newArrayList());
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryByCombineId() {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByCombineId(1L, 2L);
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryByCombineIds() {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByCombineIds(Sets.newHashSet(), 2L);
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void countOrderQuantity() {
        OrderCountQueryParam countQueryParam  = new OrderCountQueryParam();

        Integer quantity = orderQueryRepository.countOrderQuantity(countQueryParam);
        System.out.println(quantity);
        Assertions.assertNotNull(quantity);
    }

    @Test
    void queryNeedAutoFinishedOrder() {
        OrderAutoFinishQueryParam queryParam  = new OrderAutoFinishQueryParam();
        List<OrderEntity> orderEntities = orderQueryRepository.queryNeedAutoFinishedOrder(queryParam);
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryByStoreIdAndDeliveryTime() {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByStoreIdAndDeliveryTime(1L, LocalDateTime.now(), 1);
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryListByCustomerOrderIds() {
        OrderQueryParam orderQueryParam = new OrderQueryParam();
        List<OrderEntity> orderEntities = orderQueryRepository.queryListByCustomerOrderIds(orderQueryParam);
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryOrderItemSaleQuantity() {
        ItemSaleQuantityQueryParam itemSaleQuantityQueryParam = new ItemSaleQuantityQueryParam();
        List<OrderItemSaleQuantityEntity> orderItemSaleQuantityEntities = orderQueryRepository.queryOrderItemSaleQuantity(itemSaleQuantityQueryParam);
        System.out.println(orderItemSaleQuantityEntities);
        Assertions.assertNotNull(orderItemSaleQuantityEntities);
    }

    @Test
    void querySupplierOrderList() {
        SupplierOrderTotalQueryParam totalQueryParam = new SupplierOrderTotalQueryParam();
        List<SupplierOrderEntity> supplierOrderEntities = orderQueryRepository.querySupplierOrderList(totalQueryParam);
        System.out.println(supplierOrderEntities);
        Assertions.assertNotNull(supplierOrderEntities);
    }

    @Test
    void queryById() {
        OrderEntity orderEntity = orderQueryRepository.queryById(1L);
        System.out.println(orderEntity);
        Assertions.assertNotNull(orderEntity);
    }

    @Test
    void queryByNo() {
        OrderEntity orderEntity = orderQueryRepository.queryByNo("123");
        System.out.println(orderEntity);
        Assertions.assertNotNull(orderEntity);
    }

    @Test
    void queryByIds() {
        List<OrderEntity> orderEntities = orderQueryRepository.queryByIds(Lists.newArrayList());
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }

    @Test
    void queryByNos() {

        List<OrderEntity> orderEntities = orderQueryRepository.queryByNos(Lists.newArrayList());
        System.out.println(orderEntities);
        Assertions.assertNotNull(orderEntities);
    }
}